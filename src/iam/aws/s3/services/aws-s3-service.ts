import { Injectable, Logger, OnModuleInit } from "@nestjs/common";
//import { S3 } from "aws-sdk";
import { ImageFile } from "../models/image-file";
import { AwsS3File } from "../models/aws-s3-file";
import { WPError } from "../../../../commons/error/wp.error";
import { WP_ERROR_IAM_USER_IMAGE_UPLOAD } from "../../../../commons/error/error.codes";
import { ConfigService } from "@nestjs/config";
import { BaseService } from "../../../../commons/services/base.service";
import { PutObjectCommand, PutObjectCommandInput, PutObjectCommandOutput, PutObjectRequest, S3Client } from "@aws-sdk/client-s3";

@Injectable()
export class AwsS3Service extends BaseService {
    private readonly logger = new Logger(AwsS3Service.name);

    private s3Client: S3Client;

    constructor(private readonly confService: ConfigService) {
        super(confService);
        this.s3Client = new S3Client();
    }

    async uploadImageFile(imageFile: ImageFile): Promise<AwsS3File> {
        const params: PutObjectCommandInput = {
            Bucket: imageFile.bucket,
            Body: imageFile.buffer,
            Key: imageFile.name,
        };

        const command: PutObjectCommand = new PutObjectCommand(params);

        this.logger.debug(`uploading file: Bucket${params.Bucket}, Key:${params.Key}, Size:${imageFile.buffer.byteLength}`);

        try {
            const uploadResult: PutObjectCommandOutput = await this.s3Client.send(command);

            // The PutObjectCommand doesn't return Location or Key like v2 used to.
            // hence we need to construct the fileUrl from the bucket and key.
            // Note that we have domain mapping for https://${params.Bucket} in the cloud flare
            const fileUrl = `https://${params.Bucket}/${params.Key}`;

            const awsS3File: AwsS3File = {
                fileName: imageFile.name,
                fileUrl,
                key: params.Key!,
            };

            return awsS3File;
        } catch (error) {
            this.logger.error(`Could not upload userprofile image:${imageFile.name}`, error);
            throw new WPError(WP_ERROR_IAM_USER_IMAGE_UPLOAD, `image:${imageFile.name}`, error.stack, "500");
        }
    }
}
