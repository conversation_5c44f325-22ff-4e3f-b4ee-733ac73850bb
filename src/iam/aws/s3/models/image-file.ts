export class ImageFile {
    bucket: string;
    name: string;
    fieldname?: string;
    originalname?: string;
    encoding?: string;
    mimetype?: string;
    buffer?: any;
    size?: number;

    constructor(bucket: string, name: string, fieldname: string, originalname: string, encoding: string, mimetype: string, buffer: any, size: number) {
        this.bucket = bucket;
        this.name = name;
        this.fieldname = fieldname;
        this.originalname = originalname;
        this.encoding = encoding;
        this.mimetype = mimetype;
        this.buffer = buffer;
        this.size = size;
    }
}
