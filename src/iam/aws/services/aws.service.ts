import { Injectable, Logger } from "@nestjs/common";
import { AwsS3Service } from "../s3/services/aws-s3-service";
import { ImageFile } from "../s3/models/image-file";
import { AwsS3File } from "../s3/models/aws-s3-file";
import { AwsCognitoService } from "../iam/services/aws-cognito-service";
import { IAMUserAttribute } from "../iam/models/iam-user-attribute";
import { BaseService } from "../../../commons/services/base.service";
import { ConfigService } from "@nestjs/config";
import { CognitoAttributeMapper } from "../iam/cognito-attribute-mapper";

export class AttributeValidationError {
    public static readonly ATTR_ERROR_REQUIRED: string = "REQUIRED";
    public static readonly ATTR_ERROR_INVALID_VALUE: string = "INVALID_VALUE";

    constructor(name: string, errorCode: string, error?: string) {
        this.name = name;
        this.errorCode = errorCode;
        this.error = error;
    }

    name: string;
    errorCode?: string;
    error?: string;
}

@Injectable()
export class AwsService extends BaseService {
    private readonly logger = new Logger(AwsService.name);

    constructor(private readonly confService: ConfigService, private readonly awsS3Service: AwsS3Service, private readonly awsCognitoService: AwsCognitoService) {
        super(confService);
    }

    async uploadImageFile(imageFile: ImageFile): Promise<AwsS3File> {
        let fileStore: AwsS3File = await this.awsS3Service.uploadImageFile(imageFile);
        return fileStore;
    }

    async updateCognitoAttributes(user_id: string, attributes: IAMUserAttribute[]) /* : Promise<AttributeValidationError[]> */ {
        CognitoAttributeMapper.validateUserAttributes(user_id, attributes);

        await this.awsCognitoService.updateCognitoAttributes(user_id, attributes);
    }

    async resetPassword(username: string, token: string, previousPass: string, newPass: string) {
        await this.awsCognitoService.resetPassword(username, token, previousPass, newPass);
    }
    async regGood(username: string, utmSource?: string, refCode?: string) {
        await this.updateCognitoAttributes(username, [
            new IAMUserAttribute("custom:nw", "nw", "0"),
            new IAMUserAttribute("custom:utm", "utm", utmSource!),
            new IAMUserAttribute("custom:referrer_code", "ref", refCode!),
        ]);
        // { Name: "custom:nw", mappedName: "custom:nw", Value: '0' },{ Name: "custom:utm", mappedName: "custom:utm", Value: utmSource }]);
    }

    async deleteUser(username: string) {
        await this.awsCognitoService.deleteUser(username);
    }
    async disableUser(username: string) {
        await this.awsCognitoService.disableUser(username);
    }

    async deleteUserAdmin(username: string) {
        await this.awsCognitoService.deleteUserAdmin(username);
    }
}
