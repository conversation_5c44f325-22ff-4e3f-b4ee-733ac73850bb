import { Modu<PERSON>, forwardRef } from "@nestjs/common";
import { AwsService } from "./services/aws.service";
import { AwsS3Service } from "./s3/services/aws-s3-service";
import { AwsCognitoService } from "./iam/services/aws-cognito-service";
import { AwsCognitoDataService } from "./iam/services/aws-cognito-data.service";
import { AwsCognitoAuthTokenService } from "./iam/services/aws-cognito-auth-token-service";
import { SimpleKmsService } from "./kms/services/simple-kms.service";
import { AwsKmsService } from "./kms/services/aws-kms.sevice";
import { AwsSecretsManagerService } from "./secrets-manager/services/secrets-manager.service";
import { RedisModule } from "../../redis/redis.module";

@Module({
  imports: [
    RedisModule
  ],
  controllers: [],
  providers: [
    AwsService, 
    AwsS3Service, 
    AwsCognitoService, 
    AwsCognitoDataService,
    AwsCognitoAuthTokenService,
    SimpleKmsService,
    AwsKmsService,
    AwsSecretsManagerService
  ],
  exports: [
    AwsService, 
    AwsS3Service, 
    AwsCognitoService,
    AwsCognitoDataService,
    AwsCognitoAuthTokenService,
    SimpleKmsService,
    AwsKmsService,
    AwsSecretsManagerService
  ],
})
export class AwsModule { }
