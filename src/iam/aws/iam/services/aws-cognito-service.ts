import { Injectable, Logger, OnModuleInit } from "@nestjs/common";
import { WP_ERROR_IAM_USER_NOT_DELETED, WP_ERROR_IAM_USER_NOT_FOUND_BY_EMAIL, WP_ERROR_IAM_USER_NOT_FOUND_BY_UUID, WP_ERROR_IAM_USER_NOT_UPDATED } from "../../../../commons/error/error.codes";

import {
    CognitoIdentityProviderClient,
    DeleteUserCommand,
    DeleteUserCommandInput,
    DeleteUserCommandOutput,
    AdminUpdateUserAttributesCommand,
    AdminUpdateUserAttributesCommandInput,
    AdminUpdateUserAttributesCommandOutput,
    AdminDisableUserCommand,
    AdminDisableUserCommandInput,
    ChangePasswordCommand,
    ChangePasswordCommandInput,
    ChangePasswordCommandOutput,
    AdminDeleteUserCommandInput,
    AdminDeleteUserCommand,
    AdminDeleteUserCommandOutput,
    CognitoIdentityProviderServiceException,
    UserNotFoundException,
    AdminCreateUserCommandInput,
    AdminCreateUserCommand,
    AdminCreateUserCommandOutput,
    SignUpCommand,
    SignUpCommandInput,
    SignUpCommandOutput,
    ListUsersCommand,
    ListUsersCommandInput,
    ListUsersCommandOutput,
    AdminAddUserToGroupCommand,
    AdminAddUserToGroupCommandInput,
    UserType,
    AdminSetUserPasswordCommand,
    AdminSetUserPasswordCommandInput,
    AdminSetUserPasswordCommandOutput
} from "@aws-sdk/client-cognito-identity-provider";
import { IAMUserAttribute } from "../models/iam-user-attribute";
import * as util from "util";
import { WPError } from '../../../../commons/error/wp.error';
import { BaseService } from "../../../../commons/services/base.service";
import { ConfigService } from "@nestjs/config";
import { AWS_ACCESS_KEY_ID_PARAM_NAME, AWS_COGNITO_USER_POOL_ID_PARAM_NAME, AWS_REGION_PARAM_NAME, AWS_SECRET_ACCESS_KEY_PARAM_NAME, AWS_COGNITO_CLIENT_ID_PARAM_NAME } from "../../../../utils/confs";    
import { IAMUserCreate } from "../models/iam-user-create";
import { UserContext } from "../../../../commons/auth/user-context";
import { IAMAWSMapper } from "../mapper/iam-aws.mapper";
import { CognitoUserErrorHandler } from "../cognito-user-error.handler";
import { IAMUser } from "../models/iam-user";
@Injectable()
export class AwsCognitoService extends BaseService implements OnModuleInit {
    protected logger = new Logger(AwsCognitoService.name);

    protected cognitoClient: CognitoIdentityProviderClient;
    protected awsRegion: string;

    constructor(private readonly confService: ConfigService) {
        super(confService);

    }
    async onModuleInit() {
        this.awsRegion = await this.getConfig(AWS_REGION_PARAM_NAME);
        const awsAccessId: string = await this.getConfig(AWS_ACCESS_KEY_ID_PARAM_NAME);
        const awsAccessKey: string = await this.getConfig(AWS_SECRET_ACCESS_KEY_PARAM_NAME);

        // gets the values form the env fine
        this.cognitoClient = new CognitoIdentityProviderClient({
            // endpoint:process.env.AWS_COGNITO_AUTH_DOMAIN,
            region: this.awsRegion,
            credentials: {
                accessKeyId: awsAccessId,
                secretAccessKey: awsAccessKey,
            },
        });
    }

    /**
     * See [AWS Cognito API details](https://docs.aws.amazon.com/AWSJavaScriptSDK/v3/latest/clients/client-cognito-identity-provider/classes/adminupdateuserattributescommand.html)
     * @param imageFile
     * @returns `Promise<void>`
     */
    async updateCognitoAttributes(user_id: string, attributes: IAMUserAttribute[]): Promise<void> {
        const input: AdminUpdateUserAttributesCommandInput = {
            UserPoolId: process.env.AWS_COGNITO_USER_POOL_ID,
            Username: user_id, //"6c8fdaf9-e874-4af1-8270-acb203c5100f",
            UserAttributes: attributes,
            // UserAttributes: [],
        };

        const command = new AdminUpdateUserAttributesCommand(input);

        this.logger.debug(`updating cognito user attributes: user_id: ${user_id} ${util.inspect(input)}`);

        try {
            const response: AdminUpdateUserAttributesCommandOutput = await this.cognitoClient.send(command);
            this.logger.debug(`The user attributes successfully updated on the cognito.RequestId:${response.$metadata.requestId}`);
        } catch (error) {
            this.logger.debug(error);
            const { requestId, cfId, extendedRequestId } = error.$metadata;
            let errMsg = `Could not update cognito user attributes, RequestId:${requestId}`;
            if (error.name === "NotAuthorizedException") {
                errMsg = `NotAuthorizedException. ${errMsg}`;
            } else if (error.name === "ResourceNotFoundException") {
                // client fault.This exception is thrown when the Amazon Cognito service can't find the requested resource.
                errMsg = `Idebtity pool not found. ${errMsg}`;
            } else if (error.name === "UserNotFoundException") {
                // client fault.This exception is thrown when a user isn't found.
                errMsg = `User not found ${errMsg}`;
            } else if (error.name === "InvalidParameterException") {
                // client fault.This exception is thrown when a user isn't found.
                errMsg = `InvalidParameterException. ${errMsg}. ${error.message}`;
            }
            this.logger.error(errMsg, error);
            throw new WPError(WP_ERROR_IAM_USER_NOT_UPDATED, `${errMsg}`, error.stack, "500");
        }
    }

    async resetPassword(username: string, accessToken: string, previousPass: string, newPass: string) {
        const input: ChangePasswordCommandInput = {
            AccessToken: accessToken,
            PreviousPassword: previousPass,
            ProposedPassword: newPass,
        };
        // let input: ForgotPasswordCommandInput = {
        //     Username: "6c8fdaf9-e874-4af1-8270-acb203c5100f",
        //     //ClientId: "3c4p4te2qf4gm98fc6r0at08cg",
        //     //SecretHash:"pa90h0g2v6vv60kk1a44uougfl3e3a74t1isfb7cu6gfh7roj6b"
        //     ClientId: "umlu7il6sho5cqf87dldo9eai",
        // };
        // let command = new ForgotPasswordCommand(input);
        const command = new ChangePasswordCommand(input);

        this.logger.debug(`reseting password for user: ${util.inspect(username)}`);
        try {
            const response: ChangePasswordCommandOutput = await this.cognitoClient.send(command);
            this.logger.debug(`User password reset input request:${response.$metadata.requestId}`);
        } catch (error) {
            const { requestId, cfId, extendedRequestId } = error.$metadata;
            const errMsg = `Could not reset  user pass, RequestId:${requestId}`;
            this.logger.error(errMsg, error);
            throw new WPError(WP_ERROR_IAM_USER_NOT_UPDATED, `${errMsg}`, error.stack, "500");
        }
    }
    async disableUser(username: string) {
        const input: AdminDisableUserCommandInput = {
            UserPoolId: process.env.AWS_COGNITO_USER_POOL_ID,
            Username: username,
        };
        const command = new AdminDisableUserCommand(input);

        this.logger.debug(`disabling user: ${util.inspect(username)}`);
        try {
            const response: DeleteUserCommandOutput = await this.cognitoClient.send(command);
            this.logger.debug(`The user attributes successfully updated on the cognito.RequestId:${response.$metadata.requestId}`);
        } catch (error) {
            const { requestId, cfId, extendedRequestId } = error.$metadata;
            const errMsg = `Could not update cognito user attributes, RequestId:${requestId}`;
            this.logger.error(errMsg, error);
            throw new WPError(WP_ERROR_IAM_USER_NOT_UPDATED, `${errMsg}`, error.stack, "500");
        }
    }

    async deleteUser(username: string) {
        const input: AdminDeleteUserCommandInput = {
            UserPoolId: process.env.AWS_COGNITO_USER_POOL_ID,
            Username: username,
        };
        const command = new AdminDeleteUserCommand(input);

        this.logger.debug(`deleting user: ${util.inspect(username)}`);
        try {
            const response: DeleteUserCommandOutput = await this.cognitoClient.send(command);
            this.logger.debug(`The user attributes successfully updated on the cognito.RequestId:${response.$metadata.requestId}`);
        } catch (error) {
            const { requestId, cfId, extendedRequestId } = error.$metadata;
            const errMsg = `Could not DELETE cognito user , RequestId:${requestId}`;

            this.logger.error(errMsg, error);
            throw new WPError(WP_ERROR_IAM_USER_NOT_UPDATED, `${errMsg}`, error.stack, "500");
        }
    }

    async deleteUserAdmin(username: string) {

        let input: AdminDeleteUserCommandInput = {
            Username: username,
            UserPoolId: await this.getConfig(AWS_COGNITO_USER_POOL_ID_PARAM_NAME)
        };
        let command = new AdminDeleteUserCommand(input);

        this.logger.debug(`deleting user: ${util.inspect(username)}`);
        try {
            let response: AdminDeleteUserCommandOutput = await this.cognitoClient.send(command);
            this.logger.debug(`The user has been successfully deleted on the cognito.RequestId:${response.$metadata.requestId}`);
        } catch (error) {
            let cognitoError: CognitoIdentityProviderServiceException = error as CognitoIdentityProviderServiceException

            const { requestId, cfId, extendedRequestId } = cognitoError.$metadata;
            let errMsg: string = `Could not delete cognito user:${username}.${error.message}`;
            this.logger.error(errMsg, error);
            if (error instanceof UserNotFoundException) {
                throw new WPError(WP_ERROR_IAM_USER_NOT_FOUND_BY_UUID, `${errMsg}`, error, "500");
            }
            else {
                throw new WPError(WP_ERROR_IAM_USER_NOT_DELETED, `${errMsg}`, error, "500");
            }
        }
    }

    

    async createUserAsAdmin(userCreate: IAMUserCreate, userContext: UserContext):Promise<IAMUser> {

        const userPoolId: string = await this.getConfig(AWS_COGNITO_USER_POOL_ID_PARAM_NAME)

        const input: AdminCreateUserCommandInput = IAMAWSMapper.toAdminCreateUserCommandInput(userPoolId, userCreate, userContext);

        const command: AdminCreateUserCommand = new AdminCreateUserCommand(input);
        try {
            const response: AdminCreateUserCommandOutput = await this.cognitoClient.send(command);
            const user = response.User;
            return new IAMUser(
                user?.Username!,
                userCreate.userId,
                userCreate.name,
                userCreate.phoneNumber,
                userCreate.familyName,
                userCreate.nickname,
                userCreate.email,
                userCreate.gender,
                userCreate.address,
                userCreate.ref,
                userCreate.groups,
                user?.UserCreateDate!,
                user?.UserLastModifiedDate!,
                user?.Enabled!,
                user?.UserStatus!
            );
        } catch (error) {
            const wpError: WPError = new CognitoUserErrorHandler(error).handle();
            throw wpError;
        }
    }

    /**
     * Creates a regular user in AWS Cognito using SignUpCommand.
     * This differs from createUserAsAdmin as it uses the public API that doesn't require admin privileges.
     * 
     * @param userCreate User information to create
     * @param userContext Context of the user performing the operation
     * @returns Promise<IAMUser> The created user information
     */
    async createUser(userCreate: IAMUserCreate, userContext: UserContext, password: string): Promise<IAMUser> {
        try {
            // Get the client ID from configuration
            const clientId: string = await this.getConfig(AWS_COGNITO_CLIENT_ID_PARAM_NAME);
            
            // Create the user attributes array
            const userAttributes = IAMAWSMapper.toUserAttributes(userCreate);
            
            // Create the input for SignUpCommand
            const input: SignUpCommandInput = {
                ClientId: clientId,
                Username: userCreate.email,
                Password: password,
                UserAttributes: userAttributes
            };
            
            this.logger.debug(`creating regular user: ${userCreate.email}`);
            
            // Execute the SignUpCommand
            const command = new SignUpCommand(input);
            const response: SignUpCommandOutput = await this.cognitoClient.send(command);
            
            // Return the created user info
            return new IAMUser(
                userCreate.email,
                userCreate.userId,
                userCreate.name,
                userCreate.phoneNumber,
                userCreate.familyName,
                userCreate.nickname,
                userCreate.email,
                userCreate.gender,
                userCreate.address,
                userCreate.ref,
                userCreate.groups,
                new Date(), // UserCreateDate - approximation since not returned in SignUp response
                new Date(), // UserLastModifiedDate - approximation since not returned in SignUp response
                true, // Enabled - assuming enabled by default
                response.UserConfirmed ? "CONFIRMED" : "UNCONFIRMED" // UserStatus
            );
        } catch (error) {
            const wpError: WPError = new CognitoUserErrorHandler(error).handle();
            throw wpError;
        }
    }

    /**
     * Finds a user by email in the AWS Cognito user pool
     * 
     * @param email Email address to search for
     * @returns Promise<UserType | undefined> The user if found, undefined otherwise
     */
    async findUserByEmail(email: string): Promise<UserType | undefined> {
        try {
            const userPoolId = await this.getConfig(AWS_COGNITO_USER_POOL_ID_PARAM_NAME);
            
            const input: ListUsersCommandInput = {
                UserPoolId: userPoolId as string,
                Filter: `email = "${email}"`,
                Limit: 1
            };
            
            this.logger.debug(`Finding user by email: ${email}`);
            
            const command = new ListUsersCommand(input);
            const response: ListUsersCommandOutput = await this.cognitoClient.send(command);
            
            return response.Users?.[0];
        } catch (error) {
            this.logger.error(`Could not find user by email: ${email}`, error);
            const wpError: WPError = new CognitoUserErrorHandler(error).handle();
            throw wpError;
        }
    }
    
    /**
     * Adds a user to a specific AWS Cognito user group
     * 
     * @param username Username of the user to add to the group
     * @param groupName Name of the group to add the user to
     * @returns Promise<void>
     */
    async addUserToGroup(username: string, groupName: string): Promise<void> {
        try {
            const userPoolId = await this.getConfig(AWS_COGNITO_USER_POOL_ID_PARAM_NAME);
            
            const input: AdminAddUserToGroupCommandInput = {
                UserPoolId: userPoolId as string,
                Username: username,
                GroupName: groupName
            };
            
            this.logger.debug(`Adding user ${username} to group ${groupName}`);
            
            const command = new AdminAddUserToGroupCommand(input);
            await this.cognitoClient.send(command);
            
            this.logger.debug(`Successfully added user ${username} to group ${groupName}`);
        } catch (error) {
            this.logger.error(`Could not add user ${username} to group ${groupName}`, error);
            const wpError: WPError = new CognitoUserErrorHandler(error).handle();
            throw wpError;
        }
    }
    
    /**
     * Adds a user to multiple AWS Cognito user groups
     * First finds the user by email, then adds them to each specified group
     * 
     * @param email Email of the user to add to groups
     * @param groupNames Array of group names to add the user to
     * @returns Promise<void>
     */
    async addUserToGroups(email: string, groupNames: string[]): Promise<void> {
        try {
            // Find the user by email first
            const user = await this.findUserByEmail(email);
            
            if (!user) {
                throw new WPError(
                    WP_ERROR_IAM_USER_NOT_FOUND_BY_EMAIL, 
                    `User with email ${email} not found in Cognito`
                );
            }
            
            // Add the user to each group
            for (const groupName of groupNames) {
                await this.addUserToGroup(user.Username!, groupName);
            }
            
            this.logger.debug(`Successfully added user ${email} to groups: ${groupNames.join(', ')}`);
        } catch (error) {
            if (error instanceof WPError) {
                throw error;
            }
            
            this.logger.error(`Could not add user ${email} to groups: ${groupNames.join(', ')}`, error);
            const wpError: WPError = new CognitoUserErrorHandler(error).handle();
            throw wpError;
        }
    }

    async adminSetUserPassword(username: string, newPassword: string): Promise<void> {
        const userPoolId: string = await this.getConfig(AWS_COGNITO_USER_POOL_ID_PARAM_NAME);

        const input: AdminSetUserPasswordCommandInput = {
            UserPoolId: userPoolId,
            Username: username,
            Password: newPassword,
            Permanent: true, // Sets the password permanently, user does not need to change it on next login
        };

        const command = new AdminSetUserPasswordCommand(input);
        this.logger.debug(`Admin attempting to set password for user: ${username}`);

        try {
            const response: AdminSetUserPasswordCommandOutput = await this.cognitoClient.send(command);
            this.logger.debug(`Admin successfully set password for user: ${username}. RequestId: ${response.$metadata.requestId}`);
        } catch (error) {
            this.logger.error(`Failed to admin set password for user ${username}: ${error.message}`, error.stack);
            // Handle specific Cognito errors if needed, or rethrow a generic one
            const cognitoError = new CognitoUserErrorHandler(error).handle();
            throw cognitoError;
        }
    }
}
