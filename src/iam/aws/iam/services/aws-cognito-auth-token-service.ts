import {
    CognitoIdentityProviderClient,
    InitiateAuthCommand,
    InitiateAuthCommandInput,
    InitiateAuthCommandOutput,
    UnauthorizedException,
} from '@aws-sdk/client-cognito-identity-provider';
import { CognitoJwtVerifier } from "aws-jwt-verify";
import { CognitoJwtVerifierSingleUserPool } from 'aws-jwt-verify/cognito-verifier';
import { CognitoIdOrAccessTokenPayload, CognitoJwtPayload } from 'aws-jwt-verify/jwt-model';
import * as crypto from 'crypto';
import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { TokenDecoded } from '../models/auth/token-decoded';
import { BaseService } from '../../../../commons/services/base.service';
import { ConfigService } from '@nestjs/config';
import { AwsCognitoService } from './aws-cognito-service';
import { AWS_COGNITO_CLIENT_ID_PARAM_NAME, AWS_COGNITO_CLIENT_SECRET_PARAM_NAME, AWS_COGNITO_USER_POOL_ID_PARAM_NAME } from '../../../../utils/confs';
import { CognitoAuthTokenErrorHandler } from '../handlers/cognito-auth-token-error.handler';
import { WPError } from '../../../../commons/error/wp.error';
import { WP_ERROR_USER_COGNITO_AUTH_USER_INVALID_TOKEN } from '../../../../commons/error/error.codes';

@Injectable()
export class AwsCognitoAuthTokenService extends AwsCognitoService implements OnModuleInit {

    protected logger = new Logger(AwsCognitoAuthTokenService.name);

    private verifier: CognitoJwtVerifierSingleUserPool<{ userPoolId: string, clientId: string, tokenUse: "access" | "id" }>;

    protected awsRegion: string;
    private cognitoUserPoolId: string;
    private clientId: string;
    private clientSecret: string;

    constructor(private readonly configService: ConfigService) {
        super(configService);
    }

    async onModuleInit() {
        await super.onModuleInit();
        this.cognitoUserPoolId = await this.getConfig(AWS_COGNITO_USER_POOL_ID_PARAM_NAME);
        this.clientId = await this.getConfig(AWS_COGNITO_CLIENT_ID_PARAM_NAME);
        this.clientSecret = await this.getConfig(AWS_COGNITO_CLIENT_SECRET_PARAM_NAME);
        // gets the values form the env fine
        this.verifier = CognitoJwtVerifier.create({
            userPoolId: process.env.AWS_COGNITO_USER_POOL_ID!,
            clientId: process.env.AWS_COGNITO_CLIENT_ID!,
            tokenUse: "access", // or "id" if you're verifying an ID token
        });
    }

    /**
     * 
     * @param email 
     * @param password 
     * @returns InitiateAuthCommandOutput 
     *   {
            '$metadata': {
                httpStatusCode: 200,
                requestId: '4ee656a1-c3b6-46ee-844e-84ad9ab6a9fc',
                extendedRequestId: undefined,
                cfId: undefined,
                attempts: 1,
                totalRetryDelay: 0
            },
            AuthenticationResult: {
                AccessToken: 'xxxxx',
                ExpiresIn: 86400,
                IdToken: 'yyyyy',
                RefreshToken: 'zzzzzz',
                TokenType: 'Bearer'
            },
            ChallengeParameters: {}
            }

     */
    public async signInUser(email: string, password: string): Promise<InitiateAuthCommandOutput> {
        // Calculate the secret hash
        const secretHash = this.calculateSecretHash(email, this.clientId, this.clientSecret);

        const params: InitiateAuthCommandInput = {
            AuthFlow: 'USER_PASSWORD_AUTH', // or 'ADMIN_USER_PASSWORD_AUTH' if using admin flows
            ClientId: this.clientId,
            AuthParameters: {
                USERNAME: email,
                PASSWORD: password,
                SECRET_HASH: secretHash,
            },
        };

        try {
            const command = new InitiateAuthCommand(params);
            const response: InitiateAuthCommandOutput = await this.cognitoClient.send(command);
            return response;
        } catch (error) {
            const wpError: WPError = new CognitoAuthTokenErrorHandler(error).handle();
            throw wpError;
        }
    }

    /**
     * 
     * @param username 
     * @param refreshToken 
     * @returns InitiateAuthCommandOutput
     * 
     * {
        '$metadata': {
            httpStatusCode: 200,
            requestId: '55d089f6-cad7-400e-aa0c-eb006efecdfc',
            extendedRequestId: undefined,
            cfId: undefined,
            attempts: 1,
            totalRetryDelay: 0
        },
        AuthenticationResult: {
            AccessToken: 'xxxxx',
            ExpiresIn: 86400,
            IdToken: 'yyyyy',
            TokenType: 'Bearer'
        },
        ChallengeParameters: {}
        }
     */
    public async refreshTokens(username: string, refreshToken: string): Promise<InitiateAuthCommandOutput> {

        // If the client is configured with a secret, calculate the SECRET_HASH
        const secretHash = this.calculateSecretHash(username, this.clientId, this.clientSecret);

        const params: InitiateAuthCommandInput = {
            AuthFlow: 'REFRESH_TOKEN_AUTH',
            ClientId: this.clientId,
            AuthParameters: {
                USERNAME: username,
                REFRESH_TOKEN: refreshToken,
                SECRET_HASH: secretHash,
            },
        };

        try {
            const command = new InitiateAuthCommand(params);
            const response = await this.cognitoClient.send(command);
            return response;
        } catch (error) {
            const wpError: WPError = new CognitoAuthTokenErrorHandler(error).handle();
            throw wpError;
        }
    }

    /**
     * 
     * @param accessToken 
     * @returns decoded payload
     * 
     * {
            sub: '8afa1bda-586f-4f5c-a65c-00ca9e164125',
            'cognito:groups': [ 'admin', 'ew_user' ],
            iss: 'https://cognito-idp.eu-central-1.amazonaws.com/eu-central-1_eInVWpQWI',
            client_id: '3c4p4te2qf4gm98fc6r0at08cg',
            origin_jti: 'b3b8894f-f2eb-4577-9114-d0f6be21a20a',
            event_id: '01e7c5be-9bfe-4e11-9c6e-f3f605dbf1dc',
            token_use: 'access',
            scope: 'aws.cognito.signin.user.admin',
            auth_time: 1740129402,
            exp: 1740215801,
            iat: 1740129402,
            jti: 'bf8c7d67-0c9e-4c49-826a-8b12eeb0a6fa',
            username: '8afa1bda-586f-4f5c-a65c-00ca9e164125'
        }
     */
    public async decodeAndVerifyAccessToken(accessToken: string): Promise<TokenDecoded> {
        try {
            // 2) Verify returns the token's payload if valid
            //const payload:CognitoIdOrAccessTokenPayload<{ [key: string]: any }, { [key: string]: any }> = await this.verifier.verify(accessToken);
            const payload: any = await this.verifier.verify(accessToken);
            // `payload` is now the decoded claims (username, sub, etc.)

            const tokenDecoded: TokenDecoded = this.extractTokenDecoded(payload);

            return tokenDecoded;
        } catch (err) {
            this.logger.error("Token is invalid!", err);
            throw new WPError(WP_ERROR_USER_COGNITO_AUTH_USER_INVALID_TOKEN, err.message, err, WP_ERROR_USER_COGNITO_AUTH_USER_INVALID_TOKEN.headerStatusCode + "");
        }
    }

    /**
     *  Extracts the token payload into a TokenDecoded object
     * @param payload 
     * @returns TokenDecoded
     */
    public extractTokenDecoded(payload: any): TokenDecoded {
        const tokenDecoded: TokenDecoded = new TokenDecoded(
            payload.sub,
            payload["cognito:groups"],
            payload.iss,
            payload.client_id,
            payload.origin_jti,
            payload.event_id,
            payload.token_use,
            payload.scope,
            payload.auth_time,
            payload.exp,
            payload.iat,
            payload.jti,
            payload.username
        );
        return tokenDecoded;
    }

    public calculateSecretHash(username: string, clientId: string, clientSecret: string): string {
        return crypto.createHmac('SHA256', clientSecret).update(username + clientId)
            .digest('base64');
    }
}