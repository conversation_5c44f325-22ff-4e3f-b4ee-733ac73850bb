import { Injectable, Logger, OnModuleInit } from "@nestjs/common";
import { BaseService } from "../../../../commons/services/base.service";
import { ConfigService } from "@nestjs/config";
import { CloudWatchClient, GetMetricStatisticsCommand, Statistic } from "@aws-sdk/client-cloudwatch";
import { CognitoIdentityProviderClient, ListUserPoolClientsCommand } from "@aws-sdk/client-cognito-identity-provider";
import { AWS_ACCESS_KEY_ID_PARAM_NAME, AWS_COGNITO_USER_POOL_ID_PARAM_NAME, AWS_REGION_PARAM_NAME, AWS_SECRET_ACCESS_KEY_PARAM_NAME } from "../../../../utils/confs";
import { AwsCognitoService } from "./aws-cognito-service";

@Injectable()
export class AwsCognitoDataService extends AwsCognitoService implements OnModuleInit {

    private cloudWatchClient: CloudWatchClient;
    private cognitoUserPoolId: string;
    private userPoolClients: string[];
    protected logger = new Logger(AwsCognitoDataService.name);

    constructor(private readonly configService: ConfigService) {
        super(configService);
    }
    async onModuleInit() {

        await super.onModuleInit();
        this.cognitoUserPoolId = await this.getConfig(AWS_COGNITO_USER_POOL_ID_PARAM_NAME);
        
        this.cloudWatchClient = new CloudWatchClient({ region: this.awsRegion });
        this.userPoolClients = await this.getUserPoolClients();

    }

    async getDailySignupCounts(from: string, to: string): Promise<any> {
        return await this.getDailyCounts(from, to, "SignUpSuccesses");
    }

    async getDailySigninCounts(from: string, to: string): Promise<any> {
        return await this.getDailyCounts(from, to, "SignInSuccesses");
    }

    private async getUserPoolClients(): Promise<string[]> {
        const command = new ListUserPoolClientsCommand({ UserPoolId: this.cognitoUserPoolId });
        const response = await this.cognitoClient.send(command);
        const clients: string[] = response.UserPoolClients?.map(client => client.ClientId).filter((id): id is string => !!id) || [];
        clients.push("Admin"); // Add Admin client ID
        return clients;
    }

    private async getDailyCounts(from: string, to: string, metricName: string): Promise<any> {
        const startTime = new Date(from);
        const endTime = new Date(to);

        const promises = this.userPoolClients.map(async (clientId) => {
            const params = {
                Namespace: "AWS/Cognito",
                MetricName: metricName,
                Dimensions: [
                    { Name: "UserPool", Value: this.cognitoUserPoolId },
                    { Name: "UserPoolClient", Value: clientId }
                ],
                StartTime: startTime,
                EndTime: endTime,
                Period: 86400, // 1 day
                Statistics: [Statistic.Sum],
            };

            const command = new GetMetricStatisticsCommand(params);
            const result = await this.cloudWatchClient.send(command);
            return result.Datapoints?.map((data) => ({
                date: data.Timestamp?.toISOString().split('T')[0], // Extract only the date
                count: data.Sum,
            })) || [];
        });

        const results = await Promise.all(promises);

        // Aggregate totals per day
        const dailyTotals: Record<string, number> = {};
        results.flat().forEach(({ date, count }) => {
            if (date) {
                dailyTotals[date] = (dailyTotals[date] || 0) + count!;
            }
        });

        return Object.entries(dailyTotals).map(([date, totalCount]) => ({ date, totalCount })).sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());
    }

}
