import { AdminCreateUserCommandInput } from "@aws-sdk/client-cognito-identity-provider";
import { IAMUserCreate } from "../models/iam-user-create";
import { UserContext } from "../../../../commons/auth/user-context";
import { IAMUserAttribute } from "../models/iam-user-attribute";
import { CognitoAttributeMapper } from "../cognito-attribute-mapper";

export class IAMAWSMapper {

    static toAdminCreateUserCommandInput(userPoolId:string, user: IAMUserCreate, userContext:UserContext): AdminCreateUserCommandInput {
        const userAttributes:IAMUserAttribute[] = [];
        userAttributes.push(new IAMUserAttribute("email", "email", user.email));
        userAttributes.push(new IAMUserAttribute("name", "name", user.name));
        userAttributes.push(new IAMUserAttribute("address", "address", user.address));
        userAttributes.push(new IAMUserAttribute("family_name", "familyName", user.familyName));
        userAttributes.push(new IAMUserAttribute("gender", "gender", user.gender ? user.gender.toString() : ""));
        userAttributes.push(new IAMUserAttribute("nickname", "nickname", user.nickname));
        // Explicitly set email_verified to true.
        userAttributes.push(new IAMUserAttribute("email_verified", "email_verified", "true"));
        // userAttributes.push(new IAMUserAttribute("phone_number", "phoneNumber", user.phoneNumber));
        // userAttributes.push(new IAMUserAttribute("phone_number_verified", "phone_number_verified", "true"));
        userAttributes.push(new IAMUserAttribute("custom:user_id", "userId", user.userId));
        // userAttributes.push(new IAMUserAttribute("custom:referral_code", "ref", user.ref));

        CognitoAttributeMapper.validateUserAttributes(userContext.userId!, userAttributes, false);

        const input: AdminCreateUserCommandInput = {
            UserPoolId: userPoolId,
            Username: user.email,
            UserAttributes: userAttributes,
            MessageAction: 'SUPPRESS'  // Prevent automatic welcome email
        } as AdminCreateUserCommandInput;
        
        return input;
    }

    /**
     * Converts IAMUserCreate to an array of standard AWS Cognito AttributeType objects.
     * This is used for the SignUpCommand which expects AttributeType[] format.
     * 
     * @param user The user information to convert
     * @returns AttributeType[] An array of Cognito attribute objects
     */
    static toUserAttributes(user: IAMUserCreate): { Name: string, Value: string }[] {
        const attributes = [
            { Name: "email", Value: user.email },
            { Name: "phone_number", Value: user.phoneNumber },
            { Name: "name", Value: user.name },
            { Name: "address", Value: user.address },
            { Name: "family_name", Value: user.familyName },
            { Name: "nickname", Value: user.nickname },
            { Name: "custom:user_id", Value: user.userId }
        ];

        // Add gender if available
        if (user.gender !== undefined) {
            attributes.push({ Name: "gender", Value: user.gender.toString() });
        }

        // Add referral code if available
        if (user.ref) {
            attributes.push({ Name: "custom:referral_code", Value: user.ref });
        }

        // Filter out any attributes with undefined values
        return attributes.filter(attr => attr.Value !== undefined);
    }
}