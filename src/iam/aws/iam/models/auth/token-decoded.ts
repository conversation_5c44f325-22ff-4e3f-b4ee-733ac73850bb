/**
 * {
            sub: '8afa1bda-586f-4f5c-a65c-xxxxxx',
            'cognito:groups': [ 'admin', 'ew_user' ],
            iss: 'https://cognito-idp.eu-central-1.amazonaws.com/eu-central-1_eInVWpQWI',
            client_id: '3c4p4te2qf4gm98fc6r0at08cg',
            origin_jti: 'b3b8894f-f2eb-4577-9114-d0f6be21a20a',
            event_id: '01e7c5be-9bfe-4e11-9c6e-f3f605dbf1dc',
            token_use: 'access',
            scope: 'aws.cognito.signin.user.admin',
            auth_time: 1740129402,
            exp: 1740215801,
            iat: 1740129402,
            jti: 'bf8c7d67-0c9e-4c49-826a-8b12eeb0a6fa',
            username: '8afa1bda-586f-4f5c-a65c-xxxxxx'
        }
 */
export class TokenDecoded {
    sub: string;
    groups: string[];
    iss: string;
    clientId: string;
    originJti: string;
    eventId: number;
    tokenUse: string;
    scope: string;
    authTime:number
    exp: number;
    iat: number;
    jti: string;
    username: string;

    constructor(sub: string, groups: string[], iss: string, clientId: string, originJti: string, eventId: number, tokenUse: string, scope: string, authTime:number, exp: number, iat: number, jti: string, username: string) {
        this.sub = sub;
        this.groups = groups;
        this.iss = iss;
        this.clientId = clientId;
        this.originJti = originJti;
        this.eventId = eventId;
        this.tokenUse = tokenUse;
        this.scope = scope;
        this.authTime = authTime;
        this.exp = exp;
        this.iat = iat;
        this.jti = jti;
        this.username = username;
    }
}