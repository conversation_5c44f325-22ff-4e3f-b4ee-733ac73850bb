import { IAMUserCreate } from "./iam-user-create";

export class IAM<PERSON>ser extends IAMUserCreate{
    sub: string; // User ID (Sub) in AWS congnito in the form of 8b0f9a4e-e438-4665-8af4-XXX069a12fXX
    createdAt: Date;
    updatedAt: Date;
    enabled: boolean;
    status: string;

    constructor(sub:string, userId: string, name: string, phoneNumber:string, familyName: string, nickname: string, email: string, gender: number, address: string, 
        ref: string, groups: string[], createdAt: Date, updatedAt: Date, enabled: boolean, status: string) {
        super(userId, name, phoneNumber, familyName, nickname, email, gender, address, ref, groups);
        this.sub = sub;
        this.createdAt = createdAt;
        this.updatedAt = updatedAt;
        this.enabled = enabled;
        this.status = status;
    }
    
}