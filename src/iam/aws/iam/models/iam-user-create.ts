export class IAMUserCreate {
    userId: string;
    name: string;
    familyName: string;
    nickname: string;
    email: string;
    phoneNumber: string;
    gender: number;
    address: string;
    ref: string;
    groups: string[];

    constructor(userId: string, name: string, familyName: string, nickname: string, email: string, phoneNumber: string, gender: number, address: string, ref: string, groups: string[]) {
        this.userId = userId;
        this.name = name;
        this.familyName = familyName;
        this.nickname = nickname;
        this.email = email;
        this.phoneNumber = phoneNumber;
        this.gender = gender;
        this.address = address;
        this.ref = ref;
        this.groups = groups;
    }

}