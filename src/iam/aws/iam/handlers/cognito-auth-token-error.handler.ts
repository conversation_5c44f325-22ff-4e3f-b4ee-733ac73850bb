import { WPError } from "../../../../commons/error/wp.error";
import { ServiceException } from '@aws-sdk/smithy-client';
import { NotAuthorizedException, UnauthorizedException } from "@aws-sdk/client-cognito-identity-provider";
import { WP_ERROR_USER_COGNITO_AUTH_GENERAL_ERROR, WP_ERROR_USER_COGNITO_AUTH_USER_NOTAUTHORIZED, WP_ERROR_USER_COGNITO_UNAUTHORIZED } from "../../../../commons/error/error.codes";

/**
 * Class to handle the Cognito Auth errors
 */
export class CognitoAuthTokenErrorHandler {


    private error: any;
    constructor(error: any) {
        this.error = error;
    }


    /**
     * Handle the API Gateway error and return a WPError
     * @returns WPError
     */
    public handle(): WPError {

        if (this.isUnauthorizedException(this.error)) {
            const nfe: UnauthorizedException = this.error as UnauthorizedException;
            return new WPError(WP_ERROR_USER_COGNITO_UNAUTHORIZED, nfe.message, nfe, WP_ERROR_USER_COGNITO_UNAUTHORIZED.headerStatusCode + "");
        }

        if (this.isNotAuthorizedException(this.error)) {
            const nae: NotAuthorizedException = this.error as NotAuthorizedException;
            return new WPError(WP_ERROR_USER_COGNITO_AUTH_USER_NOTAUTHORIZED, nae.message, nae, WP_ERROR_USER_COGNITO_AUTH_USER_NOTAUTHORIZED.headerStatusCode + "");
        }
        else if (this.error instanceof Error) {
            return new WPError(WP_ERROR_USER_COGNITO_AUTH_GENERAL_ERROR, this.error.message, (this.error as Error), WP_ERROR_USER_COGNITO_AUTH_GENERAL_ERROR.headerStatusCode + "");
        } else {
            return new WPError(WP_ERROR_USER_COGNITO_AUTH_GENERAL_ERROR, this.error.messag, undefined, WP_ERROR_USER_COGNITO_AUTH_GENERAL_ERROR.headerStatusCode + "");
        }
    }

    public isUnauthorizedException(error: any): boolean {
        if (error instanceof ServiceException && (error as Error).name === UnauthorizedException.name) {
            return true;
        }
        return false;
    }

    public isNotAuthorizedException(error: any): boolean {
        if (error instanceof ServiceException && (error as Error).name === NotAuthorizedException.name) {
            return true;
        }
        return false;
    }

}