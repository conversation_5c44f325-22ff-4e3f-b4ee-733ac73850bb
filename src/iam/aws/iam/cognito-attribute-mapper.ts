import { WPError } from "../../../commons/error/wp.error";
import { WP_ERROR_IAM_USER_ATTRIBUTE_NOT_AVAILABLE, WP_ERROR_IAM_USER_ATTRIBUTE_NOT_CREATABLE, WP_ERROR_IAM_USER_ATTRIBUTE_NOT_UPDATABLE, WP_ERROR_IAM_USER_ATTRIBUTE_VALIDATION } from "../../../commons/error/error.codes";
import { IAMUserAttribute } from "./models/iam-user-attribute";
import { AttributeValidationError } from "../services/aws.service";

export interface CognitoAttributeMapperInterface {
    name: string;
    /**
     * name of the attribute in the wodo platform
     */
    mappedName: string;
    createable: boolean;
    updatable: boolean;
    required?: boolean;
    patternMatch?: string;
    patternMatchErrorMessage?: string;
}

export class CognitoAttributeMapper {
    static _name: CognitoAttributeMapperInterface = { name: "name", mappedName: "name", updatable: true, createable:true, required: true, patternMatch: "^\\p{L}[\\p{L}\\s]{1,49}$"};
    static family_name: CognitoAttributeMapperInterface = { name: "family_name", mappedName: "lastname", updatable: true, createable:true, required: true, patternMatch: "^\\p{L}[\\p{L}\\s]{1,49}$" };
    static gender: CognitoAttributeMapperInterface = { name: "gender", mappedName: "gender", updatable: true, createable:true, required: false };
    static nickname: CognitoAttributeMapperInterface = {
        name: "nickname",
        mappedName: "nickname",
        updatable: true,
        createable:true,
        required: false,
        /**
         * The regex pattern follows a structured approach to validate usernames effectively. 
         * It enforces 
         *  length constraints min 5 max 20, 
         *  consists of alphanumeric(all of the letters and numerals in a given language set)
         *  character types, 
         *  and prohibits consecutive dots or underscores, 
         * ensuring a valid username format
         */
        patternMatch: "^(?=[\\p{L}\\p{N}._-]{5,20}$)(?!.*[_.]{2})[^_.].*[^_.]$",
        patternMatchErrorMessage: "only letters and numbers are allowed"

    };
    static phone_number: CognitoAttributeMapperInterface = { name: "phone_number", mappedName: "phoneNumber", updatable: true, createable:true };

    static email: CognitoAttributeMapperInterface = { 
        name: "email", 
        mappedName: "email", 
        createable:true,
        updatable: true, 
        required: true, 
        patternMatch: "^[\\w\\-\\.]+@([\\w\\-]+\\.)+[\\w\\-]{2,4}$" 
      };
    static birthdate: CognitoAttributeMapperInterface = {
        name: "birthdate",
        mappedName: "birthdate",
        createable:true,
        updatable: true,
        required: true,
        patternMatch: "[0-9]{4}(/|-)[0-9]{2}(/|-)[0-9]{2}",
        patternMatchErrorMessage: "invalid date value.The format must be yyyy-mm-dd"
    };
    static picture: CognitoAttributeMapperInterface = { name: "picture", mappedName: "image", updatable: true,createable:true };
    static zoneinfo: CognitoAttributeMapperInterface = { name: "zoneinfo", mappedName: "zoneinfo", updatable: false, createable:true };

    //new user
    static nw: CognitoAttributeMapperInterface = { name: "custom:nw", mappedName: "nw", updatable: true, createable:true };
    static utm: CognitoAttributeMapperInterface = { name: "custom:utm", mappedName: "utm", updatable: true, createable:true };
    static ref: CognitoAttributeMapperInterface = { name: "custom:referrer_code", mappedName: "ref", updatable: true, createable:true };
    static locale: CognitoAttributeMapperInterface = { name: "locale", mappedName: "locale", updatable: false, createable:true };

    static user_id: CognitoAttributeMapperInterface = { name: "custom:user_id", mappedName: "userId", updatable: false, createable:true, required: true };
    static address: CognitoAttributeMapperInterface = { name: "address", mappedName: "address", updatable: true, createable:true };

    static email_verified: CognitoAttributeMapperInterface = { name: "email_verified", mappedName: "email_verified", updatable: true, createable:true, required: false };
    static phone_number_verified: CognitoAttributeMapperInterface = { name: "phone_number_verified", mappedName: "phone_number_verified", updatable: true, createable:true, required: false };


    static allAttr: Map<string, CognitoAttributeMapperInterface> = new Map<string, CognitoAttributeMapperInterface>();

    static {
        this.allAttr.set(this._name.name, this._name);
        this.allAttr.set(this.family_name.name, this.family_name);
        this.allAttr.set(this.gender.name, this.gender);
        this.allAttr.set(this.phone_number.name, this.phone_number);
        this.allAttr.set(this.email.name, this.email);
        this.allAttr.set(this.nw.name, this.nw);
        this.allAttr.set(this.utm.name, this.utm);
        this.allAttr.set(this.ref.name, this.ref);
        this.allAttr.set(this.nickname.name, this.nickname);
        this.allAttr.set(this.locale.name, this.locale);
        this.allAttr.set(this.birthdate.name, this.birthdate);
        this.allAttr.set(this.picture.name, this.picture);
        this.allAttr.set(this.zoneinfo.name, this.zoneinfo);
        this.allAttr.set(this.user_id.name, this.user_id);
        this.allAttr.set(this.address.name, this.address);
        this.allAttr.set(this.email_verified.name, this.email_verified);
        this.allAttr.set(this.phone_number_verified.name, this.phone_number_verified);
    }

    /**
     * Validates the given attribute
     * 
     * @param iamAttr 
     * @returns @AttributeValidationError
     */
    public static validateUserAttribute(user_id: string, iamAttr: IAMUserAttribute, update:boolean = true) {
        const attr: CognitoAttributeMapperInterface | undefined = this.allAttr.get(iamAttr.Name);
        if (!attr) {
            throw new WPError(WP_ERROR_IAM_USER_ATTRIBUTE_NOT_AVAILABLE, `user: ${user_id} tires to operate on attribute:${iamAttr.Name} not available`, undefined, "400");
        }
       
        if(update) {
            if (!attr.updatable) {
                throw new WPError(WP_ERROR_IAM_USER_ATTRIBUTE_NOT_UPDATABLE, `user: ${user_id} tires to operate on attribute:${iamAttr.Name} not updatable`, undefined, "400");
            }
        }
        else {
            if (!attr.createable) {
                throw new WPError(WP_ERROR_IAM_USER_ATTRIBUTE_NOT_CREATABLE, `user: ${user_id} tires to operate on attribute:${iamAttr.Name} not createable`, undefined, "400");
            }
        }

        if (attr.required && (!iamAttr.Value || iamAttr.Value.trim().length === 0)) {
            return new AttributeValidationError(iamAttr.mappedName, AttributeValidationError.ATTR_ERROR_REQUIRED,"valeu required");
        }
        if (attr.patternMatch) {
            const rg0 = new RegExp(attr.patternMatch, 'u');

            if (!iamAttr.Value || rg0.test(iamAttr.Value.trim()) === false) {
                return new AttributeValidationError(iamAttr.mappedName,  AttributeValidationError.ATTR_ERROR_REQUIRED,attr.patternMatchErrorMessage);
            }
        }
        return null;
    }

    public static validateUserAttributes(user_id: string, attributes: IAMUserAttribute[], update:boolean = true) {
        let responses: AttributeValidationError[] = [];
        for (let index = 0; index < attributes.length; index++) {
            const attr = attributes[index];
            let val0 = this.validateUserAttribute(user_id,attr, update);
            if (val0 && val0 !== null) responses.push(val0);
        }
        if(responses.length > 0) {
            let msg = "";
            for (let i = 0; i < responses.length; i++) {
                const attrValidationError:AttributeValidationError = responses[i];
                msg = msg + `${attrValidationError.name} ${attrValidationError.error}.\n`;                
            }
            throw new WPError(WP_ERROR_IAM_USER_ATTRIBUTE_VALIDATION, msg, `user: ${user_id} has attribute validation errors`, "400");
        }
    }
}
