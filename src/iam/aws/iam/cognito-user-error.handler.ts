import { WPError } from "../../../commons/error/wp.error";
import {
    WP_ERROR_USER_COGNITO_UNAUTHORIZED,
    WP_ERROR_USER_COGNITO_NOT_FOUND_EXCEPTION,
    WP_ERROR_USER_COGNITO_VALIDATION_PARAMS,
    WP_ERROR_COGNITO_GENERAL_ERROR} from "../../../commons/error/error.codes";
import { ServiceException } from '@aws-sdk/smithy-client';
import { CognitoIdentityProviderServiceException, ResourceNotFoundException, UnauthorizedException } from "@aws-sdk/client-cognito-identity-provider";

/**
 * Class to handle the Cognito  errors
 */
export class CognitoUserErrorHandler {

    private error: any;
    constructor(error: any) {
        this.error = error;
    }


    /**
     * Handle the Cognito  error and return a WPError
     * @returns WPError
     */
    public  handle(): WPError {
        if (this.isUnauthorizedException(this.error)) {
            const nfe:UnauthorizedException = this.error as UnauthorizedException;
            return new WPError(WP_ERROR_USER_COGNITO_UNAUTHORIZED,nfe.message, nfe, WP_ERROR_USER_COGNITO_UNAUTHORIZED.headerStatusCode + "");
        }
        else if (this.isNotFoundException(this.error)) {
            const nfe:ResourceNotFoundException = this.error as ResourceNotFoundException;
            return new WPError(WP_ERROR_USER_COGNITO_NOT_FOUND_EXCEPTION,nfe.message, nfe, WP_ERROR_USER_COGNITO_NOT_FOUND_EXCEPTION.headerStatusCode + "");
        }
        else if (this.isValidationError(this.error)) {
            const se:CognitoIdentityProviderServiceException = this.error as CognitoIdentityProviderServiceException;
           
            /*if(this.error.message.includes("Value null at 'createUsagePlanInput.name'")){ 
                console.log("Name is required"); 
            }*/

            return new WPError(WP_ERROR_USER_COGNITO_VALIDATION_PARAMS,se.message, se, WP_ERROR_USER_COGNITO_VALIDATION_PARAMS.headerStatusCode + "");
        }
        else if (this.error instanceof Error) {
            return new WPError(WP_ERROR_COGNITO_GENERAL_ERROR,this.error.message, (this.error as Error), WP_ERROR_COGNITO_GENERAL_ERROR.headerStatusCode + "");
        } else {
            return new WPError(WP_ERROR_COGNITO_GENERAL_ERROR,this.error.messag, undefined, WP_ERROR_COGNITO_GENERAL_ERROR.headerStatusCode + "");
        }
    }

    public isUnauthorizedException(error:any): boolean {
        if (error instanceof ServiceException && (error as Error).name === UnauthorizedException.name) {
           return true;
        }
       return false;
    }

    /**
     * Check if the error is a NotFoundException from the AWS Cognito  SDK
     * 
     * @param error 
     * @returns boolean
     */
    public isNotFoundException(error:any): boolean {
        if (error instanceof ServiceException && (error as Error).name === ResourceNotFoundException.name) {
           return true;
        }
       return false;
    }

    public isValidationError(error:any): boolean {
    
        if (error instanceof CognitoIdentityProviderServiceException && (error as Error).name === "ValidationException") {
           return true;
        }
       return false;
    }

    public isNameNullValidationError(errorMessage:string):boolean {
        if(errorMessage.includes("Value null at 'createUsagePlanInput.name'")){ 
            console.log("Name is required"); 
            return true;
        }
        return false;
    }

}