import { CreateSecretCommandOutput, DescribeSecretCommandOutput, GetSecretValueCommandOutput, Tag } from "@aws-sdk/client-secrets-manager";
import { AwsSecret } from "../models/aws-secret";
import { AwsTag } from "../../commons/models/aws.tag";
import { AwsSecretValue } from "../models/aws-secret-value";

/**
 * AWS Secrets Manager Mapper
 */
export class AwsSmMapper {

    public static toAwsSecretFromCreate(result:CreateSecretCommandOutput, secretString:string, createdAt:Date, tags?:AwsTag[]): AwsSecret {
        if(result == null || result == undefined) {
            return new AwsSecret("", "", "", "", createdAt, tags);
        }
        return new AwsSecret(result.ARN!, result.Name!, secretString, result.VersionId!, createdAt, tags);
    }

    public static toAwsSecret(result:DescribeSecretCommandOutput, awsSecretValue:AwsSecretValue): AwsSecret {
        if(result == null || result == undefined) {
            return new AwsSecret("", "", "", "", new Date(), []);
        }

        if(awsSecretValue == null || awsSecretValue == undefined) {
            return new AwsSecret("", "", "", "", new Date(), []);
        }

        const tags:Tag[] = result.Tags!;
        const awsTags:AwsTag[] | undefined = tags?.map(tag => {
            return new AwsTag(tag.Key!, tag.Value!);
        });
        return new AwsSecret(result.ARN!, result.Name!, awsSecretValue.secretString!, awsSecretValue.versionId!, awsSecretValue.createdAt, awsTags);
    }

    public static toAwsSecretValue(result:GetSecretValueCommandOutput): AwsSecretValue  {
        if(result == null || result == undefined) {
            return new AwsSecretValue("", "", "", "", new Date());
        }
        return new AwsSecretValue(result.ARN!, result.Name!, result.SecretString!, result.VersionId!, result.CreatedDate!);
    }

}