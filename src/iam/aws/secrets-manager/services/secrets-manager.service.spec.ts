import { Test, TestingModule } from '@nestjs/testing';
import {
    SecretsManagerClient,
    CreateSecretCommandOutput,
    GetSecretValueCommandOutput,
    DescribeSecretCommandOutput,
    DeleteSecretCommandOutput,
    UpdateSecretCommandOutput,
    ListSecretsCommandOutput,
    ResourceNotFoundException,
} from '@aws-sdk/client-secrets-manager';
import { AwsTag } from '../../commons/models/aws.tag';
import { AwsSecret } from '../models/aws-secret';
import { AwsSecretValue } from '../models/aws-secret-value';
import { AwsSecretsManagerService } from './secrets-manager.service';

jest.mock('@aws-sdk/client-secrets-manager');

const mockSecretsManagerClient = {
    send: jest.fn(),
};

describe('AwsSecretsManagerService', () => {
    let service: AwsSecretsManagerService;

    beforeEach(async () => {
        jest.clearAllMocks();

        const module: TestingModule = await Test.createTestingModule({
            providers: [AwsSecretsManagerService],
        }).compile();

        service = module.get<AwsSecretsManagerService>(AwsSecretsManagerService);

        // Override the SecretsManagerClient instance with a mocked version
        (service as any).client = mockSecretsManagerClient;
    });

    it('should be defined', () => {
        expect(service).toBeDefined();
    });

    describe('createSecret', () => {
        it('should create a secret and return AwsSecret', async () => {
            const mockCreateSecretOutput: CreateSecretCommandOutput = {
                $metadata: {},
                ARN: 'arn:aws:secretsmanager:region:account:secret/test-secret',
                Name: 'test-secret',
            };

            mockSecretsManagerClient.send.mockResolvedValue(mockCreateSecretOutput);

            const secretName = 'test-secret';
            const secretValue = 'test-value';
            const tags: AwsTag[] = [
                { TagKey: 'walletId', TagValue: 'wallet1' },
                { TagKey: 'userId', TagValue: 'user1' },
            ];;

            const result:AwsSecret = await service.createSecret(secretName, secretValue, tags);

            expect(mockSecretsManagerClient.send).toHaveBeenCalledTimes(1);
            expect(result).toBeDefined();
            expect(result.name).toEqual(secretName);
            expect(result.arn).toEqual(mockCreateSecretOutput.ARN);
            expect(result.secretString).toEqual(secretValue);
        });

        it('should throw an error if createSecret fails', async () => {
            mockSecretsManagerClient.send.mockRejectedValue(new Error('createSecret failed'));

            const secretName = 'test-secret';
            const secretValue = 'test-value';
            const tags: AwsTag[] = [
                { TagKey: 'walletId', TagValue: 'wallet1' },
                { TagKey: 'userId', TagValue: 'user1' },
            ];;

            await expect(service.createSecret(secretName, secretValue, tags)).rejects.toThrow('createSecret failed');
        });
    });

    describe('getSecret', () => {
        it('should return a secret if found', async () => {

            const secretName = 'test-secret';
            const tags: AwsTag[] = [
                { TagKey: 'walletId', TagValue: 'wallet1' },
                { TagKey: 'userId', TagValue: 'user1' },
            ];;

            const mockDescribeSecretOutput: DescribeSecretCommandOutput = {
                $metadata: {},
                ARN: 'arn:aws:secretsmanager:region:account:secret/test-secret',
                Name: secretName,
                Tags: [{ Key: 'walletId', Value: 'wallet1' }, { Key: 'userId', Value: 'user1' }],
                Description: 'Test Secret',
                KmsKeyId: 'test-key',
                RotationEnabled: false,
                RotationLambdaARN: 'arn:aws:lambda:region:account:function:test-lambda',
                RotationRules: {
                    AutomaticallyAfterDays: 0,
                },
                LastRotatedDate: new Date(),
                LastChangedDate: new Date(),
                LastAccessedDate: new Date(),
                DeletedDate: new Date(),
                CreatedDate: new Date(),
                VersionIdsToStages: {
                    'version-id': ['AWSCURRENT'],
                }
            };

            const mockGetSecretValueOutput: GetSecretValueCommandOutput = {
                $metadata: {},
                SecretString: 'test-value',
                ARN: 'arn:aws:secretsmanager:region:account:secret/test-secret',
                Name: 'test-secret',
                VersionId: 'version-id',
                CreatedDate: new Date()
            };

            mockSecretsManagerClient.send
                .mockResolvedValueOnce(mockDescribeSecretOutput)
                .mockResolvedValueOnce(mockGetSecretValueOutput);

          

            const result:AwsSecret | null = await service.getSecret(secretName);

            expect(mockSecretsManagerClient.send).toHaveBeenCalledTimes(2);
            expect(result).toBeDefined();
            expect(result).toBeInstanceOf(AwsSecret);
            expect(result!.name).toEqual(secretName);
            expect(result!.secretString).toEqual(mockGetSecretValueOutput.SecretString);
            expect(result!.arn).toEqual(mockDescribeSecretOutput.ARN);
            expect(result!.tags).toBeDefined();
            expect(result!.tags).toEqual(tags);
        });

        it('should return null if secret not found', async () => {
            mockSecretsManagerClient.send.mockRejectedValue(new ResourceNotFoundException( { message: 'Secret not found' , $metadata: {} }));

            const secretName = 'non-existent-secret';

            const result = await service.getSecret(secretName);

            expect(result).toBeNull();
        });

        it('should throw an error for other exceptions', async () => {
            mockSecretsManagerClient.send.mockRejectedValue(new Error('Some AWS Error'));

            const secretName = 'test-secret';

            await expect(service.getSecret(secretName)).rejects.toThrow('Some AWS Error');
        });
    });

    describe('deleteSecret', () => {
        it('should delete a secret successfully', async () => {
            const mockDeleteSecretOutput: DeleteSecretCommandOutput = {
                $metadata: {},
            };

            mockSecretsManagerClient.send.mockResolvedValue(mockDeleteSecretOutput);

            const secretName = 'test-secret';

            await service.deleteSecret(secretName);

            expect(mockSecretsManagerClient.send).toHaveBeenCalledTimes(1);
        });

        it('should throw an error if deleteSecret fails', async () => {
            mockSecretsManagerClient.send.mockRejectedValue(new Error('deleteSecret failed'));

            const secretName = 'test-secret';

            await expect(service.deleteSecret(secretName)).rejects.toThrow('deleteSecret failed');
        });
    });

    describe('listSecrets', () => {
        it('should list secrets successfully', async () => {
            const mockListSecretsOutput: ListSecretsCommandOutput = {
                $metadata: {},
                SecretList: [{ Name: 'secret1' }, { Name: 'secret2' }],
            };

            mockSecretsManagerClient.send.mockResolvedValue(mockListSecretsOutput);

            const result = await service.listSecrets();

            expect(mockSecretsManagerClient.send).toHaveBeenCalledTimes(1);
            expect(result).toEqual(['secret1', 'secret2']);
        });

        it('should throw an error if listSecrets fails', async () => {
            mockSecretsManagerClient.send.mockRejectedValue(new Error('listSecrets failed'));

            await expect(service.listSecrets()).rejects.toThrow('listSecrets failed');
        });
    });
});
