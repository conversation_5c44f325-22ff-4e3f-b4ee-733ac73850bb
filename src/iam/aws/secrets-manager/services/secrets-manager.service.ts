import { CreateSecretCommand, CreateSecretCommandInput, CreateSecretCommandOutput, DeleteSecretCommand, 
    DeleteSecretCommandInput, DeleteSecretCommandOutput, DescribeSecretCommand, DescribeSecretCommandInput, 
    DescribeSec<PERSON>CommandOutput, GetSecretValueCommand, GetSecretValueCommandInput, GetSecretValueCommandOutput, 
    ListSecretsCommand, ListSecretsCommandInput, ResourceNotFoundException, SecretsManagerClient, Tag, UpdateSecretCommand, UpdateSecretCommandInput, 
    UpdateSecretCommandOutput } 
from "@aws-sdk/client-secrets-manager";
import { Injectable, Logger } from "@nestjs/common";
import { AwsTag } from "../../commons/models/aws.tag";
import { AwsSmMapper } from "../utils/aws-sm.mapper";
import { AwsSecret } from "../models/aws-secret";
import { AwsSecretValue } from "../models/aws-secret-value";

/**
 * Service class for AWS Secrets Manager
 * 
 * https://docs.aws.amazon.com/AWSJavaScriptSDK/v3/latest/client/secrets-manager/
 */
@Injectable()
export class AwsSecretsManagerService {
    private logger = new Logger(AwsSecretsManagerService.name);

    private readonly client: SecretsManagerClient;

    constructor() {
        this.client = new SecretsManagerClient();
    }

    /**
     *  Create a new secret
     * @param secretName 
     * @param secretValue 
     * @param tags 
     * @returns CreateSecretCommandOutput
     */
    async createSecret(secretName: string, secretValue: string, tags?:AwsTag[]): Promise<AwsSecret> {
        
        let stags:Tag[] = [];
        if(!tags) {
            stags = tags!.map((t) => {
                return {
                    Key: t.TagKey,
                    Value: t.TagValue
                } as Tag;
            });
        }
        const input:CreateSecretCommandInput = {
            Name: secretName,
            SecretString: secretValue,
            Tags: stags
        } as CreateSecretCommandInput;

        try {
            const command:CreateSecretCommand = new CreateSecretCommand(input);
            const result:CreateSecretCommandOutput = await this.client.send(command);
            this.logger.debug(`Secret: ${secretName} created`, result);
            const awsSecret:AwsSecret = AwsSmMapper.toAwsSecretFromCreate(result, secretValue, new Date(), tags);
            return awsSecret; 
        } catch (error) {
            this.logger.error(`Error creating secret: ${secretName}`, error);
            throw error;
        }
    }

    /**
     * Get a secret
     * @param secretName The ARN or name of the secret.
     * @returns AwsSecret or null
     */
    async getSecret(secretName: string): Promise<AwsSecret | null> {
        const input:DescribeSecretCommandInput = {
            SecretId: secretName,
        } as DescribeSecretCommandInput;

        const command:DescribeSecretCommand = new DescribeSecretCommand(input);
        let result:DescribeSecretCommandOutput;
        try {
            result  = await this.client.send(command);
            this.logger.debug(`found secret: ${secretName}`);
        } catch (error) {
            if(error instanceof ResourceNotFoundException) {
                this.logger.debug(`Secret not found secret: ${secretName}`);
                return null;
            }
            this.logger.error(`Error finding secret: ${secretName}`, error);
            throw error;
        }

        const secretValue:AwsSecretValue | null = await this.getSecretValue(secretName);
        const awsSecret:AwsSecret | null = AwsSmMapper.toAwsSecret(result, secretValue!);
        return awsSecret;
    }

    /**
     * Get the value of a secret
     * @param secretName 
     * @returns string
     */
    async getSecretValue(secretName: string): Promise<AwsSecretValue | null> {
        const input:GetSecretValueCommandInput = {
            SecretId: secretName,
        }as GetSecretValueCommandInput;

        try {
            const command:GetSecretValueCommand = new GetSecretValueCommand(input);
            const result:GetSecretValueCommandOutput = await this.client.send(command);
            this.logger.debug(`Found secret value: ${secretName}`, result.ARN);
            const value:AwsSecretValue = AwsSmMapper.toAwsSecretValue(result);
            return value;
        } catch (error) {
            if(error instanceof ResourceNotFoundException) {
                this.logger.debug(`Secret value not found secret: ${secretName}`);
                return null;
            }
            console.error(`Error finding secret value: ${secretName}`, error);
            throw error;
        }
    }
    
    async updateSecret(secretName: string, secretValue: string): Promise<void> {
        const input:UpdateSecretCommandInput = {
            SecretId: secretName,
            SecretString: secretValue,
        } as UpdateSecretCommandInput;
       
        const command:UpdateSecretCommand = new UpdateSecretCommand(input);
        try {
            const result:UpdateSecretCommandOutput = await this.client.send(command);
            this.logger.debug(`Secret: ${secretName} updated`, result);
        } catch (error) {
            this.logger.error(`Error updating secret: ${secretName}`, error);
            throw error;
        }
    }

    /**
     * Delete a secret
     * 
     * @param secretName 
     */
    async deleteSecret(secretName: string): Promise<void> {
        const input:DeleteSecretCommandInput = {
            SecretId: secretName,
        } as DeleteSecretCommandInput;

        const command:DeleteSecretCommand = new DeleteSecretCommand(input);
        try {
            const result:DeleteSecretCommandOutput = await this.client.send(command);
            this.logger.debug(`Secret: ${secretName} deleted`, result);
        } catch (error) {
            this.logger.error(`Error deleting secret: ${secretName}`, error);
            throw error;
        }
    }

    /**
     * List all secrets
     * 
     * @param maxResults 
     * @returns string[]
     */
    async listSecrets(maxResults: number = 100): Promise<string[]> {
        const input:ListSecretsCommandInput = {
            MaxResults: maxResults,
        } as ListSecretsCommandInput;

        const command:ListSecretsCommand = new ListSecretsCommand(input);
        try {
            const result = await this.client.send(command);
            const secretList:string[] = result.SecretList!.map((s) => s.Name!);
            this.logger.debug("Secrets:", secretList);
            return secretList;
        } catch (error) {
            this.logger.error("Error listing secrets:", error);
            throw error;
        }
    }
   
}