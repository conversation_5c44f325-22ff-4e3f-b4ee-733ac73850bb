import {
    Create<PERSON>liasCommand, CreateAliasCommandInput, CreateAliasCommandOutput, CreateKeyCommand,
    CreateKeyCommandInput, CreateKeyCommandOutput, DataKeySpec, DecryptCommand, DecryptCommandInput,
    DecryptCommandOutput, DescribeKeyCommand, DescribeKeyCommandInput, DescribeKeyCommandOutput,
    EncryptCommand, EncryptCommandInput, EncryptCommandOutput, GenerateDataKeyCommand,
    GenerateDataKeyCommandInput, GenerateDataKeyCommandOutput, KeyMetadata, KeySpec, KeyUsageType, KMSClient, ListResourceTagsCommand, ListResourceTagsCommandInput, ListResourceTagsCommandOutput, NotFoundException, ScheduleKeyDeletionCommand,
    ScheduleKeyDeletionCommandInput, ScheduleKeyDeletionCommandOutput,
}
from "@aws-sdk/client-kms";
import { AwsKeyMetadata } from "../models/aws-key-metadata";
import { AwsKmsMapper } from "../utils/aws-kms.mapper";
import { AwsKey, Aws<PERSON>eyCreate, AwsKeyGenerate } from "../models/aws-key";
import { AwsTag } from "../../commons/models/aws.tag";
import { AwsDataKey, AwsDataKeyGenerate } from "../models/aws-data-key";
import { AwsKeyAlias, AwsKeyAliasCreate } from "../models/aws-key.alias";
import { WPError } from "../../../../commons/error/wp.error";
import { Inject, Injectable, Logger } from "@nestjs/common";
import { WP_ERROR_AWS_KMS_VALIDATION } from "../../../../commons/error/error.codes";

/**
 * Service class for AWS KMS
 * 
 * https://docs.aws.amazon.com/AWSJavaScriptSDK/v3/latest/client/kms/
 */
@Injectable()
export class AwsKmsService {

    private logger = new Logger(AwsKmsService.name);

    public kmsClient: KMSClient;

    constructor() {
        this.kmsClient = new KMSClient();
    }

    getKMSClient(): KMSClient {
        return this.kmsClient;
    }

    /**
     * Creates a KMS key.
     * 
     * https://docs.aws.amazon.com/AWSJavaScriptSDK/v3/latest/client/kms/command/CreateKeyCommand/ 
     * 
     * @param params AwsKeyCreate
     * 
     * Default algorithm is KeySpec.SYMMETRIC_DEFAULT represented by the key specification AES-256 in Galois/Counter Mode (GCM). 
     * It is used for both encryption and decryption operations in AWS KMS
     * 
     * @returns KeyMetadata
     * 
     * Key Metadata includes 
     *  * Key ID
     *  * Key ARN
     *  * Description
     *  * Key state (enabled, disabled, etc.)
     *  * Key usage (e.g., ENCRYPT_DECRYPT)
     *  * Creation date
     *  * tags
     * 
     */
    async createKey(params: AwsKeyCreate): Promise<AwsKeyMetadata> {
        const input: CreateKeyCommandInput = {
            Description: params.Description,
            KeySpec: params.KeySpec,
            KeyUsage: params.KeyUsage,
            MultiRegion: params.MultiRegion,
            Tags: params.Tags,
        } as CreateKeyCommandInput;

        const createKeyCommand: CreateKeyCommand = new CreateKeyCommand(input);
        try {
            const result: CreateKeyCommandOutput = await this.getKMSClient().send(createKeyCommand);
            this.logger.debug("Aws Key created:", result);
            const km: KeyMetadata = result.KeyMetadata!;
            const awsKeyMetadata: AwsKeyMetadata = AwsKmsMapper.toAwsKeyMetadata(km, params.Tags!);
            return awsKeyMetadata;
        } catch (error) {
            this.logger.error("Error creating aws key:", error);
            throw error;
        }
    }

    /**
     * Retrives the metadata for a KMS key. 
     * It is not possible to retrieve the actual cryptographic material (key content) of a managed key from AWS KMS. 
     * This is by design and is a fundamental security feature of AWS KMS.
     * 
     * Key Metadata includes 
     *  * Key ID
     *  * Key ARN
     *  * Description
     *  * Key state (enabled, disabled, etc.)
     *  * Key usage (e.g., ENCRYPT_DECRYPT)
     *  * Creation date
     *  * tags
     * 
     * @param keyId 
     * @returns KeyMetadata
     */
    async getKeyInfo(keyId: string): Promise<AwsKeyMetadata | null> {
        const input: DescribeKeyCommandInput = {
            KeyId: keyId
        } as DescribeKeyCommandInput;

        const command: DescribeKeyCommand = new DescribeKeyCommand(input);

        try {
            const result: DescribeKeyCommandOutput = await this.getKMSClient().send(command);
            this.logger.debug("AWS Key metadata:", result.KeyMetadata);
            const km: KeyMetadata = result.KeyMetadata!;
            const tags: AwsTag[] = await this.getKmsKeyTags(km.KeyId!);
            const awsKeyMetadata: AwsKeyMetadata = AwsKmsMapper.toAwsKeyMetadata(km,tags);
            return awsKeyMetadata;
        } catch (error) {
            if(error instanceof NotFoundException) {
                return null;
            }
            this.logger.error("Error while getting key from AWS:", error);
            throw error;
        }
    }

    async getKmsKeyTags(keyId: string): Promise<AwsTag[]> {
        const input: ListResourceTagsCommandInput = {
            KeyId: keyId
        } as ListResourceTagsCommandInput;

        const command: ListResourceTagsCommand = new ListResourceTagsCommand(input);
        try {
            const result:ListResourceTagsCommandOutput = await this.getKMSClient().send(command);
            this.logger.debug("Key tags for keyId:"+keyId, result);
            const tags: AwsTag[] = AwsKmsMapper.toAwsTags(result);
            return tags;
        } catch (error) {
            this.logger.error("Error getting key tags:", error);
            throw error;
        }
    }

    /**
     * Schedules a KMS key for deletion after a specified waiting period as 7 days.
     * 
     * @param keyId 
     * @returns ScheduleKeyDeletionCommandOutput
     */
    async deleteKey(keyId: string): Promise<ScheduleKeyDeletionCommandOutput> {
        const input: ScheduleKeyDeletionCommandInput = {
            KeyId: keyId,
            PendingWindowInDays: 7
        } as ScheduleKeyDeletionCommandInput;
        const command: ScheduleKeyDeletionCommand = new ScheduleKeyDeletionCommand(input);
        try {
            const result: ScheduleKeyDeletionCommandOutput = await this.getKMSClient().send(command);
            this.logger.debug("scheduled key delation:", result);
            return result;
        } catch (error) {
            this.logger.error("Error scheduling key deletion", error);
            throw error;
        }
    }

    /**
     *  Encrypts data using a KMS key. 
     *  The result is encoded as base64.
     * 
     * @param keyId 
     * @param data 
     * @returns string as base64 encoded
     */
    async encryptData(keyId: string, data: string): Promise<string> {
        const params: EncryptCommandInput = {
            KeyId: keyId,
            Plaintext: Buffer.from(data)
        } as EncryptCommandInput;

        const command: EncryptCommand = new EncryptCommand(params);

        try {
            const result: EncryptCommandOutput = await this.getKMSClient().send(command);
            const base64EncodedStr: string = AwsKmsMapper.toBase64(result.CiphertextBlob!);
            this.logger.debug("Encrypted data (base64):", base64EncodedStr);
            return base64EncodedStr;
        } catch (error) {
            this.logger.error("Error encrypting data:", error);
            throw error;
        }
    }

    /**
     * Decrypts data using a KMS key.
     * @param keyId 
     * @param base64Ciphertext 
     * @returns string
     */
    async decryptData(keyId: string, base64Ciphertext: string): Promise<string> {
        const ciphertextBlob = Buffer.from(base64Ciphertext, "base64");
        const params: DecryptCommandInput = {
            KeyId: keyId,
            CiphertextBlob: ciphertextBlob
        } as DecryptCommandInput;

        const command: DecryptCommand = new DecryptCommand(params);
        try {
            const result: DecryptCommandOutput = await this.getKMSClient().send(command);
            const plaintext = Buffer.from(result.Plaintext!).toString("utf-8");
            this.logger.debug("Decrypted plaintext:", plaintext);
            return plaintext;
        } catch (error) {
            this.logger.error("Error decrypting data:", error);
            throw error;
        }
    }

    /**
     * Generates a data key for a KMS key.
     * 
     * Allowed DataKeySpec values are AES_128 and AES_256.
     * 
     * @param keyId 
     * @param keySpec DataKeySpec 
     */
    async generateDataKey(generate: AwsDataKeyGenerate): Promise<AwsDataKey> {
        const input: GenerateDataKeyCommandInput = {
            KeyId: generate.keyId,
            KeySpec: generate.keySpec,
        } as GenerateDataKeyCommandInput;
        try {
            const result: GenerateDataKeyCommandOutput = await this.getKMSClient().send(new GenerateDataKeyCommand(input));
            const awsDataKey: AwsDataKey = AwsKmsMapper.toAwsDataKey(generate.keyId!, generate.keySpec!, result!);
            return awsDataKey;

        } catch (error) {
            this.logger.error("Error generating aws data key:", error);
            throw error;
        }
    }

    async generateDataKeyAES256(generate: AwsDataKeyGenerate): Promise<AwsDataKey> {
        generate.keySpec = DataKeySpec.AES_256;
        return await this.generateDataKey(generate);
    }

    async decryptDataKey(keyId:string, encryptedDataKeyBase64:string): Promise<Uint8Array> {
        const ciphertextBlob = Buffer.from(encryptedDataKeyBase64, "base64");
        const params: DecryptCommandInput = {
            CiphertextBlob: ciphertextBlob
        } as DecryptCommandInput;

        const command: DecryptCommand = new DecryptCommand(params);
        try {
            const result: DecryptCommandOutput = await this.getKMSClient().send(command);
            this.logger.debug("Data key decrypted successfully for key id:"+keyId);
            return result.Plaintext!;
        } catch (error) {
            this.logger.error("Error decrypting data key for key id:"+keyId, error);
            throw error;
            
        }
    }



    /**
     *  Creates an alias for a KMS key.
     * 
     *  @param create AwsKeyAliasCreate
     */
    validateAwsKeyAliasCreate(create: AwsKeyAliasCreate): void {
        if (!create) {
            throw new WPError(WP_ERROR_AWS_KMS_VALIDATION, "Keya alias model object can not be null while creating aws key alias.", undefined, WP_ERROR_AWS_KMS_VALIDATION.headerStatusCode + "");
        }
        if (!create.aliasName) {
            throw new WPError(WP_ERROR_AWS_KMS_VALIDATION, "Alias name is required to create aws key alias.", undefined, WP_ERROR_AWS_KMS_VALIDATION.headerStatusCode + "");
        }
        if (!create.targetKeyId) {
            throw new WPError(WP_ERROR_AWS_KMS_VALIDATION, "Target key id is required to create aws key alias.", undefined, WP_ERROR_AWS_KMS_VALIDATION.headerStatusCode + "");
        }
        // Ensure the alias name starts with "alias/"
        if (!create.aliasName.startsWith("alias/")) {
            throw new WPError(WP_ERROR_AWS_KMS_VALIDATION, 'Alias name must start with "alias/". For example, "alias/myKeyAlias".', undefined, WP_ERROR_AWS_KMS_VALIDATION.headerStatusCode + "");
        }
    }


    /**
     *  Creates an alias for a KMS key.
     * @param aliasName Specifies the alias name. This value must begin with alias/ followed by a name, such as alias/ExampleAlias.
     * @param targetKeyId 
     */
    async createAlias(create: AwsKeyAliasCreate): Promise<AwsKeyAlias> {

        this.validateAwsKeyAliasCreate(create);;

        const input: CreateAliasCommandInput = {
            AliasName: create.aliasName,
            TargetKeyId: create.targetKeyId,
        } as CreateAliasCommandInput;

        try {
            const result: CreateAliasCommandOutput = await this.getKMSClient().send(new CreateAliasCommand(input));
            this.logger.debug(`Alias "${create.aliasName}" created successfully for key ${create.targetKeyId}`);
            return AwsKmsMapper.toAwsKeyAlias(create.aliasName, create.targetKeyId, result); ;
        } catch (error) {
            this.logger.error("Error creating alias:", error);
            throw error;
        }
    };

}
