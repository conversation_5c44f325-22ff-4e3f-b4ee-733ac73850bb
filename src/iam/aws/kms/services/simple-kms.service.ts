import { Injectable, Logger } from "@nestjs/common";
import { randomUUID } from "crypto";
import { Aws<PERSON>ey, AwsKeyCreate, AwsKeyGenerate } from "../models/aws-key";
import { AwsKeyMetadata } from "../models/aws-key-metadata";
import { Aws<PERSON><PERSON><PERSON><PERSON>, AwsD<PERSON><PERSON>eyGenerate } from "../models/aws-data-key";
import { AwsKeyAlias, AwsKeyAliasCreate } from "../models/aws-key.alias";
import { DataKeySpec, KeySpec, KeyUsageType, ScheduleKeyDeletionCommandOutput } from "@aws-sdk/client-kms";

@Injectable()
export class SimpleKmsService {
    private logger = new Logger(SimpleKmsService.name);
    private keyStore: Map<string, any> = new Map();
    private aliasStore: Map<string, string> = new Map();
    private dataKeyStore: Map<string, Buffer> = new Map();

    /**
     * Creates a mock KMS key and stores it in the in-memory key store.
     * @param params AwsKeyCreate
     * @returns Mocked Key Metadata
     */
    async createKey(params: AwsKeyCreate): Promise<AwsKeyMetadata> {
        const keyId = randomUUID();
        const keyArn = `arn:aws:kms:mock-region:mock-account-id:key/${keyId}`;
        const metadata = {
            KeyId: keyId,
            Arn: keyArn,
            Description: params.Description,
            KeySpec: params.KeySpec,
            KeyUsage: params.KeyUsage,
            CreationDate: new Date(),
        };

        this.keyStore.set(keyId, { ...metadata, Tags: params.Tags });
        this.logger.debug("Mock key created:", metadata);

        return metadata as AwsKeyMetadata;
    }

    /**
     * Generates a mock data encryption key and stores it in the data key store.
     * @param generate AwsDataKeyGenerate
     * @returns Mocked Data Key
     */
    async generateDataKey(generate: AwsDataKeyGenerate): Promise<AwsDataKey> {
        const dataKeyId = randomUUID();
        const plaintextKey = Buffer.from(randomUUID());
        const encryptedKey = Buffer.from(randomUUID());

        this.dataKeyStore.set(dataKeyId, plaintextKey);

        const awsDataKey: AwsDataKey = {
            keyId: generate.keyId,
            keySpec: generate.keySpec!,
            dataKey: plaintextKey.toString("base64"),
            encryptedDataKey: encryptedKey.toString("base64"),
            createdAt: new Date(),
        };

        this.logger.debug("Mock data key generated:", awsDataKey);

        return awsDataKey;
    }

    async generateDataKeyAES256(generate: AwsDataKeyGenerate): Promise<AwsDataKey> {
        generate.keySpec = DataKeySpec.AES_256;
        return await this.generateDataKey(generate);
    }

    async decryptDataKey(keyId:string, encryptedDataKeyBase64:string): Promise<Uint8Array> {
       return Buffer.from(encryptedDataKeyBase64, "base64");
    }

    /**
     * Encrypts data using a mock key.
     * @param keyId 
     * @param data 
     * @returns Encrypted data (mocked as base64 string)
     */
    async encryptData(keyId: string, data: string): Promise<string> {
        const encryptedData = Buffer.from(data).toString("base64");
        this.logger.debug("Mock encrypted data:", encryptedData);
        return encryptedData;
    }

    /**
     * Decrypts data using a mock key.
     * @param keyId 
     * @param base64Ciphertext 
     * @returns Decrypted plaintext
     */
    async decryptData(keyId: string, base64Ciphertext: string): Promise<string> {
        const decryptedData = Buffer.from(base64Ciphertext, "base64").toString("utf-8");
        this.logger.debug("Mock decrypted data:", decryptedData);
        return decryptedData;
    }

    /**
     * Creates an alias for a mock key.
     * @param create AwsKeyAliasCreate
     * @returns Mocked Key Alias
     */
    async createAlias(create: AwsKeyAliasCreate): Promise<AwsKeyAlias> {
        const aliasName = create.aliasName.startsWith("alias/") ? create.aliasName : `alias/${create.aliasName}`;
        this.aliasStore.set(aliasName, create.targetKeyId);

        const alias = {
            aliasName,
            targetKeyId: create.targetKeyId,
        };

        this.logger.debug(`Mock alias created: ${aliasName} for key ${create.targetKeyId}`);

        return alias as AwsKeyAlias;
    }

    /**
     * Gets metadata for a mock key.
     * @param keyId 
     * @returns Mocked Key Metadata
     */
    async getKeyInfo(keyId: string): Promise<AwsKeyMetadata | null> {
        if (!this.keyStore.has(keyId)) {
            this.logger.error(`Mock key not found: ${keyId}`);
            return null;
        }

        const keyInfo = this.keyStore.get(keyId);
        this.logger.debug("Mock key metadata:", keyInfo);

        return keyInfo as AwsKeyMetadata;
    }

    /**
     * Schedules a mock key for deletion (mock operation).
     * @param keyId 
     * @returns Mock success message
     */
    async deleteKey(keyId: string): Promise<ScheduleKeyDeletionCommandOutput> {
        if (!this.keyStore.has(keyId)) {
            throw new Error(`Mock key not found: ${keyId}`);
        }

        this.keyStore.delete(keyId);
        this.logger.debug(`Mock key deleted: ${keyId}`);

        return { KeyId: keyId, DeletionDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), $metadata: {} };
    }
}
