import { Test, TestingModule } from '@nestjs/testing';
import { AwsKmsService } from './aws-kms.sevice';

import {
    KMSClient, CreateKeyCommandOutput, DescribeKeyCommandOutput, KeyUsageType,
    NotFoundException, KeyMetadata, ScheduleKeyDeletionCommandOutput,
    EncryptCommandOutput, DecryptCommandOutput, GenerateDataKeyCommandOutput,
} from '@aws-sdk/client-kms';
import { AwsKeyCreate } from '../models/aws-key';
import { AwsKeyAlias, AwsKeyAliasCreate } from '../models/aws-key.alias';
import { AwsKmsMapper } from '../utils/aws-kms.mapper';
import { AwsKeyMetadata } from '../models/aws-key-metadata';
import { AwsDataKey, AwsDataKeyGenerate } from '../models/aws-data-key';


/**
 * Mock the AWS KMS client
 */
jest.mock('@aws-sdk/client-kms');

/**
 * Will be assigned mockKMSClient to the AwsKmsService
 */
const mockKMSClient = {
    send: jest.fn(),
};

/**
 * Mock KMSClient's KeyMetadata
 */
const mockKeyMetadata: KeyMetadata = {
    KeyId: 'test-key-id',
    Arn: 'arn:aws:kms:region:account:key/test-key-id',
    Description: 'Test Key',
    KeyUsage: KeyUsageType.ENCRYPT_DECRYPT,
    CreationDate: new Date(),
} as KeyMetadata;

const mockTags = [
    { TagKey: 'walletId', TagValue: 'wallet1' },
    { TagKey: 'userId', TagValue: 'user1' },
];

describe('AwsKmsService', () => {
    let service: AwsKmsService;

    beforeEach(async () => {
        jest.clearAllMocks();

        const module: TestingModule = await Test.createTestingModule({
            providers: [AwsKmsService],
        }).compile();

        service = module.get<AwsKmsService>(AwsKmsService);

        // Override the KMSClient instance with a mocked version
        (service as any).kmsClient = mockKMSClient;
    });

    it('should be defined', () => {
        expect(service).toBeDefined();
    });

    describe('createKey', () => {
        it('should create a key and return its metadata', async () => {
            const mockCreateKeyOutput: CreateKeyCommandOutput = {
                $metadata: {},
                KeyMetadata: mockKeyMetadata,
            };

            mockKMSClient.send.mockResolvedValue(mockCreateKeyOutput);

            const keyParams: AwsKeyCreate = {
                Description: 'Test Key',
                KeySpec: 'SYMMETRIC_DEFAULT',
                KeyUsage: 'ENCRYPT_DECRYPT',
                MultiRegion: false,
                Tags: mockTags,
            };

            const result: AwsKeyMetadata = await service.createKey(keyParams);

            expect(mockKMSClient.send).toHaveBeenCalledTimes(1);
            expect(result).toBeDefined();
            expect(result.KeyId).toEqual(mockKeyMetadata.KeyId);
            expect(result.tags).toBeDefined();
            expect(result.tags).toEqual(mockTags);
        });

        it('should throw an error if AWS returns an error', async () => {
            mockKMSClient.send.mockRejectedValue(new Error('AWS Error'));

            const keyParams: AwsKeyCreate = {
                Description: 'Test Key',
                KeySpec: 'SYMMETRIC_DEFAULT',
                KeyUsage: 'ENCRYPT_DECRYPT',
                MultiRegion: false,
                Tags: mockTags,
            };

            await expect(service.createKey(keyParams)).rejects.toThrow('AWS Error');
        });
    });

    describe('getKeyInfo', () => {
        it('should return key metadata for a valid key ID', async () => {
            const mockDescribeKeyOutput: DescribeKeyCommandOutput = {
                $metadata: {},
                KeyMetadata: mockKeyMetadata,
            } as DescribeKeyCommandOutput;

            mockKMSClient.send.mockResolvedValue(mockDescribeKeyOutput);

            jest.spyOn(service, 'getKmsKeyTags').mockResolvedValue(mockTags);

            const result:AwsKeyMetadata | null = await service.getKeyInfo('test-key-id');

            expect(mockKMSClient.send).toHaveBeenCalledTimes(1);
            expect(result!.KeyId).toEqual(mockKeyMetadata.KeyId);
            expect(result!.tags).toBeDefined();
            expect(result!.tags).toEqual(mockTags);
        });

        it('should return null for a non-existent key ID', async () => {
            mockKMSClient.send.mockRejectedValue(new NotFoundException({ message: "NotFoundException", $metadata: {} }));

            const result:AwsKeyMetadata | null = await service.getKeyInfo('non-existent-key-id');

            expect(result).toBeNull();
        });

        it('should throw an error for other AWS errors', async () => {
            mockKMSClient.send.mockRejectedValue(new Error('AWS Error'));

            await expect(service.getKeyInfo('invalid-key-id')).rejects.toThrow('AWS Error');
        });
    });

    describe('createAlias', () => {
        it('should create an alias for a key', async () => {
            const aliasParams: AwsKeyAliasCreate = {
                aliasName: 'alias/TestAlias',
                targetKeyId: 'test-key-id',
            };

            mockKMSClient.send.mockResolvedValue({ $metadata: {} });

            const result:AwsKeyAlias = await service.createAlias(aliasParams);

            expect(mockKMSClient.send).toHaveBeenCalledTimes(1);
            expect(result.aliasName).toEqual(aliasParams.aliasName);
            expect(result.targetKeyId).toEqual(aliasParams.targetKeyId);
            expect(result.createdAt).toBeDefined();
        });

        it('should throw an error if alias creation fails', async () => {
            mockKMSClient.send.mockRejectedValue(new Error('AWS Error'));

            const aliasParams: AwsKeyAliasCreate = {
                aliasName: 'alias/TestAlias',
                targetKeyId: 'test-key-id',
            };

            await expect(service.createAlias(aliasParams)).rejects.toThrow('AWS Error');
        });
    });

    describe('deleteKey', () => {
        it('should schedule a key for deletion', async () => {
            const mockDeleteKeyOutput: ScheduleKeyDeletionCommandOutput = {
                $metadata: {},
                DeletionDate: new Date(),
            };

            mockKMSClient.send.mockResolvedValue(mockDeleteKeyOutput);

            const result = await service.deleteKey('test-key-id');

            expect(mockKMSClient.send).toHaveBeenCalledTimes(1);
            expect(result.DeletionDate).toBeDefined();
        });

        it('should throw an error if scheduling deletion fails', async () => {
            mockKMSClient.send.mockRejectedValue(new Error('AWS Error'));

            await expect(service.deleteKey('test-key-id')).rejects.toThrow('AWS Error');
        });
    });

    describe('encryptData', () => {
        it('should encrypt data and return a base64 string', async () => {
            const mockEncryptOutput: EncryptCommandOutput = {
                $metadata: {},
                CiphertextBlob: Buffer.from('encrypted-data'),
            };

            mockKMSClient.send.mockResolvedValue(mockEncryptOutput);

            const result = await service.encryptData('test-key-id', 'test-data');

            expect(mockKMSClient.send).toHaveBeenCalledTimes(1);
            expect(result).toEqual(Buffer.from('encrypted-data').toString('base64'));
        });

        it('should throw an error if encryption fails', async () => {
            mockKMSClient.send.mockRejectedValue(new Error('AWS Error'));

            await expect(service.encryptData('test-key-id', 'test-data')).rejects.toThrow('AWS Error');
        });
    });

    describe('decryptData', () => {
        it('should decrypt data and return a plaintext string', async () => {
            const mockDecryptOutput: DecryptCommandOutput = {
                $metadata: {},
                Plaintext: Buffer.from('test-data'),
            };

            mockKMSClient.send.mockResolvedValue(mockDecryptOutput);

            const result = await service.decryptData('test-key-id', Buffer.from('encrypted-data').toString('base64'));

            expect(mockKMSClient.send).toHaveBeenCalledTimes(1);
            expect(result).toEqual('test-data');
        });

        it('should throw an error if decryption fails', async () => {
            mockKMSClient.send.mockRejectedValue(new Error('AWS Error'));

            await expect(
                service.decryptData('test-key-id', Buffer.from('encrypted-data').toString('base64')),
            ).rejects.toThrow('AWS Error');
        });
    });

    describe('generateDataKey', () => {
        it('should generate a data key and return the key details', async () => {
            const mockGenerateDataKeyOutput: GenerateDataKeyCommandOutput = {
                $metadata: {},
                CiphertextBlob: Buffer.from('encrypted-data-key'),
                Plaintext: Buffer.from('plaintext-key'),
            };

            mockKMSClient.send.mockResolvedValue(mockGenerateDataKeyOutput);

            const generate: AwsDataKeyGenerate = new AwsDataKeyGenerate('test-key-id', 'AES_256');
            const result:AwsDataKey = await service.generateDataKey(generate);

            expect(mockKMSClient.send).toHaveBeenCalledTimes(1);
            expect(result).toBeDefined();
            expect(result.keyId).toEqual(generate.keyId);
            expect(result.keySpec).toEqual(generate.keySpec);
            expect(result.encryptedDataKey).toEqual(AwsKmsMapper.toBase64(mockGenerateDataKeyOutput.CiphertextBlob!));
            expect(result.dataKey).toEqual(AwsKmsMapper.toBase64(mockGenerateDataKeyOutput.Plaintext!));
        });

        it('should throw an error if data key generation fails', async () => {
            mockKMSClient.send.mockRejectedValue(new Error('AWS Error'));
            const generate: AwsDataKeyGenerate = new AwsDataKeyGenerate('test-key-id', 'AES_256');
            await expect(
                service.generateDataKey(generate),
            ).rejects.toThrow('AWS Error');
        });
    });
});
