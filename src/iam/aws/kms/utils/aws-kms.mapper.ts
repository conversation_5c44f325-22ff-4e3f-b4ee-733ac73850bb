import { CreateAliasCommandOutput, GenerateDataKeyCommandOutput, KeyMetadata, ListResourceTagsCommandOutput } from "@aws-sdk/client-kms";
import { AwsKeyMetadata } from "../models/aws-key-metadata";
import { AwsDataKey } from "../models/aws-data-key";
import { AwsKeyAlias } from "../models/aws-key.alias";
import { AwsTag } from "../../commons/models/aws.tag";

export class AwsKmsMapper {

    /**
     *  Converts KeyMetadata to AwsKeyMetadata
     * @param km 
     * @returns AwsKeyMetadata | null
     */
    public static toAwsKeyMetadata(km: KeyMetadata, tags:AwsTag[]): AwsKeyMetadata {
        if (!km) {
            return new AwsKeyMetadata("", "", new Date(), true, "", "", "", new Date(), new Date(), "", [], [], [], false, 0, []);
        }
        const awsKeyMetadata: AwsKeyMetadata = new AwsKeyMetadata(
            km.KeyId!, km.Arn!, km.CreationDate!, km.Enabled!,
            km.Description!, km.KeyUsage!, km.KeyState!,
            km.DeletionDate!, km.ValidTo!, km.KeySpec!,
            km.EncryptionAlgorithms!, km.SigningAlgorithms!, km.KeyAgreementAlgorithms!,
            km.MultiRegion!, km.PendingDeletionWindowInDays!,
            tags
        );
        return awsKeyMetadata;
    }

    public static toAwsDataKey(keyId: string, keySpec: string, result: GenerateDataKeyCommandOutput): AwsDataKey {
        if (!result) {
            return new AwsDataKey(keyId, keySpec, "", "", new Date());
        }
        const plaintextDataKeyBase64 = AwsKmsMapper.toBase64(result.Plaintext!);
        const encryptedDataKeyBase64 = AwsKmsMapper.toBase64(result.CiphertextBlob!);
        const dataKey: AwsDataKey = new AwsDataKey(keyId, keySpec, plaintextDataKeyBase64, encryptedDataKeyBase64, new Date());
        return dataKey;
    }

    public static toAwsKeyAlias(aliasName:string, targetKeyId:string, result: CreateAliasCommandOutput): AwsKeyAlias {
        const alias:AwsKeyAlias = new AwsKeyAlias(aliasName, targetKeyId, new Date);
        return alias;
    }

    public static toAwsTags(result:ListResourceTagsCommandOutput): AwsTag[] {
        if(result == null) {
            return [];
        }
        return result.Tags!.map(tag => {
            return new AwsTag(tag.TagKey!, tag.TagValue!);
        });
    }

    public static toBase64(data: Uint8Array): string {
        if(data == null) {
            return "";
        }
        return Buffer.from(data).toString("base64");
    };

}