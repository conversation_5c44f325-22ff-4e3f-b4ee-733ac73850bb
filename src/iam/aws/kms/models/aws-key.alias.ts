export class AwsKeyAliasCreate {
    aliasName: string;
    targetKeyId: string;

    constructor(aliasName: string, targetKeyId: string) {
        this.aliasName = aliasName;
        this.targetKeyId = targetKeyId;
    }
}   

export class AwsKeyAlias extends AwsKeyAliasCreate{

    createdAt: Date;

    constructor(aliasName: string, targetKeyId: string, createdAt: Date) {
       super(aliasName, targetKeyId);
       this.createdAt = createdAt;
    }
}   