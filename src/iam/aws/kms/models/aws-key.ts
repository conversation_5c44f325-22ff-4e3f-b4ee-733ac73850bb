import { WPError } from "../../../../commons/error/wp.error";
import { AwsTag } from "../../commons/models/aws.tag";
import { AwsKeyMetadata } from "./aws-key-metadata";
import { WP_ERROR_AWS_KMS_VALIDATION } from "../../../../commons/error/error.codes";

export abstract class AwsKeyBase {

    keyAlias: string;

    constructor(keyAlias: string) {
        this.keyAlias = keyAlias;
    }

    validateKeyAlias() {
        if (!this.keyAlias) {
            throw new WPError(WP_ERROR_AWS_KMS_VALIDATION, "Key alias is required.", undefined, WP_ERROR_AWS_KMS_VALIDATION.headerStatusCode + "");
        }

        if (!this.keyAlias.startsWith("alias/")) {
            throw new WPError(WP_ERROR_AWS_KMS_VALIDATION, "Key alias name should start with 'alias/'.", undefined, WP_ERROR_AWS_KMS_VALIDATION.headerStatusCode + "");
        }
    }
}

/**
 * Model class to generate a new KMS key.
 * 
 */
export class AwsKeyGenerate extends AwsKeyBase {

    description?: string;
    tags?: AwsTag[];

    constructor(keyAlias:string, description?: string, tags?: AwsTag[]) {
        super(keyAlias);
        this.description = description;
        this.tags = tags;
        this.validateKeyAlias();
    }
}

/**
 * Model class to represent a KMS key.
 */
export class AwsKey extends AwsKeyBase {

    metadata: AwsKeyMetadata;

    constructor(keyAlias: string, metadata: AwsKeyMetadata) {
        super(keyAlias);
        this.metadata = metadata;
        this.validateKeyAlias();
    }
}
/**
 *  Model class to create a new KMS key.
 */
export class AwsKeyCreate {
    /**
     * <p>A description of the KMS key. Use a description that helps you decide whether the KMS key
     *       is appropriate for a task. The default value is an empty string (no description).</p>
     *          <important>
     *             <p>Do not include confidential or sensitive information in this field. This field may be displayed in plaintext in CloudTrail logs and other output.</p>
     *          </important>
     *          <p>To set or change the description after the key is created, use <a>UpdateKeyDescription</a>.</p>
     * @public
     */
    Description?: string | undefined;

    /**
    * <p>Determines the <a href="https://docs.aws.amazon.com/kms/latest/developerguide/concepts.html#cryptographic-operations">cryptographic operations</a> for which you can use the KMS key. The default value is
    *         <code>ENCRYPT_DECRYPT</code>. This parameter is optional when you are creating a symmetric
    *       encryption KMS key; otherwise, it is required. You can't change the <code>KeyUsage</code>
    *       value after the KMS key is created.</p>
    *          <p>Select only one valid value.</p>
    *          <ul>
    *             <li>
    *                <p>For symmetric encryption KMS keys, omit the parameter or specify
    *             <code>ENCRYPT_DECRYPT</code>.</p>
    *             </li>
    *             <li>
    *                <p>For HMAC KMS keys (symmetric), specify <code>GENERATE_VERIFY_MAC</code>.</p>
    *             </li>
    *             <li>
    *                <p>For asymmetric KMS keys with RSA key pairs, specify <code>ENCRYPT_DECRYPT</code> or
    *             <code>SIGN_VERIFY</code>.</p>
    *             </li>
    *             <li>
    *                <p>For asymmetric KMS keys with NIST-recommended elliptic curve key pairs, specify
    *           <code>SIGN_VERIFY</code> or <code>KEY_AGREEMENT</code>.</p>
    *             </li>
    *             <li>
    *                <p>For asymmetric KMS keys with <code>ECC_SECG_P256K1</code> key pairs specify
    *           <code>SIGN_VERIFY</code>.</p>
    *             </li>
    *             <li>
    *                <p>For asymmetric KMS keys with SM2 key pairs (China Regions only), specify
    *             <code>ENCRYPT_DECRYPT</code>, <code>SIGN_VERIFY</code>, or <code>KEY_AGREEMENT</code>.</p>
    *             </li>
    *          </ul>
    * @public
    */
    KeyUsage?: string | undefined;

    /**
* <p>Specifies the type of KMS key to create. The default value,
*       <code>SYMMETRIC_DEFAULT</code>, creates a KMS key with a 256-bit AES-GCM key that is used for
*       encryption and decryption, except in China Regions, where it creates a 128-bit symmetric key
*       that uses SM4 encryption. For help choosing a key spec for your KMS key, see <a href="https://docs.aws.amazon.com/kms/latest/developerguide/key-types.html#symm-asymm-choose">Choosing a KMS key type</a> in the <i>
*                <i>Key Management Service Developer Guide</i>
*             </i>.</p>
*          <p>The <code>KeySpec</code> determines whether the KMS key contains a symmetric key or an
*       asymmetric key pair. It also determines the algorithms that the KMS key supports. You can't
*       change the <code>KeySpec</code> after the KMS key is created. To further restrict the
*       algorithms that can be used with the KMS key, use a condition key in its key policy or IAM
*       policy. For more information, see <a href="https://docs.aws.amazon.com/kms/latest/developerguide/policy-conditions.html#conditions-kms-encryption-algorithm">kms:EncryptionAlgorithm</a>, <a href="https://docs.aws.amazon.com/kms/latest/developerguide/policy-conditions.html#conditions-kms-mac-algorithm">kms:MacAlgorithm</a> or <a href="https://docs.aws.amazon.com/kms/latest/developerguide/policy-conditions.html#conditions-kms-signing-algorithm">kms:Signing Algorithm</a> in the <i>
*                <i>Key Management Service Developer Guide</i>
*             </i>.</p>
*          <important>
*             <p>
*                <a href="http://aws.amazon.com/kms/features/#AWS_Service_Integration">Amazon Web Services services that
*           are integrated with KMS</a> use symmetric encryption KMS keys to protect your data.
*         These services do not support asymmetric KMS keys or HMAC KMS keys.</p>
*          </important>
*          <p>KMS supports the following key specs for KMS keys:</p>
*          <ul>
*             <li>
*                <p>Symmetric encryption key (default)</p>
*                <ul>
*                   <li>
*                      <p>
*                         <code>SYMMETRIC_DEFAULT</code>
*                      </p>
*                   </li>
*                </ul>
*             </li>
*             <li>
*                <p>HMAC keys (symmetric)</p>
*                <ul>
*                   <li>
*                      <p>
*                         <code>HMAC_224</code>
*                      </p>
*                   </li>
*                   <li>
*                      <p>
*                         <code>HMAC_256</code>
*                      </p>
*                   </li>
*                   <li>
*                      <p>
*                         <code>HMAC_384</code>
*                      </p>
*                   </li>
*                   <li>
*                      <p>
*                         <code>HMAC_512</code>
*                      </p>
*                   </li>
*                </ul>
*             </li>
*             <li>
*                <p>Asymmetric RSA key pairs (encryption and decryption -or- signing and verification)</p>
*                <ul>
*                   <li>
*                      <p>
*                         <code>RSA_2048</code>
*                      </p>
*                   </li>
*                   <li>
*                      <p>
*                         <code>RSA_3072</code>
*                      </p>
*                   </li>
*                   <li>
*                      <p>
*                         <code>RSA_4096</code>
*                      </p>
*                   </li>
*                </ul>
*             </li>
*             <li>
*                <p>Asymmetric NIST-recommended elliptic curve key pairs (signing and verification -or- deriving shared secrets)</p>
*                <ul>
*                   <li>
*                      <p>
*                         <code>ECC_NIST_P256</code> (secp256r1)</p>
*                   </li>
*                   <li>
*                      <p>
*                         <code>ECC_NIST_P384</code> (secp384r1)</p>
*                   </li>
*                   <li>
*                      <p>
*                         <code>ECC_NIST_P521</code> (secp521r1)</p>
*                   </li>
*                </ul>
*             </li>
*             <li>
*                <p>Other asymmetric elliptic curve key pairs (signing and verification)</p>
*                <ul>
*                   <li>
*                      <p>
*                         <code>ECC_SECG_P256K1</code> (secp256k1), commonly used for
*               cryptocurrencies.</p>
*                   </li>
*                </ul>
*             </li>
*             <li>
*                <p>SM2 key pairs (encryption and decryption -or- signing and verification -or- deriving shared secrets)</p>
*                <ul>
*                   <li>
*                      <p>
*                         <code>SM2</code> (China Regions only)</p>
*                   </li>
*                </ul>
*             </li>
*          </ul>
* @public
*/
    KeySpec?: string | undefined;

    /**
     * <p>Assigns one or more tags to the KMS key. Use this parameter to tag the KMS key when it is
     *       created. To tag an existing KMS key, use the <a>TagResource</a> operation.</p>
     *          <important>
     *             <p>Do not include confidential or sensitive information in this field. This field may be displayed in plaintext in CloudTrail logs and other output.</p>
     *          </important>
     *          <note>
     *             <p>Tagging or untagging a KMS key can allow or deny permission to the KMS key. For details, see <a href="https://docs.aws.amazon.com/kms/latest/developerguide/abac.html">ABAC for KMS</a> in the <i>Key Management Service Developer Guide</i>.</p>
     *          </note>
     *          <p>To use this parameter, you must have <a href="https://docs.aws.amazon.com/kms/latest/developerguide/kms-api-permissions-reference.html">kms:TagResource</a> permission in an IAM policy.</p>
     *          <p>Each tag consists of a tag key and a tag value. Both the tag key and the tag value are
     *       required, but the tag value can be an empty (null) string. You cannot have more than one tag
     *       on a KMS key with the same tag key. If you specify an existing tag key with a different tag
     *       value, KMS replaces the current tag value with the specified one.</p>
     *          <p>When you add tags to an Amazon Web Services resource, Amazon Web Services generates a cost allocation
     *               report with usage and costs aggregated by tags. Tags can also be used to control access to a KMS key. For details,
     *               see <a href="https://docs.aws.amazon.com/kms/latest/developerguide/tagging-keys.html">Tagging Keys</a>.</p>
     * @public
     */
    Tags?: AwsTag[] | undefined;

    /**
     * <p>Creates a multi-Region primary key that you can replicate into other Amazon Web Services Regions. You
     *       cannot change this value after you create the KMS key. </p>
     *          <p>For a multi-Region key, set this parameter to <code>True</code>. For a single-Region KMS
     *       key, omit this parameter or set it to <code>False</code>. The default value is
     *         <code>False</code>.</p>
     *          <p>This operation supports <i>multi-Region keys</i>, an KMS feature that lets you create multiple
     *       interoperable KMS keys in different Amazon Web Services Regions. Because these KMS keys have the same key ID, key
     *       material, and other metadata, you can use them interchangeably to encrypt data in one Amazon Web Services Region and decrypt
     *       it in a different Amazon Web Services Region without re-encrypting the data or making a cross-Region call. For more information about multi-Region keys, see <a href="https://docs.aws.amazon.com/kms/latest/developerguide/multi-region-keys-overview.html">Multi-Region keys in KMS</a> in the <i>Key Management Service Developer Guide</i>.</p>
     *          <p>This value creates a <i>primary key</i>, not a replica. To create a
     *         <i>replica key</i>, use the <a>ReplicateKey</a> operation. </p>
     *          <p>You can create a symmetric or asymmetric multi-Region key, and you can create a
     *       multi-Region key with imported key material. However, you cannot create a multi-Region key in
     *       a custom key store.</p>
     * @public
     */
    MultiRegion?: boolean | undefined;
}