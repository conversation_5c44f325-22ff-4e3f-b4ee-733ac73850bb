export class AwsDataKeyGenerate {
    keyId: string;
    keySpec?: string;

    constructor(keyId: string, keySpec?: string) {
        this.keyId = keyId;
        this.keySpec = keySpec;
    }

}

/**
 * Model for AWS Data Key
 */
export class AwsDataKey {
    keyId: string;
    keySpec: string;
    dataKey: string; // base64 encoded
    encryptedDataKey: string; // base64 encoded
    createdAt: Date;

    constructor(keyId: string, keySpec: string, dataKey: string, encryptedDataKey: string, createdAt: Date) {
        this.keyId = keyId;
        this.keySpec = keySpec;
        this.dataKey = dataKey;
        this.encryptedDataKey = encryptedDataKey;
        this.createdAt = createdAt;
    }
}