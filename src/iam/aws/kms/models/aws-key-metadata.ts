import { AwsTag } from "../../commons/models/aws.tag";

/**
 * <p>Contains metadata about a KMS key.</p>
 */
export class AwsKeyMetadata {
    /**
     * <p>The globally unique identifier for the KMS key.</p>
     * @public
     */
    KeyId: string | undefined;
    /**
     * <p>The Amazon Resource Name (ARN) of the KMS key. For examples, see <a href="https://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html#arn-syntax-kms">Key Management Service (KMS)</a> in the Example ARNs section of the <i>Amazon Web Services General
     *         Reference</i>.</p>
     * @public
     */
    Arn?: string | undefined;
    /**
     * <p>The date and time when the KMS key was created.</p>
     * @public
     */
    CreationDate?: Date | undefined;
    /**
     * <p>Specifies whether the KMS key is enabled. When <code>KeyState</code> is
     *         <code>Enabled</code> this value is true, otherwise it is false.</p>
     * @public
     */
    Enabled?: boolean | undefined;
    /**
     * <p>The description of the KMS key.</p>
     * @public
     */
    Description?: string | undefined;

    /**
     * <p>The current status of the KMS key.</p>
     *          <p>For more information about how key state affects the use of a KMS key, see <a href="https://docs.aws.amazon.com/kms/latest/developerguide/key-state.html">Key states of KMS keys</a> in
     *       the <i>Key Management Service Developer Guide</i>.</p>
     * @public
     */
    KeyUsage?: string | undefined;

    /**
     * <p>The current status of the KMS key.</p>
     *          <p>For more information about how key state affects the use of a KMS key, see <a href="https://docs.aws.amazon.com/kms/latest/developerguide/key-state.html">Key states of KMS keys</a> in
     *       the <i>Key Management Service Developer Guide</i>.</p>
     * @public
     */
    KeyState?: string | undefined;
    /**
     * <p>The date and time after which KMS deletes this KMS key. This value is present only when
     *       the KMS key is scheduled for deletion, that is, when its <code>KeyState</code> is
     *         <code>PendingDeletion</code>.</p>
     *          <p>When the primary key in a multi-Region key is scheduled for deletion but still has replica
     *       keys, its key state is <code>PendingReplicaDeletion</code> and the length of its waiting
     *       period is displayed in the <code>PendingDeletionWindowInDays</code> field.</p>
     * @public
     */
    DeletionDate?: Date | undefined;
    /**
     * <p>The time at which the imported key material expires. When the key material expires, KMS
     *       deletes the key material and the KMS key becomes unusable. This value is present only for KMS
     *       keys whose <code>Origin</code> is <code>EXTERNAL</code> and whose <code>ExpirationModel</code>
     *       is <code>KEY_MATERIAL_EXPIRES</code>, otherwise this value is omitted.</p>
     * @public
     */
    ValidTo?: Date | undefined;

     /**
     * <p>Describes the type of key material in the KMS key.</p>
     * @public
     */
     KeySpec?: string | undefined;
     /**
      * <p>The encryption algorithms that the KMS key supports. You cannot use the KMS key with other
      *       encryption algorithms within KMS.</p>
      *          <p>This value is present only when the <code>KeyUsage</code> of the KMS key is
      *         <code>ENCRYPT_DECRYPT</code>.</p>
      * @public
      */
     EncryptionAlgorithms?: string[] | undefined;
     /**
      * <p>The signing algorithms that the KMS key supports. You cannot use the KMS key with other
      *       signing algorithms within KMS.</p>
      *          <p>This field appears only when the <code>KeyUsage</code> of the KMS key is
      *         <code>SIGN_VERIFY</code>.</p>
      * @public
      */
     SigningAlgorithms?: string[] | undefined;
     /**
      * <p>The key agreement algorithm used to derive a shared secret.</p>
      * @public
      */
     KeyAgreementAlgorithms?: string[] | undefined;
     /**
      * <p>Indicates whether the KMS key is a multi-Region (<code>True</code>) or regional
      *         (<code>False</code>) key. This value is <code>True</code> for multi-Region primary and
      *       replica keys and <code>False</code> for regional KMS keys.</p>
      *          <p>For more information about multi-Region keys, see <a href="https://docs.aws.amazon.com/kms/latest/developerguide/multi-region-keys-overview.html">Multi-Region keys in KMS</a> in the <i>Key Management Service Developer Guide</i>.</p>
      * @public
      */
     MultiRegion?: boolean | undefined;

     PendingDeletionWindowInDays?: number | undefined;

     tags:AwsTag[] = [];

     constructor(KeyId: string, Arn: string, CreationDate: Date, Enabled: boolean, Description: string, KeyUsage: string, 
        KeyState: string, DeletionDate: Date, ValidTo: Date, KeySpec: string, EncryptionAlgorithms: string[], 
        SigningAlgorithms: string[], KeyAgreementAlgorithms: string[], MultiRegion: boolean, PendingDeletionWindowInDays: number, tags:AwsTag[]) {    
         this.KeyId = KeyId;
         this.Arn = Arn;
         this.CreationDate = CreationDate;
         this.Enabled = Enabled;
         this.Description = Description;
         this.KeyUsage = KeyUsage;
         this.KeyState = KeyState;
         this.DeletionDate = DeletionDate;
         this.ValidTo = ValidTo;
         this.KeySpec = KeySpec;
         this.EncryptionAlgorithms = EncryptionAlgorithms;
         this.SigningAlgorithms = SigningAlgorithms;
         this.KeyAgreementAlgorithms = KeyAgreementAlgorithms;
         this.MultiRegion = MultiRegion;
         this.PendingDeletionWindowInDays = PendingDeletionWindowInDays;
         this.tags = tags;
     }
}