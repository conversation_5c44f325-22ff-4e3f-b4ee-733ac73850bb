import { Body, Controller, Injectable, Logger, Post, UseGuards } from "@nestjs/common";
import { ApiOperation, ApiResponse, ApiTags } from "@nestjs/swagger";
import { Roles } from "../../../commons/auth/roles.decarator";
import { Role } from "../../../commons/auth/role.enum";
import { UserService } from "../services/user.service";
import { AuthenticationResultType } from "@aws-sdk/client-cognito-identity-provider";
import { MobileLoginRequest, RefreshTokenRequest, MobileLoginResponse, MobileAppleRequest } from "../utils/custom-auth-utils";
import { UserKycShuftiproDto } from "../dtos/user.kyc.dto";
import { RecaptchaGuard } from "../../../commons/guards/recaptcha.guard";

@ApiTags("mobile")
@Controller("mobile")
@Injectable()
export class MobileController {
    private readonly logger = new Logger(MobileController.name);

    constructor(private readonly userService: UserService) {}

    @ApiOperation({ summary: "Process mobile app login" })
    @ApiResponse({ status: 200, description: "Mobile login processed successfully" })
    @ApiResponse({ status: 403, description: "Forbidden." })
    @Post("/slogin")
    @Roles(Role.full_access)
    async processMobileLogin(@Body() mobileLoginRequest: MobileLoginRequest){
        return await this.userService.processMobileLoginRegister(mobileLoginRequest.googleOauthToken, mobileLoginRequest.appDef);
    }

    @ApiOperation({ summary: "Refresh token" })
    @ApiResponse({ status: 200, description: "Refresh token processed successfully" })
    @ApiResponse({ status: 403, description: "Forbidden." })
    @Post("/refresh-token")
    @Roles(Role.user)
    async refreshToken(@Body() refreshToken: RefreshTokenRequest) {
        return await this.userService.refreshToken(refreshToken.refreshToken, refreshToken.email);
    }

    @Post('/mobile/login-register')
    // @UseGuards(RecaptchaGuard)
    @Roles(Role.full_access)
    @ApiOperation({ summary: 'Mobile Login/Register with Google' })
    async mobileLoginRegister(@Body() mobileLoginRequest: MobileLoginRequest): Promise<MobileLoginResponse> {
      return await this.userService.processMobileLoginRegister(mobileLoginRequest.googleOauthToken, mobileLoginRequest.appDef);
    }

    @Post('/mobile/google-login-register')
    // @UseGuards(RecaptchaGuard)
    @Roles(Role.full_access)
    @ApiOperation({ summary: 'Mobile Login/Register with Google' })
    async mobileGoogleLoginRegister(@Body() mobileLoginRequest: MobileLoginRequest): Promise<MobileLoginResponse> {
      return await this.userService.processMobileLoginRegister(mobileLoginRequest.googleOauthToken, mobileLoginRequest.appDef);
    }

    @Post('/mobile/apple-login-register')
    // @UseGuards(RecaptchaGuard)
    @Roles(Role.full_access)
    @ApiOperation({ summary: 'Mobile Login/Register with Apple' })
    async mobileAppleLoginRegister(@Body() mobileAppleRequest: MobileAppleRequest): Promise<MobileLoginResponse> {
      return await this.userService.processMobileLoginRegisterApple(mobileAppleRequest.appleIdToken, mobileAppleRequest.appDef);
    }
}
