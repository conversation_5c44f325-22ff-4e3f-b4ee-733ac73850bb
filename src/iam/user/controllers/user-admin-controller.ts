import { Body, Controller, Delete, Headers, Injectable, Logger, Param, Post, Put } from "@nestjs/common";
import { ApiOperation, ApiResponse, ApiTags } from "@nestjs/swagger";
import { UserDto } from "../dtos/user.dto";
import { User, UserCreate } from "../entities/user.entity";
import { UserMapper } from "../utils/user.mapper";
import { UserService } from "../services/user.service";
import { UserCreateDto } from "../dtos/user-create.dto";
import { RESTUtil } from "../../../utils/rest-util";
import { WPError } from "../../../commons/error/wp.error";
import { WP_ERROR_IAM_USER_NOT_FOUND_BY_ID } from "../../../commons/error/error.codes";
import { UserRegistrationService } from "../services/user-registration.service";
import { Roles } from "../../../commons/auth/roles.decarator";
import { Role } from "../../../commons/auth/role.enum";
import { UserContext } from "../../../commons/auth/user-context";
import { BaseService } from "../../../commons/services/base.service";
import { ConfigService } from "@nestjs/config";
import { UserRepository } from "../repositories/user.repository";

@ApiTags("admin/users")
@Controller("admin/users")
@Injectable()
export class UserAdminController extends BaseService {
    private readonly logger = new Logger(UserAdminController.name);
    constructor(
        private readonly userService: UserService,
        private readonly userRepo: UserRepository,
        private readonly userRegistrationService: UserRegistrationService,
        private readonly confService: ConfigService,
    ) {
        super(confService);
    }

    @ApiOperation({ summary: "Creates a new user." })
    @ApiResponse({ status: 200, description: "A new user has been created successfully", type: UserDto })
    @ApiResponse({ status: 403, description: "Forbidden." })
    @Post("")
    @Roles(Role.admin)
    async createUser(@Headers() headers, @Body() dto: UserCreateDto): Promise<UserDto> {
        const userContext: UserContext = RESTUtil.resolveUserContex(headers);
        const userCreate: UserCreate = UserMapper.toUserCreate(dto, userContext);
        const user: User | undefined = await this.userService.create(userCreate, userContext);
        if (!user) {
            throw new WPError(WP_ERROR_IAM_USER_NOT_FOUND_BY_ID, `by id:${userCreate.id}`, undefined, "404");
        }
        const userDto: UserDto = await UserMapper.toUserDto(user);
        return userDto;
    }


    @ApiOperation({ summary: "Updates user by the given user id" })
    @ApiResponse({ status: 200, description: "User has been updated successfully", type: UserDto })
    @ApiResponse({ status: 403, description: "Forbidden." })
    @Put("/:id")
    @Roles(Role.admin)
    async updateUserRefCode(@Headers() headers, @Param("id") id: string, @Body() dto: { referralCode: string }): Promise<UserDto> {
        await this.checkUserContextRoles(headers, id, "updateUser");

        const userContext: UserContext = RESTUtil.resolveUserContex(headers);

        await this.userRepo.updateReferalCode(id, dto.referralCode);
        let user: User = await this.userService.findById(id, userContext);
        const userDto: UserDto = UserMapper.toUserDto(user);
        return userDto;
    }

    @ApiOperation({ summary: "Purges user data from the cloud and databse by the given user id" })
    @ApiResponse({ status: 200, description: "User has been purged successfully", type: UserDto })
    @ApiResponse({ status: 403, description: "Forbidden." })
    @Delete("/true_purge/:userId")
    @Roles(Role.admin)
    async truePurgeUser(@Headers() headers, @Param("userId") userId: string): Promise<void> {
        // await this.checkUserContextRoles(headers, userId, "purgeUser");

        const userContext: UserContext = RESTUtil.resolveUserContex(headers);
        try {
            let user = await this.userService.findById(userId);
            console.log(user.uuid, user.email);
            await this.userService.purge(user);
            // await this.userRepo.purgeById(user.uuid);
            await this.userRepo.deleteEmailFromKS(user.email);
        } catch (error) {
            console.log("TRYING BY EMAIL");
            try {
                let user = await this.userService.findByEmail(userId);
                console.log(user.uuid, user.email);
                await this.userService.purge(user);
                // await this.userRepo.purgeById(user.uuid);
            } catch (error) {
                await this.userRepo.deleteEmailFromKS(userId);
                console.log('ERROR TRYING TO DELETE BY EMAIL');
            }
        }

        // return;
    }

    @ApiOperation({ summary: "Purges user data from the cloud and databse by the given user id" })
    @ApiResponse({ status: 200, description: "User has been purged successfully", type: UserDto })
    @ApiResponse({ status: 403, description: "Forbidden." })
    @Delete("/:uuid")
    @Roles(Role.admin)
    async purgeUser(@Headers() headers, @Param("uuid") uuid: string): Promise<void> {
        await this.checkUserContextRoles(headers, uuid, "purgeUser");

        const userContext: UserContext = RESTUtil.resolveUserContex(headers);

        await this.userService.purgeUserDataByUuid(uuid);
    }

    private async checkUserContextRoles(headers, id: string, label = "") {
        //tenant removed.
        return false;
        // const userContext: UserContext = RESTUtil.resolveUserContex(headers);
        // const isAdmin = userContext.isTenantAdmin();

        // if (!isAdmin && userContext.userId !== id) throw new WPError(WP_ERROR_IAM_USER_NOT_FOUND_BY_ID, `by id:${id}`, undefined, "404");
    }
}
