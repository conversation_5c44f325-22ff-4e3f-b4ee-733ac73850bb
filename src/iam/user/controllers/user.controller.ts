import { Body, Controller, Delete, Get, Headers, Injectable, Ip, Logger, Param, ParseFilePipeBuilder, Post, Put, Query, Req, Res, UploadedFile, UseInterceptors } from "@nestjs/common";
import { ApiOperation, ApiQuery, ApiResponse, ApiTags } from "@nestjs/swagger";
import { Response } from "express";
import { UserRegistrationResponseDto } from "../dtos/user-registration-response.dto";
import { UserRegistrationDto } from "../dtos/user-registration.dto";
import { UserDto } from "../dtos/user.dto";
import { User, UserPagination, UserRegistration, UserUpdate } from "../entities/user.entity";
import { UserMapper } from "../utils/user.mapper";
import { UserService } from "../services/user.service";
import { Request } from "express";
import { UserUpdateDto } from "../dtos/user-update.dto";
import { GenderEnum } from "../../../commons/gender.enum";
import { UserFilterQueryParams } from "../dtos/user-filter-query.dto";
import { PaginationDto } from "../../../commons/pagination/pagination-dto";
import { USER_PAGE_RECORD_COUNT, USER_PER_PAGE_PARAM_NAME } from "../../../utils/confs";
import { QueryFilter } from "../../../commons/pagination/query-filter";
import { PaginationUtil } from "../../../commons/pagination/pagination.util";
import { FileInterceptor } from "@nestjs/platform-express";
import { UserImageFileUploadDto } from "../dtos/user-image-upload.dto";
import { UserImageFileDto } from "../dtos/user-image.dto";
import { RESTUtil } from "../../../utils/rest-util";
import { WPError } from "../../../commons/error/wp.error";
import { WP_ERROR_IAM_USER_NOT_FOUND_BY_ID } from "../../../commons/error/error.codes";
import { jwtDecode } from "jwt-decode";
import { UserChangePassDto } from "../dtos/user-change-pass.dto";
import { UserRegisterCompleteDto } from "../dtos/register-complete.dto";
import { UserRegistrationService } from "../services/user-registration.service";
import { UserKycShuftiproDto } from "../dtos/user.kyc.dto";
import { Roles } from "../../../commons/auth/roles.decarator";
import { Role } from "../../../commons/auth/role.enum";
import { UserContext } from "../../../commons/auth/user-context";
import { BaseService } from "../../../commons/services/base.service";
import { ConfigService } from "@nestjs/config";
import { UserNicknameDto } from "../dtos/user-nickname.dto";
import { IAMUserAttribute } from "../../aws/iam/models/iam-user-attribute";

@ApiTags("users")
@Controller("users")
@Injectable()
export class UserController extends BaseService {
    private readonly logger = new Logger(UserController.name);
    constructor(
        private readonly userService: UserService,
        private readonly userRegistrationService: UserRegistrationService,
        private readonly confService: ConfigService,
    ) {
        super(confService);
    }

    /**
     *
     * HTTP Header:
     *
     * content-type": "multipart/form-data;",
     *
     * @param file `UserImageFileUploadDto`
     *  {
     *    fieldname: 'image',
     *    originalname: 'game_hub.png',
     *    encoding: '7bit',
     *    mimetype: 'image/png',
     *    buffer: <Buffer 89 50 3 01 ... 53636 more bytes>,
     *    size: 53686
     *   }
     *
     * MAX 5MB
     */
    @Post("/:id/image")
    @UseInterceptors(FileInterceptor("file", { limits: { files: 1 } }))
    @ApiOperation({ summary: "uploads user profile picture" })
    @Roles(Role.user)
    async upload(
        @Headers() headers,
        @Param("id") id: string,
        @UploadedFile(
            new ParseFilePipeBuilder()
                .addFileTypeValidator({
                    fileType: ".(png|jpeg|jpg)",
                })
                .addMaxSizeValidator({
                    maxSize: 1024 * 1024 * 2, // number of bytes = 2 MB
                })
                .build({
                    fileIsRequired: false,
                }),
        )
        file: UserImageFileUploadDto,
    ) {
        await this.checkUserContextRoles(headers, id, "upload");
        const dto: UserImageFileDto = await this.userService.uploadUserProfileFile(file, id);
        return dto;
    }

    // this api is invoked by Cognito's lambda funtions
    @ApiOperation({ summary: "Queues a user registration task." })
    @ApiResponse({ status: 201, description: "A new user registration task has been queued succesfully.", type: UserRegistrationResponseDto })
    @ApiResponse({ status: 403, description: "Forbidden." })
    @Post(`/register`)
    @Roles(Role.service_admin)
    async registerUser(@Body() dto: UserRegistrationDto): Promise<UserRegistrationResponseDto> {
        // const nickname = await this.userService.generatePreferrredUsername();
        const userReg: UserRegistration = UserMapper.toUserRegistration(dto);
        const flowId: string = await this.userRegistrationService.startUserRegistrationFlow(userReg);
        const responseDto: UserRegistrationResponseDto = {
            ...dto,
        };
        return responseDto;
    }

    @ApiOperation({ summary: "Creates a new prereg." })
    @ApiResponse({ status: 200, description: "Prereg successful", type: UserDto })
    @ApiResponse({ status: 403, description: "Forbidden." })
    @Post("/mew")
    @Roles(Role.full_access)
    async prereg(@Ip() ip: string, @Headers() headers: any): Promise<string> {
        if (headers["user_groups"]) return "";

        const prereg = await this.userService.prereg(headers["cf-connecting-ip"] ? headers["cf-connecting-ip"] : ip);

        return prereg;
    }

    

    @ApiOperation({ summary: "Updates user by the given user id" })
    @ApiResponse({ status: 200, description: "User has been updated successfully", type: UserDto })
    @ApiResponse({ status: 403, description: "Forbidden." })
    @Put("/:id")
    @Roles(Role.user)
    async updateUser(@Headers() headers, @Param("id") id: string, @Body() dto: UserUpdateDto): Promise<UserDto> {
        await this.checkUserContextRoles(headers, id, "updateUser");

        const userContext: UserContext = RESTUtil.resolveUserContex(headers);

        const userUpdate: UserUpdate = UserMapper.toUserUpdate(dto, userContext);
        const updatedUser: User = await this.userService.update(id, userUpdate);
        const user: UserDto = await UserMapper.toUserDto(updatedUser);
        return user;
    }

    @ApiOperation({ summary: "Find and soft delete user by the given user id" })
    @ApiResponse({ status: 200, description: "Return the user found by the given id." })
    @ApiResponse({ status: 403, description: "Forbidden." })
    @Delete("/:id")
    @Roles(Role.user)
    async deleteUserById(@Headers() headers, @Param("id") id: string): Promise<void> {
        await this.checkUserContextRoles(headers, id, "deleteUserById");
        const decodedToken = jwtDecode(headers["authorization"]),
            username = decodedToken["username"];

        // await this.userService.deleteById(id,headers['Authorization']||headers['authorization']);
        await this.userService.disableById(id, username);
    }

    @ApiOperation({ summary: "Unified login page with Cognito, Google, and Apple authentication" })
    @ApiResponse({ status: 200, description: "Returns unified login page HTML" })
    @Get("/login")
    @Roles(Role.full_access)
    async getUnifiedLoginPage(@Res() res: Response): Promise<void> {
        const html = `
        <!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Login - Eazy Wallet</title>
            <style>
                body {
                    font-family: Arial, sans-serif;
                    text-align: center;
                    margin: 50px;
                }
                h1 {
                    margin-bottom: 30px;
                }
                a {
                    display: block;
                    width: 200px;
                    margin: 10px auto;
                    padding: 10px;
                    text-decoration: none;
                    border: 1px solid #ccc;
                    background: white;
                    color: black;
                    cursor: pointer;
                }
                a:hover {
                    background: #f0f0f0;
                }
            </style>
        </head>
        <body>
            <h1>Login</h1>
            
            <a href="${process.env.COGNITO_HOSTED_UI_URL}/login?client_id=${process.env.AWS_COGNITO_CLIENT_ID}&response_type=code&scope=${process.env.COGNITO_SCOPE}&redirect_uri=${process.env.COGNITO_CALLBACK_DOMAIN}/users/landing">
                Login with Email
            </a>
            
            <a href="${process.env.COGNITO_HOSTED_UI_URL}/oauth2/authorize?client_id=${process.env.AWS_COGNITO_CLIENT_ID}&response_type=code&scope=${process.env.COGNITO_GOOGLE_SCOPE || 'email openid profile'}&redirect_uri=${encodeURIComponent(process.env.COGNITO_CALLBACK_DOMAIN + '/users/landing')}&identity_provider=Google&prompt=login">
                Login with Google
            </a>
            
            <a href="${process.env.COGNITO_HOSTED_UI_URL}/oauth2/authorize?client_id=${process.env.AWS_COGNITO_CLIENT_ID}&response_type=code&scope=${process.env.COGNITO_APPLE_SCOPE || 'email+openid'}&redirect_uri=${process.env.COGNITO_CALLBACK_DOMAIN}/users/landing&identity_provider=SignInWithApple&prompt=login">
                Login with Apple
            </a>
        </body>
        </html>
        `;
        
        res.setHeader('Content-Type', 'text/html');
        res.send(html);
    }

    @ApiOperation({ summary: "Get landing page data after user login" })
    @ApiResponse({ status: 200, description: "Returns headers dump", type: Object })
    @ApiResponse({ status: 403, description: "Forbidden." })
    @Get("/landing")
    @Roles(Role.full_access)
    async getLandingPage(@Headers() headers): Promise<object> {
        // await this.checkUserContextRoles(headers, id, "getLandingPage");
        
        return {
            header: {
                title: "Landing Page"
            },
            body: {
                headers: headers
            }
        };
    }    
    

    @ApiOperation({ summary: "Find a user by the given user id" })
    @ApiResponse({ status: 200, description: "Return the user found by the given id.", type: UserDto })
    @ApiResponse({ status: 403, description: "Forbidden." })
    @Get("/:id")
    @Roles(Role.user)
    async findUserById(@Headers() headers, @Param("id") id: string): Promise<UserDto> {
        const userContext: UserContext = RESTUtil.resolveUserContex(headers);

        const user: User = await this.userService.findById(id, userContext);

        const dto: UserDto = UserMapper.toUserDto(user);
        return dto;
    }

    @ApiOperation({ summary: "Reset KYC requested at date for a user" })
    @ApiResponse({ status: 200, description: "KYC requested at date reset successfully." })
    @ApiResponse({ status: 403, description: "Forbidden." })
    @Put("/:id/kyc-date-check")
    @Roles(Role.user)
    async resetKycRequestedAt(@Headers() headers, @Param("id") id: string): Promise<void> {
        const userContext: UserContext = RESTUtil.resolveUserContex(headers);
        if (userContext.userId !== id) {
            throw new WPError(WP_ERROR_IAM_USER_NOT_FOUND_BY_ID, `by id(1):${id}`, undefined, "403");
        }

        await this.userService.resetKycRequestedAt(id);
    }

    @ApiOperation({ summary: "Find users by the given user ids" })
    @ApiResponse({ status: 200, description: "Return the users found by the given ids.", type: UserDto })
    @ApiResponse({ status: 403, description: "Forbidden." })
    @Get("/internal/:ids")
    @Roles(Role.service_admin)
    async findUsersByIdsInternal(@Headers() headers, @Param("ids") ids: string[]): Promise<UserDto[]> {
        // console.log(headers);
        // console.log('&&&& HEADERS &&&&&&');
        if (!headers["auth"] || headers["auth"] != process.env.GAME_IAM_AUTH_HEADER)
            throw new WPError(WP_ERROR_IAM_USER_NOT_FOUND_BY_ID, `by id:${ids} header auth`, undefined, "404");

        let users: User[] = await this.userService.findByIds(ids),
            userDtos: UserDto[] = [];

        users.forEach(user => {
            let dto: UserDto = UserMapper.toUserDto(user);
            userDtos.push(dto);
        });
        // let dto: UserDto = UserMapper.toUserDto(users);
        return userDtos;
    }

    private async checkUserContextRoles(headers, id: string, label = "") {
        const userContext: UserContext = RESTUtil.resolveUserContex(headers);

        if (userContext.userId !== id) throw new WPError(WP_ERROR_IAM_USER_NOT_FOUND_BY_ID, `by id:${id}`, undefined, "404");
    }

    @ApiOperation({ summary: "Find a user by the given user id" })
    @ApiResponse({ status: 200, description: "Return the user found by the given id.", type: UserDto })
    @ApiResponse({ status: 403, description: "Forbidden." })
    @Post("/reset-password")
    @Roles(Role.user)
    async resetPassword(@Headers() headers, @Body() dto: UserChangePassDto): Promise<void> {
        const userContext: UserContext = RESTUtil.resolveUserContex(headers);

        const decodedToken = jwtDecode(headers["authorization"]),
            username = decodedToken["username"];

        await this.userService.resetPassword(username, headers["authorization"], dto["previousPass"], dto["newPass"]);
    }

    @ApiOperation({ summary: "Find all user s" })
    @ApiResponse({ status: 200, description: "Returns user s by the given pagination and filer.", type: UserDto, isArray: true })
    @ApiResponse({ status: 403, description: "Forbidden." })
    @ApiQuery({ name: `gender`, enum: GenderEnum })
    @ApiQuery({ name: `name`, type: String })
    @ApiQuery({ name: `email`, type: String })
    @ApiQuery({ name: `nickname`, type: String })
    @ApiQuery({ name: `birth date`, type: Date })
    @Get("")
    @Roles(Role.admin)
    async findAllUsers(@Headers() headers, @Query() queryParamsFilterDto: UserFilterQueryParams, @Req() req: Request): Promise<PaginationDto<UserDto>> {
        const userContext: UserContext = RESTUtil.resolveUserContex(headers);
        RESTUtil.paginationRBAC(userContext, queryParamsFilterDto);

        const path = `${req.protocol}://${req.get("Host")}${req.baseUrl}${req.path}`;
        const limit: number = Number.parseInt(await this.getConfig(USER_PER_PAGE_PARAM_NAME, USER_PAGE_RECORD_COUNT));
        const queryParams: QueryFilter = UserMapper.toQueryFilterParams(limit, queryParamsFilterDto);

        const up: UserPagination = await this.userService.getWithFilter(queryParams);
        const dtos: UserDto[] = UserMapper.toUserDtos(up.rows);
        const totalRowCount = up.count;
        const pDto: PaginationDto<UserDto> = PaginationUtil.createPaginationDto<UserDto>(queryParams.pagination, totalRowCount, path, dtos);
        return pDto;
    }
    @ApiOperation({ summary: "Completes the registration if not already registerd" })
    @ApiResponse({ status: 200, description: "Return the user found by the given id.", type: UserDto })
    @ApiResponse({ status: 403, description: "Forbidden." })
    @Put("/register-complete/:id")
    @Roles(Role.user)
    async registerComplete(@Headers() headers, @Param("id") id: string, @Body() dto: UserRegisterCompleteDto): Promise<void> {
        // await this.checkUserContextRoles(headers, id, "resetPassword");
        const decodedToken = jwtDecode(headers["authorization"]),
            username = decodedToken["username"];

        await this.userService.registrationComplete(headers["user_id"], username, dto);
    }

    @ApiOperation({ summary: "Login trigger" })
    @ApiResponse({ status: 200, description: "Return the user found by the given id.", type: UserDto })
    @ApiResponse({ status: 403, description: "Forbidden." })
    @Put("/login-complete/:id")
    @Roles(Role.user)
    async loginComplete(@Headers() headers, @Param("id") id: string): Promise<void> {
        const userContext: UserContext = RESTUtil.resolveUserContex(headers);

        if (userContext.userId !== id) throw new WPError(WP_ERROR_IAM_USER_NOT_FOUND_BY_ID, `by id:${id}`, undefined, "404");
        await this.userRegistrationService.completeUserLoginFlow(id);
    }

    @ApiOperation({ summary: "Checks if the registration is complete" })
    @ApiResponse({ status: 200, description: "Return the user found by the given id.", type: UserDto })
    @ApiResponse({ status: 403, description: "Forbidden." })
    @Get("/is-register-complete/:id")
    @Roles(Role.user)
    async isRegisterComplete(@Headers() headers, @Param("id") id: string): Promise<number> {
        // await this.checkUserContextRoles(headers, id, "resetPassword");
        let cs0 = await this.userService.isRegistrationComplete(headers["user_id"]);

        return cs0;
        // return
    }

    @ApiOperation({ summary: "shuftiproRequest" })
    @ApiResponse({ status: 200, description: "Return the user verification url.", type: UserDto })
    @ApiResponse({ status: 403, description: "Forbidden." })
    @Post("/kyc")
    @Roles(Role.user)
    async shuftiproRequest(@Body() dto: UserKycShuftiproDto) {
        const data = await this.userService.shuftiRequest(dto);
        return data;
    }

    

    @ApiOperation({ summary: "shuftiproKycResult" })
    @ApiResponse({ status: 200, description: "Update the user with kyc results.", type: UserDto })
    @ApiResponse({ status: 403, description: "Forbidden." })
    @Post("/kycCallback")
    @Roles(Role.full_access)
    async shuftiproKycResult(@Body() dto: any) {
        this.logger.debug("shuftiproKycResult", JSON.stringify(dto));
        const data = await this.userService.shuftiKycResult(dto);
        return data;
    }

    @ApiOperation({ summary: "Updates only the nickname of a user" })
    @ApiResponse({ status: 200, description: "Nickname has been updated successfully", type: UserDto })
    @ApiResponse({ status: 403, description: "Forbidden." })
    @ApiResponse({ status: 409, description: "Nickname already taken." })
    @Put("/:id/nickname")
    @Roles(Role.user)
    async updateNickname(@Headers() headers, @Param("id") id: string, @Body() dto: UserNicknameDto): Promise<UserDto> {
        await this.checkUserContextRoles(headers, id, "updateNickname");
        const userContext: UserContext = RESTUtil.resolveUserContex(headers);

        if(userContext.userId !== id) {
            throw new WPError(WP_ERROR_IAM_USER_NOT_FOUND_BY_ID, `by id1:${id}`, undefined, "404");;
        }
        
        // First get the existing user
        const existingUser = await this.userService.findById(id);
        if (!existingUser) {
            throw new WPError(WP_ERROR_IAM_USER_NOT_FOUND_BY_ID, `by id2:${id}`, undefined, "404");
        }
        
        // changing the nickname
        const userUpdate: UserUpdate = {
            ...existingUser,
            nickname: dto.nickname,
            updatedAt: new Date()
        };

        const updatedUser: User = await this.userService.update(id, userUpdate, [new IAMUserAttribute('nickname', 'nickname', dto.nickname)]);
        const user: UserDto = UserMapper.toUserDto(updatedUser);
        return user;
    }

    @ApiOperation({ summary: "Debug environment variables" })
    @Get("/debug-env")
    @Roles(Role.full_access)
    async debugEnvironment(@Res() res: Response): Promise<void> {
        const envInfo = {
            COGNITO_SCOPE: process.env.COGNITO_SCOPE,
            COGNITO_APPLE_SCOPE: process.env.COGNITO_APPLE_SCOPE,
            COGNITO_GOOGLE_SCOPE: process.env.COGNITO_GOOGLE_SCOPE,
            AWS_COGNITO_CLIENT_ID: process.env.AWS_COGNITO_CLIENT_ID,
            GOOGLE_CLIENT_ID: process.env.GOOGLE_CLIENT_ID,
            COGNITO_HOSTED_UI_URL: process.env.COGNITO_HOSTED_UI_URL,
            COGNITO_CALLBACK_DOMAIN: process.env.COGNITO_CALLBACK_DOMAIN
        };
        res.json(envInfo);
    }
}