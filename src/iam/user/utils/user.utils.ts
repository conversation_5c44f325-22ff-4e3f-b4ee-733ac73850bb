export class UserUtils {
    public static composeUserProfileImageName(bucketFolder: string, mimeType: string, userId: string) {
        let fileName: string;
        if (mimeType.includes("png") || mimeType.includes("jpg") || mimeType.includes("jpeg")) {
            fileName = `${bucketFolder}/${userId}.png`;
        } else {
            throw new Error(`Invalid image type.Only png files are accepted.`);
        }

        return fileName;
    }
}
