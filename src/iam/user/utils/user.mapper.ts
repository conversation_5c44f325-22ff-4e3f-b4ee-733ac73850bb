import { getGenderEnumBy<PERSON><PERSON> } from "../../../commons/gender.enum";
import { UserRegistrationDto } from "../dtos/user-registration.dto";
import { UserDto } from "../dtos/user.dto";
import { User, UserCreate, UserEntity, UserRegistration, UserUpdate } from "../entities/user.entity";
import { UserFilterQueryParams } from "../dtos/user-filter-query.dto";
import { Pagination } from "../../../commons/pagination/pagination";
import { PaginationUtil } from "../../../commons/pagination/pagination.util";
import { QueryFilter, QueryFilterParam, QueryFilterSearchParam, QueryFilterSortParam, QueryParams } from "../../../commons/pagination/query-filter";
import { QueryFilterUtil } from "../../../commons/pagination/query-filter.util";
import { UserUpdateDto } from "../dtos/user-update.dto";
import { UserCreateDto } from "../dtos/user-create.dto";
import { ImageFile } from "../../aws/s3/models/image-file";
import { UserImageFileUploadDto } from "../dtos/user-image-upload.dto";
import { AwsS3File } from "../../aws/s3/models/aws-s3-file";
import { UserImageFileDto } from "../dtos/user-image.dto";
import { UserContext } from "../../../commons/auth/user-context";
import { UserValidator } from "./user.validator";

export class UserMapper {
    public static toUserCreate(dto: UserCreateDto, userContext: UserContext): UserCreate {
        const userUpdate: UserCreate = {
            id: dto.id,
            uuid: dto.uuid,
            name: dto.name,
            lastname: dto.lastname,
            nickname: dto.nickname,
            address: dto.address,
            birthdate: dto.birthdate,
            gender: dto.gender,
            image: dto.image,
            password: dto.password,
            phoneNumber: dto.phoneNumber,
            email: dto.email,
            locale: dto.locale,
            zoneinfo: dto.zoneinfo,
            emailVerifiedAt: dto.emailVerifiedAt,
            phoneVerifiedAt: dto.phoneVerifiedAt,
            kycEnabled: dto.kycEnabled,
            kycStatus: dto.kycStatus,
            referralCode: dto.referralCode,
            referrerCode: dto.referrerCode
        };
        return userUpdate;
    }

    public static toUserUpdate(dto: UserUpdateDto, userContext: UserContext): UserUpdate {
        const userUpdate: UserUpdate = {
            id: dto.id,
            name: dto.name,
            lastname: dto.lastname,
            nickname: dto.nickname,
            address: dto.address,
            birthdate: dto.birthdate,
            gender: dto.gender,
            image: dto.image,
            password: dto.password,
            phoneNumber: dto.phoneNumber,
            email: dto.email,
            locale: dto.locale,
            zoneinfo: dto.zoneinfo,
            emailVerifiedAt: dto.emailVerifiedAt,
            phoneVerifiedAt: dto.phoneVerifiedAt,
            updatedAt: new Date(),
            kycEnabled: dto.kycEnabled,
            kycStatus: dto.kycStatus,
            referralCode: dto.referralCode,
            referrerCode: dto.referrerCode
        };
        return userUpdate;
    }

    public static toUserRegistration(dto: UserRegistrationDto): UserRegistration {
        const userReg: UserRegistration = {
            id: dto.id,
            uuid:dto.uuid??'',
            nickname:dto.nickname,
            email: dto.email,
            image:dto.picture,
            locale: dto.locale,
            zoneinfo: dto.zoneinfo,
            password: dto.password,
            emailVerifiedAt: dto.emailVerifiedAt ? dto.emailVerifiedAt : new Date(),
            referralCode: UserValidator.fixRefCodeLenght(dto.referralCode??''),
            referrerCode: UserValidator.fixRefCodeLenght(dto.referrerCode??'')
        };
        return userReg;
    }

    public static toUserDto(user: User): UserDto {
        const dto: UserDto = {
            id: user.id,
            name: user.name,
            uuid: user.uuid,
            lastname: user.lastname,
            nickname: user.nickname,
            address: user.address,
            errors: user.errors,
            birthdate: user.birthdate,
            gender: user.gender,
            image: user.image,
            password: user.password,
            phoneNumber: user.phoneNumber,
            email: user.email,
            locale: user.locale,
            zoneinfo: user.zoneinfo,
            emailVerifiedAt: user.emailVerifiedAt,
            emailVerified: user.emailVerifiedAt && user.emailVerifiedAt != null,
            phoneVerifiedAt: user.phoneVerifiedAt,
            phoneNumberVerified: user.phoneVerifiedAt && user.phoneVerifiedAt != null,
            kycEnabled: user.kycEnabled && user.kycEnabled != null,
            kycStatus: user.kycStatus,
            createdAt: user.createdAt,
            updatedAt: user.updatedAt,
            phoneVerified: false,
            referralCode: user.referralCode,
            referrerCode: user.referrerCode
        };
        return dto;
    }

    public static toUserDtos(users: User[]): UserDto[] {
        if (!users || users.length < 1) {
            return [];
        }
        const dtos: UserDto[] = new Array<UserDto>();
        for (let i = 0; i < users.length; i++) {
            const user = users[i];
            dtos.push(this.toUserDto(user));
        }

        return dtos;
    }

    public static toQueryFilterParams(limit: number, filterDto: UserFilterQueryParams): QueryFilter {
        const pagination: Pagination = PaginationUtil.calculatePagination(filterDto.page, limit);
        const params: Map<string, QueryParams> = new Map<string, QueryParams>();

        const userProfileFilterParams: Map<string, QueryFilterParam> = new Map<string, QueryFilterParam>();
        const userProfileSortParams: Map<string, QueryFilterSortParam> = new Map<string, QueryFilterSortParam>();
        params.set(UserEntity.name, { filterParams: userProfileFilterParams, sortParams: userProfileSortParams });

        if (filterDto.gender) {
            const userGenderValue: number = getGenderEnumByKey(filterDto.gender);
            const searchParam: QueryFilterSearchParam = new QueryFilterSearchParam("gender", userGenderValue, UserEntity.name);
            userProfileFilterParams.set(searchParam.name, searchParam);
        }

        if (filterDto.id) {
            const searchParam: QueryFilterSearchParam = new QueryFilterSearchParam("id", filterDto.id, UserEntity.name);
            userProfileFilterParams.set(searchParam.name, searchParam);
        }

        if (filterDto.name) {
            const searchParam: QueryFilterSearchParam = new QueryFilterSearchParam("name", filterDto.name, UserEntity.name);
            userProfileFilterParams.set(searchParam.name, searchParam);
        }

        if (filterDto.email) {
            const searchParam: QueryFilterSearchParam = new QueryFilterSearchParam("email", filterDto.email, UserEntity.name);
            userProfileFilterParams.set(searchParam.name, searchParam);
        }

        if (filterDto.nickname) {
            const searchParam: QueryFilterSearchParam = new QueryFilterSearchParam("nickname", filterDto.nickname, UserEntity.name);
            userProfileFilterParams.set(searchParam.name, searchParam);
        }

        if (filterDto.phoneNumber) {
            const searchParam: QueryFilterSearchParam = new QueryFilterSearchParam("phoneNumber", filterDto.phoneNumber, UserEntity.name);
            userProfileFilterParams.set(searchParam.name, searchParam);
        }

        if (filterDto.lastname) {
            const searchParam: QueryFilterSearchParam = new QueryFilterSearchParam("lastname", filterDto.lastname, UserEntity.name);
            userProfileFilterParams.set(searchParam.name, searchParam);
        }

        if (filterDto.address) {
            const searchParam: QueryFilterSearchParam = new QueryFilterSearchParam("address", filterDto.address, UserEntity.name);
            userProfileFilterParams.set(searchParam.name, searchParam);
        }

        if (filterDto.referralCode) {
            const searchParam: QueryFilterSearchParam = new QueryFilterSearchParam("referralCode", filterDto.address, UserEntity.name);
            userProfileFilterParams.set(searchParam.name, searchParam);
        }

        if (filterDto.referrerCode) {
            const searchParam: QueryFilterSearchParam = new QueryFilterSearchParam("referrerCode", filterDto.address, UserEntity.name);
            userProfileFilterParams.set(searchParam.name, searchParam);
        }

        if (filterDto.sortBy) {
            if (!filterDto.sortOrder) {
                filterDto.sortOrder = "DESC";
            }
            const sortParam: QueryFilterSortParam = new QueryFilterSortParam(filterDto.sortBy, filterDto.sortOrder, UserEntity.name);
            userProfileSortParams.set(sortParam.name, sortParam);
        }

        if (filterDto.birthdate) {
            const searchParam: QueryFilterSearchParam = new QueryFilterSearchParam("birthdate", filterDto.birthdate, UserEntity.name);
            userProfileFilterParams.set(searchParam.name, searchParam);
        }
        QueryFilterUtil.addBetweenCriteria(UserEntity.name, userProfileFilterParams, "birthdate", filterDto.from_birthdate, filterDto.to_birthdate);

        const queryParams: QueryFilter = {
            pagination: pagination,
            params: params,
        };

        return queryParams;
    }

    public static toAwsImageFile(bucket: string, fileName: string, imageFileDTO: UserImageFileUploadDto): ImageFile {
        const imageFile: ImageFile = new ImageFile(
            bucket,
            fileName,
            imageFileDTO.fieldname??'',
            imageFileDTO.originalname??'',
            imageFileDTO.encoding??'',
            imageFileDTO.mimetype??'',
            imageFileDTO.buffer??'',
            imageFileDTO.size??0,
        );

        return imageFile;
    }

    public static toUserImageFileDto(awsS3File: AwsS3File): UserImageFileDto {
        const dto: UserImageFileDto = {
            fileUrl: awsS3File.fileUrl,
        } as UserImageFileDto;

        return dto;
    }
}
