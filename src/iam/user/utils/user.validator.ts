import { GenderEnum, genderEnumsArray, getGenderEnum } from "../../../commons/gender.enum";
import { WPError } from "../../../commons/error/wp.error";
import { WP_ERROR_IAM_USER_VALIDATION_PARAMS } from "../../../commons/error/error.codes";

export class UserValidator {
    public static validateGender(key: string, optional = true): GenderEnum {
        if (optional && !key) {
            throw new WPError(WP_ERROR_IAM_USER_VALIDATION_PARAMS, `Gender value can not be null.`);
        }

        const gender: GenderEnum = getGenderEnum(key);
        if (!gender) {
            throw new WPError(WP_ERROR_IAM_USER_VALIDATION_PARAMS, `Invalid gender value provided. The valid values: ${genderEnumsArray}`, undefined, "400");
        }

        return gender;
    }

    public static fixLength(str:string,lenght:number) {
        if(str && str.length > lenght) {
            str = str.slice(0,lenght);
        }
        return str;
    }

    public static fixRefCodeLenght(str:string) {
        return this.fixLength(str,10);
    }
}
