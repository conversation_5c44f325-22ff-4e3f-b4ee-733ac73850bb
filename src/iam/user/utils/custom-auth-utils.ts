import axios from 'axios';
import { AdminInitiateAuthCommand, AdminRespondToAuthChallengeCommand, CognitoIdentityProviderClient, ListUsersCommand } from '@aws-sdk/client-cognito-identity-provider';
import { Logger } from '@nestjs/common';
import { WPError } from '../../../commons/error/wp.error';
import { KYCCLIENTTYPE } from '../entities/user.kyc.entity';
import { ApiProperty } from '@nestjs/swagger';
import { WP_ERROR_IAM_USERS_NOT_FOUND } from '../../../commons/error/error.codes';
const crypto = require('crypto');

export class MobileLoginResponse {
    AccessToken?: string;
    IdToken?: string;
    RefreshToken?: string;
    ExpiresIn?: number;
    email: string;
    userId: string;
}

export class MobileLoginRequest {
    @ApiProperty({
        description: 'Google ID token',
        example: 'OAUTHTOKEN',
    })
    googleOauthToken: string;

    @ApiProperty({
        description: 'Application definition',
        example: 'com.wodonetwork.android',
    })
    appDef?: string;
}

export class RefreshTokenRequest {
    @ApiProperty({
        description: 'Refresh token',
        example: 'REFRESHTOKEN',
    })
    refreshToken: string;

    @ApiProperty({
        description: 'User email/username',
        example: '<EMAIL>',
    })
    email: string;
}

export class MobileAppleRequest {
    @ApiProperty({
        description: 'Apple ID token',
        example: 'APPLE_ID_TOKEN',
    })
    appleIdToken: string;

    @ApiProperty({
        description: 'Application definition',
        example: 'com.wodonetwork.android',
    })
    appDef?: string;
}

export class CustomAuthUtils {
    private cognitoClient: CognitoIdentityProviderClient;
    private readonly logger = new Logger(CustomAuthUtils.name);

    constructor() {
        this.cognitoClient = new CognitoIdentityProviderClient({
            region: process.env.AWS_REGION,
        });
    }

    async verifyGoogleToken(token: string) {
        const response = await axios.get(`https://oauth2.googleapis.com/tokeninfo?id_token=${token}`);
        if (response.status !== 200) {
            throw new WPError(WP_ERROR_IAM_USERS_NOT_FOUND, 'Failed to verify Google token', undefined, '403');
        }
        const data = response.data;
        if (!data.email) {
            throw new WPError(WP_ERROR_IAM_USERS_NOT_FOUND, 'Email not found in Google token data', undefined, '403');
        }
        return {
            email: data.email,
            name: data.name || '',
            sub: data.sub,
        };
    }

    async authenticateWithGoogle(email: string, infoSerialized: string) {
        try {
            // Prepare the authentication parameters
            const clientId = process.env.AWS_COGNITO_CUSTOM_CLIENT_ID;
            const clientSecret = process.env.AWS_COGNITO_CUSTOM_CLIENT_SECRET || '';

            if (clientSecret == '') {
            }

            // Calculate SECRET_HASH
            const message = email + clientId;
            const hmac = crypto.createHmac('SHA256', clientSecret);
            const secretHash = hmac.update(message).digest('base64');
            this.logger.debug(
                {
                    clientId,
                    email: email,
                    clientSecret: clientSecret,
                    secretHash: secretHash,
                    poolId: process.env.AWS_COGNITO_USER_POOL_ID,
                },
                'Environment Variables1:',
            );
            const authParams = {
                AuthFlow: 'CUSTOM_AUTH' as const,
                UserPoolId: process.env.AWS_COGNITO_USER_POOL_ID,
                ClientId: process.env.AWS_COGNITO_CUSTOM_CLIENT_ID,
                ClientSecret: process.env.AWS_COGNITO_CUSTOM_CLIENT_SECRET,
                AuthParameters: {
                    USERNAME: email,
                    SECRET_HASH: secretHash, // Add this line

                    // Any other parameters you might need
                },
            };
            if (clientSecret == '') {
                delete authParams.ClientSecret;
                delete authParams.AuthParameters.SECRET_HASH;
            }
            // Initiate the authentication
            const authCommand = new AdminInitiateAuthCommand(authParams);
            const authResponse = await this.cognitoClient.send(authCommand);
            this.logger.debug('AUTH RESPONSE:', authResponse);
            // Step 2: Respond to the custom challenge
            if (authResponse.ChallengeName === 'CUSTOM_CHALLENGE') {
                this.logger.debug('RESPONDING TO CHALLENGE!!!!');
                const challengeParams = {
                    ChallengeName: 'CUSTOM_CHALLENGE' as const,
                    ClientId: process.env.AWS_COGNITO_CUSTOM_CLIENT_ID,
                    ClientSecret: process.env.AWS_COGNITO_CUSTOM_CLIENT_SECRET,
                    UserPoolId: process.env.AWS_COGNITO_USER_POOL_ID,
                    ChallengeResponses: {
                        USERNAME: email,
                        ANSWER: infoSerialized, // The Google ID token
                        SECRET_HASH: secretHash, // Add this line
                    },
                    Session: authResponse.Session,
                };

                if (clientSecret == '') {
                    delete challengeParams.ClientSecret;
                    delete challengeParams.ChallengeResponses.SECRET_HASH;
                }

                const respondChallengeCmd = new AdminRespondToAuthChallengeCommand(challengeParams);
                let challengeResponse = await this.cognitoClient.send(respondChallengeCmd);
                this.logger.debug('Challenge response:', challengeResponse);

                return challengeResponse;
                // console.log('Custom auth response:', JSON.stringify(authResponse, null, 2));
            } // return authResponse;
        } catch (error) {
            this.logger.error('Custom auth error:', error);
            throw new WPError(WP_ERROR_IAM_USERS_NOT_FOUND, 'Custom auth error', undefined, '403');
        }
    }

    async authenticateWithApple(email: string, infoSerialized: string) {
        try {
            const clientId = process.env.AWS_COGNITO_CUSTOM_CLIENT_ID;
            const clientSecret = process.env.AWS_COGNITO_CUSTOM_CLIENT_SECRET || '';

            const message = email + clientId;
            const hmac = crypto.createHmac('SHA256', clientSecret);
            const secretHash = hmac.update(message).digest('base64');

            const authParams = {
                AuthFlow: 'CUSTOM_AUTH' as const,
                UserPoolId: process.env.AWS_COGNITO_USER_POOL_ID,
                ClientId: process.env.AWS_COGNITO_CUSTOM_CLIENT_ID,
                ClientSecret: process.env.AWS_COGNITO_CUSTOM_CLIENT_SECRET,
                AuthParameters: {
                    USERNAME: email,
                    SECRET_HASH: secretHash,
                    ANSWER: infoSerialized, // The Apple ID token
                },
            };
            if (clientSecret === '') {
                delete authParams.ClientSecret;
                delete authParams.AuthParameters.SECRET_HASH;
            }

            const authCommand = new AdminInitiateAuthCommand(authParams);
            const authResponse = await this.cognitoClient.send(authCommand);

            // Respond to challenge similar to Google
            if (authResponse.ChallengeName === 'CUSTOM_CHALLENGE') {
                const challengeParams = {
                    ChallengeName: 'CUSTOM_CHALLENGE' as const,
                    ClientId: process.env.AWS_COGNITO_CUSTOM_CLIENT_ID,
                    UserPoolId: process.env.AWS_COGNITO_USER_POOL_ID,
                    ChallengeResponses: {
                        USERNAME: email,
                        ANSWER: infoSerialized,
                        SECRET_HASH: secretHash,
                    },
                    Session: authResponse.Session,
                };

                if (clientSecret === '') {
                    delete challengeParams.ChallengeResponses.SECRET_HASH;
                }

                const respondChallengeCmd = new AdminRespondToAuthChallengeCommand(challengeParams);
                const challengeResponse = await this.cognitoClient.send(respondChallengeCmd);
                return challengeResponse;
            } else {
                return authResponse;
            }
        } catch (error) {
            this.logger.error('Error in Apple authentication:', error);
            throw error;
        }
    }

    async findUserByEmail(email: string) {
        const listUsersCommand = new ListUsersCommand({
            UserPoolId: process.env.AWS_COGNITO_USER_POOL_ID,
            Filter: `email = "${email}"`,
            Limit: 1,
        });

        const { Users } = await this.cognitoClient.send(listUsersCommand);
        return Users?.[0];
    }

    async refreshToken(token: string, email: string) {
        try {
            const awsUser = await this.findUserByEmail(email);

            let username: string | undefined;

            if (awsUser) {
                //aws requires cognito sub as username, MF
                username = awsUser.Attributes?.find(attr => attr.Name === 'sub')?.Value;
            }
            if (!username) {
                throw new WPError(WP_ERROR_IAM_USERS_NOT_FOUND, 'Username not found', undefined, '403');
            }

            console.log('AWS USER:', awsUser);
            const clientId = process.env.AWS_COGNITO_CLIENT_ID?.trim();
            const clientSecret = process.env.AWS_COGNITO_CLIENT_SECRET || '';
            const userPoolId = process.env.AWS_COGNITO_USER_POOL_ID?.trim();

            // Calculate SECRET_HASH
            const message = username + clientId;
            const hmac = crypto.createHmac('SHA256', clientSecret);
            const secretHash = hmac.update(message).digest('base64');

            // Log environment variables

            // Log the calculated SECRET_HASH
            this.logger.debug('Calculated SECRET_HASH:', secretHash);

            // Prepare the authentication parameters
            const authParams = {
                REFRESH_TOKEN: token,
                SECRET_HASH: secretHash,
                USERNAME: email,
            };
            if (clientSecret == '') {
                delete authParams.SECRET_HASH;
            }

            // Log the authentication parameters
            this.logger.debug('Refresh token parameters:', authParams);

            const command = new AdminInitiateAuthCommand({
                AuthFlow: 'REFRESH_TOKEN_AUTH' as const,
                ClientId: clientId,
                UserPoolId: userPoolId,
                AuthParameters: authParams,
            });

            // Log the command input
            this.logger.debug('AdminInitiateAuthCommand input:', command.input);

            // Execute the command
            const response = await this.cognitoClient.send(command);

            // Log and return the response
            this.logger.debug('Refresh token response:', response);
            return response;
        } catch (error) {
            // Log detailed error information
            this.logger.error('Refresh token error details:', {
                message: error.message,
                code: error.name,
                requestId: error.$metadata?.requestId,
                stack: error.stack,
            });
            throw new WPError(WP_ERROR_IAM_USERS_NOT_FOUND, 'Refresh token error', undefined, '403');
        }
    }
}