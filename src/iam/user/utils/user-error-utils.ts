import { WPError } from "../../../commons/error/wp.error";
import { UniqueConstraintError, ValidationErrorItem } from "sequelize";
import { WPErrorCode } from "../../../commons/error/error.codes";

export class UserErrorUtils {
    public static unique_primary = "PRIMARY";
    public static unique_id = "id";
    public static unique_email = "email";

    /**
     * TODO: This is an nappropriate way to process errors. Use a design pattern here later.
     * @param error `Error`
     * @param errorCode `WPErrorCode`
     */
    public static handleORMError(error: Error, errorCode: WPErrorCode) {
        let transportErrorCode = "500";
        const errorMessage: string = error.message;
        if (error.name === "SequelizeUniqueConstraintError") {
            transportErrorCode = "409";
            const uce: UniqueConstraintError = error as UniqueConstraintError;

            this.checkValidationErrorItems(error, uce, errorCode, this.unique_primary, `There is already a record with the same id.${uce.message}`, transportErrorCode);
            // in case the db throws PK error with 'id' attr instead of PRIMARY
            this.checkValidationErrorItems(error, uce, errorCode, this.unique_id, `There is already a record with the same id.${uce.message}`, transportErrorCode);
            this.checkValidationErrorItems(error, uce, errorCode, this.unique_email, `There is already a record with the same email.${uce.message}`, transportErrorCode);
        }
        throw new WPError(errorCode, `${errorMessage}`, error.stack, transportErrorCode);
    }

    /**
     *
     * @param error `Error`
     * @param uce `UniqueConstraintError`
     * @param errorCode `WPErrorCode`
     * @param uniqueAttr `string`
     * @param message `string`
     * @param transportErrorCode `string`
     */
    private static checkValidationErrorItems(error: Error, uce: UniqueConstraintError, errorCode: WPErrorCode, uniqueAttr: string, message: string, transportErrorCode: string) {
        const validationErrorItems: ValidationErrorItem[] = uce.get(uniqueAttr);
        if (validationErrorItems && validationErrorItems.length > 0) {
            throw new WPError(errorCode, `${message}.${validationErrorItems[0].message}`, error.stack, transportErrorCode);
        }
    }
}
