import { Injectable } from '@nestjs/common';
import { UserKycLogRepository } from '../repositories/user_kyc_log.repository';
import { UserKycLogEntity } from '../entities/user_kyc_log.entity';

@Injectable()
export class UserKycLogService {
    constructor(private readonly userKycLogRepository: UserKycLogRepository) {}

    async create(data: Partial<UserKycLogEntity>): Promise<UserKycLogEntity> {
        return this.userKycLogRepository.create(data);
    }

    async findAll(): Promise<UserKycLogEntity[]> {
        return this.userKycLogRepository.findAll();
    }

    async findOne(id: number): Promise<UserKycLogEntity | null> {
        return this.userKycLogRepository.findOne(id);
    }

    async update(id: number, data: Partial<UserKycLogEntity>): Promise<UserKycLogEntity | null> {
        const [, updatedRecords] = await this.userKycLogRepository.update(id, data);
        return updatedRecords[0] || null;
    }

    async remove(id: number): Promise<boolean> {
        const deletedCount = await this.userKycLogRepository.remove(id);
        return deletedCount > 0;
    }
}