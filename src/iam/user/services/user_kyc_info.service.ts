import { Injectable } from '@nestjs/common';
import { UserKycInfoRepository } from '../repositories/user_kyc_info.repository';
import { UserKycInfo } from '../entities/user_kyc_info.entity';

@Injectable()
export class UserKycInfoService {
  constructor(private readonly userKycInfoRepository: UserKycInfoRepository) {}

  async create(data: Partial<UserKycInfo>): Promise<UserKycInfo> {
    return this.userKycInfoRepository.create(data);
  }

  async findAll(): Promise<UserKycInfo[]> {
    return this.userKycInfoRepository.findAll();
  }

  async findOne(id: number): Promise<UserKycInfo | null> {
    return this.userKycInfoRepository.findOne(id);
  }

  async update(id: number, data: Partial<UserKycInfo>): Promise<UserKycInfo | null> {
    const [, updatedRecords] = await this.userKycInfoRepository.update(id, data);
    return updatedRecords[0] || null;
  }

  async remove(id: number): Promise<boolean> {
    const deletedCount = await this.userKycInfoRepository.remove(id);
    return deletedCount > 0;
  }
}