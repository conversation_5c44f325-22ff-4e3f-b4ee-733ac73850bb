import { Inject, Injectable, Logger, forwardRef } from "@nestjs/common";
import { UserRegistration } from "../entities/user.entity";
import { UserService } from "./user.service";

@Injectable()
export class UserRegistrationService {
    private readonly logger = new Logger(UserRegistrationService.name);

    constructor(@Inject(forwardRef(() => UserService)) private readonly userService: UserService) {
        
    }

    async startUserRegistrationFlow(userReg: UserRegistration): Promise<string> {
        // Directly persist user registration
        await this.userService.register(userReg);

        return userReg.id;
    }

    async completeUserLoginFlow(userId: string): Promise<void> {
        await this.userService.loginComplete(userId);
    }   

    async completeUserKycFlow(userId: string): Promise<void> {
        await this.userService.kycComplete(userId);
    }

}
