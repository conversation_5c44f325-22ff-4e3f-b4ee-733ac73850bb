import { Injectable, Logger } from '@nestjs/common';
import { User, UserCreate, UserEntity, UserPagination, UserRegistration, UserUpdate } from '../entities/user.entity';
import { UserRepository } from '../repositories/user.repository';
import { generateUsername } from 'unique-username-generator';
import { nanoid } from 'nanoid/async';
import { WPError } from '../../../commons/error/wp.error';
import {
  WP_ERROR_IAM_USER_EXISTS,
  WP_ERROR_IAM_USER_NOT_DELETED,
  WP_ERROR_IAM_USER_NOT_FOUND_BY_EMAIL,
  WP_ERROR_IAM_USER_NOT_FOUND_BY_ID,
  WP_ERROR_IAM_USERS_NOT_FOUND,
} from '../../../commons/error/error.codes';
import { QueryFilter } from '../../../commons/pagination/query-filter';
import { UserImageFileUploadDto } from '../dtos/user-image-upload.dto';
import { UserImageFileDto } from '../dtos/user-image.dto';
import { AWS_USER_PROFILE_BUCKET_FOLDER_PARAM_NAME, AWS_USER_PROFILE_BUCKET_NAME_PARAM_NAME } from '../../../utils/confs';
import { UserUtils } from '../utils/user.utils';
import { AwsService } from '../../aws/services/aws.service';
import { UserMapper } from '../utils/user.mapper';
import { AwsS3File } from '../../aws/s3/models/aws-s3-file';
import { ImageFile } from '../../aws/s3/models/image-file';
import { IAMUserAttribute } from '../../aws/iam/models/iam-user-attribute';
import { UserRegisterCompleteDto } from '../dtos/register-complete.dto';
import { UserKycShuftiproDto } from '../dtos/user.kyc.dto';
import { UserKycRepository } from '../repositories/user.kyc.repository';
import { UserKycEntity } from '../entities/user.kyc.entity';
import { UserContext } from '../../../commons/auth/user-context';
import { ConfigService } from '@nestjs/config';
import { BaseService } from '../../../commons/services/base.service';
import { Transactional } from 'sequelize-transactional-decorator';
import { CustomAuthUtils, MobileLoginResponse } from '../utils/custom-auth-utils';
import { AwsCognitoService } from '../../aws/iam/services/aws-cognito-service';
import { IAMUserCreate } from '../../aws/iam/models/iam-user-create';
import { Role } from '../../../commons/auth/role.enum';
import { IAMUser } from '../../aws/iam/models/iam-user';
import axios from 'axios';
import * as jwt from 'jsonwebtoken';
import { jwkToPem } from 'jwk-to-pem';

@Injectable()
export class UserService extends BaseService {
  private readonly logger = new Logger(UserService.name);

  constructor(
    private readonly userRepo: UserRepository,
    private readonly userKycRepo: UserKycRepository,
    private readonly confService: ConfigService,
    private readonly awsService: AwsService,
    private readonly awsCognitoService: AwsCognitoService,
    private readonly customAuthUtils: CustomAuthUtils,
  ) {
    super(confService);
  }

  @Transactional()
  async resetKycRequestedAt(id: string): Promise<void> {
    await this.userRepo.resetKycRequestedAt(id);
  }

  async generatePreferrredUsername(): Promise<string> {
    const username = generateUsername();
    const nanoId = await nanoid(6);
    return `${username}-${nanoId}`;
  }

  @Transactional()
  async register(userReg: UserRegistration): Promise<User | undefined> {
    const user: User | undefined = await this.userRepo.register(userReg);
    return user;
  }

  @Transactional()
  async create(uc: UserCreate, userConext: UserContext, syncWithRemoteIam: boolean = false): Promise<User | undefined> {
    try {
    } catch (error) {}
    const userExist = await this.userRepo.findByEmail(uc.email);
    if (userExist) {
      throw new WPError(WP_ERROR_IAM_USER_EXISTS, 'User already exist', undefined, WP_ERROR_IAM_USER_EXISTS.headerStatusCode + '');
    }
    const user: User | undefined = await this.userRepo.create(uc);

    if (syncWithRemoteIam) {
      const groups: string[] = [Role.user];

      const awsUSerCreate: IAMUserCreate = new IAMUserCreate(
        user!.id,
        uc.name,
        uc.lastname,
        uc.nickname,
        uc.email,
        uc.phoneNumber,
        uc.gender,
        uc.address,
        uc.referralCode,
        groups,
      );
      const iamUser: IAMUser = await this.awsCognitoService.createUserAsAdmin(awsUSerCreate, userConext);
      user!.uuid = iamUser.sub;
      await this.userRepo.update(user!.id, user!);
    }

    return user;
  }

  async prereg(ip: string): Promise<string> {
    const res = await this.userRepo.prereg(ip);
    return res;
  }

  async shuftiRequest(userKyc: UserKycShuftiproDto): Promise<any> {
    const response = await this.userRepo.shuftiRequest(userKyc);
    return response;
  }

  async shuftiKycResult(kycResultDto: any): Promise<UserKycEntity> {
    const userKyc = await this.userKycRepo.shuftiKycResult(kycResultDto);
    return userKyc;
  }

  @Transactional()
  async update(id: string, userUpdate: UserUpdate, updates?: IAMUserAttribute[]): Promise<User> {
    const _user = await this.userRepo.findById(id);

    // TODO: implement outbox pattern for AWS cognito updates and DB updates.
    if (!updates) {
      updates = [
        new IAMUserAttribute('name', 'name', userUpdate.name),
        new IAMUserAttribute('family_name', 'lastname', userUpdate.lastname),
        new IAMUserAttribute('nickname', 'nickname', userUpdate.nickname),
        new IAMUserAttribute('birthdate', 'birthdate', userUpdate.birthdate ? userUpdate.birthdate.toString() : ''),
        new IAMUserAttribute('phone_number', 'phone_number', userUpdate.phoneNumber),
      ];
    }

    if (userUpdate.gender) {
      updates.push(new IAMUserAttribute('gender', 'gender', userUpdate.gender?.toString()));
    }

    await this.awsService.updateCognitoAttributes(_user!.uuid, updates);
    const entity: UserEntity = await this.userRepo.update(id, userUpdate);
    const user: User = entity as unknown as User;
    return user;
  }

  async updateUserIamge(id: string, image: string): Promise<void> {
    // TODO: implement an XA tx here.
    const _user = await this.userRepo.findById(id);

    await this.awsService.updateCognitoAttributes(_user!.uuid, [{ Name: 'picture', mappedName: 'image', Value: image }]);
    await this.userRepo.updateUserIamge(id, image);
  }

  async resetPassword(username: string, token: string, previousPass: string, newPass: string) {
    await this.awsService.resetPassword(username, token, previousPass, newPass);
  }

  async registrationComplete(userId: string, username: string, dto: UserRegisterCompleteDto) {
    const userReg = await this.userRepo.findById(userId);

    if (!userReg || userReg.registerSeenAt) return;

    await this.userRepo.registerComplete(userId, dto.utmSource!, dto.refCode!);
    await this.awsService.regGood(username, dto.utmSource, dto.refCode);

    //TODO: check SQS for user registration complete event
    /*         if (userReg.referralCode != "") {
            try {
                const { userRefCodeEventFlow, provisionUserAccount } = proxyActivities<IActivities>({
                    startToCloseTimeout: "60 seconds",
                    retry: { maximumAttempts: 5 },
                });

                const userRegisteredEventDto = {
                    userId: userReg.id,
                    referralCode: userReg.referralCode,
                } as UserRefCodeEventDto;
                await userRefCodeEventFlow(userRegisteredEventDto);
            } catch (error) {
                this.logger.log(`Could not run campaigns for user registration:${JSON.stringify(userReg)}`);
                //?
            }
        }
 */
  }

  async isRegistrationComplete(userId: string) {
    let isc = await this.userRepo.isRegisterComplete(userId);
    return isc;
  }

  async findById(id: string, userContext?: UserContext): Promise<User> {
    const entity: UserEntity|undefined = userContext ? await this.userRepo.findOneById(id, userContext) : await this.userRepo.findById(id);

    if (!entity) {
      throw new WPError(WP_ERROR_IAM_USER_NOT_FOUND_BY_ID, `by id:${id}`, undefined, '404');
    }

    const user: User = entity as unknown as User;
    return user;
  }

  async findByEmail(email: string): Promise<User> {
    const entity: UserEntity|undefined = await this.userRepo.findByEmail(email);

    if (!entity) {
      throw new WPError(WP_ERROR_IAM_USER_NOT_FOUND_BY_EMAIL, `by email:${email}`, undefined, '404');
    }

    const user: User = entity as unknown as User;
    return user;
  }

  async findByIds(ids: string[]): Promise<User[]> {
    const entity: UserEntity[]|undefined = await this.userRepo.findByIds(ids);
    if (!entity) {
      throw new WPError(WP_ERROR_IAM_USER_NOT_FOUND_BY_ID, `by id:${ids}`, undefined, '404');
    }
    const users: User[] = entity as unknown as User[];
    return users;
  }

  @Transactional()
  async deleteById(user: User): Promise<void> {
    //check for wallet
    await this.userRepo.deleteById(user.id);
    await this.awsService.deleteUser(user.uuid!);
  }

  @Transactional()
  async purge(user: User): Promise<void> {
    //check for wallet
    await this.awsService.deleteUser(user.uuid!).catch(error => {
      this.logger.error('DELETE AWS ERROR', error);
    });

    await this.userRepo.purgeById(user.id);
  }

  async disableById(id: string, username: string): Promise<void> {
    //check for wallet
    this.logger.debug('ACCESS' + username);
    await this.awsService.disableUser(username);
    await this.userRepo.deleteById(id);
  }

  async purgeById(id: string): Promise<void> {
    await this.userRepo.purgeById(id);
  }

  /**
   * Purges the user data from the cluud and database by the given id
   * @param id
   */
  @Transactional()
  async purgeUserDataById(id: string): Promise<void> {
    let user: UserEntity|undefined;
    try {
      user = await this.userRepo.findById(id);
      await this.userRepo.purgeById(id);
    } catch (error) {
      if (error instanceof WPError) {
        const wpError: WPError = error as WPError;
        if (wpError.errorCode.code === WP_ERROR_IAM_USER_NOT_FOUND_BY_ID.code) {
          // ignore
        } else {
          throw error;
        }
      } else {
        throw error;
      }
    }
    if (user) {
      await this.awsService.deleteUserAdmin(user.uuid);
    }
  }

  @Transactional()
  async purgeUserDataByEmail(email: string): Promise<void> {
    let user: UserEntity|undefined;
    try {
      user = await this.userRepo.findByEmail(email);
      if (user) {
        await this.userRepo.purgeById(user.id);
      }
    } catch (error) {
      if (error instanceof WPError) {
        const wpError: WPError = error as WPError;
        if (wpError.errorCode.code === WP_ERROR_IAM_USER_NOT_FOUND_BY_ID.code) {
          // ignore
        } else {
          throw error;
        }
      } else {
        throw error;
      }
    }
    if (user) {
      await this.awsService.deleteUserAdmin(user.uuid);
    }
  }

  @Transactional()
  async purgeUserDataByUuid(uuid: string): Promise<void> {
    let user: UserEntity|undefined;
    try {
      user = await this.userRepo.findByUuid(uuid);
      if (user) {
        await this.userRepo.purgeById(user.id);
      }
    } catch (error) {
      if (error instanceof WPError) {
        const wpError: WPError = error as WPError;
        if (wpError.errorCode.code === WP_ERROR_IAM_USER_NOT_FOUND_BY_ID.code) {
          // ignore
        } else {
          throw error;
        }
      } else {
        throw error;
      }
    }

    await this.awsService.deleteUserAdmin(uuid);
  }

  async getWithFilter(queryFilter: QueryFilter): Promise<UserPagination> {
    return await this.userRepo.getWithFilter(queryFilter);
  }
  async findAll(): Promise<User[]> {
    return await this.userRepo.findAll();
  }

  async uploadUserProfileFile(imageFileDTO: UserImageFileUploadDto, userId: string): Promise<UserImageFileDto> {
    this.logger.debug(`dto:${imageFileDTO.toString()}, userId:${userId}`);

    const bucket: string = await this.getConfig(AWS_USER_PROFILE_BUCKET_NAME_PARAM_NAME);
    const bucketFolder: string = await this.getConfig(AWS_USER_PROFILE_BUCKET_FOLDER_PARAM_NAME);

    const fileName: string = UserUtils.composeUserProfileImageName(bucketFolder, imageFileDTO.mimetype??'', userId);

    this.logger.debug(
      `uploading file name: ${fileName}, fieldname name: ${imageFileDTO.fieldname},original name: ${imageFileDTO.originalname}, mimetype: ${imageFileDTO.mimetype}, size:${imageFileDTO.size}, encoding:${imageFileDTO.encoding},userId:${userId}`,
    );
    const awsImageFile: ImageFile = UserMapper.toAwsImageFile(bucket, fileName, imageFileDTO);
    const awsS3File: AwsS3File = await this.awsService.uploadImageFile(awsImageFile);
    await this.updateUserIamge(userId, awsS3File.fileUrl);
    return UserMapper.toUserImageFileDto(awsS3File);
  }

  async processMobileLoginRegister(googleIdToken: string, appDef?: string): Promise<MobileLoginResponse> {
    try {
      this.logger.debug('processMobileLogin', { googleIdToken, appDef });
      // 1. Verify Google token
      const googleUserInfo = await this.customAuthUtils.verifyGoogleToken(googleIdToken);
      const infoSerialized = JSON.stringify(googleUserInfo);

      let tokens = await this.customAuthUtils.authenticateWithGoogle(googleUserInfo.email, infoSerialized);

      try {
        this.logger.debug(tokens, 'tokens');
        if (!tokens?.AuthenticationResult) {
          this.logger.debug('RETRYING, PROBABLY NEW USER');
          tokens = await this.customAuthUtils.authenticateWithGoogle(googleUserInfo.email, infoSerialized);
        }

        if (!tokens?.AuthenticationResult) {
          this.logger.error('Custom auth error', { tokens });
          throw new WPError(WP_ERROR_IAM_USERS_NOT_FOUND, 'Custom auth error', undefined, '403');
        }

        //default delay for the workflow to complete, lame
        await new Promise(resolve => setTimeout(resolve, 1500));

        //TODO: gotta find a better way to do this
        //try to find the user in the database, because the user might not be created yet in the database, workflow delay.
        let gotUser = false;
        for (let i = 0; i < 5; i++) {
          this.logger.debug(`trying to find user ${googleUserInfo.email} in the database, attempt: ${i}`);
          const user = await this.userRepo.findByEmail(googleUserInfo.email);
          if (user) {
            gotUser = true;
            break;
          }
          await new Promise(resolve => setTimeout(resolve, 1000));
        }

        if (!gotUser) {
          throw new WPError(WP_ERROR_IAM_USERS_NOT_FOUND, 'User not found in the database', undefined, '403');
        }

        const awsUser = await this.customAuthUtils.findUserByEmail(googleUserInfo.email);
        return {
          ...tokens?.AuthenticationResult,
          email: googleUserInfo.email,
          userId: awsUser?.Attributes?.find(attr => attr.Name === 'custom:user_id')?.Value!,
        };
      } catch (error) {
        this.logger.error('Authentication error:', error);
        throw new WPError(WP_ERROR_IAM_USERS_NOT_FOUND, 'Custom auth error', undefined, '403');
      }
    } catch (error) {
      this.logger.error('Authentication error:', error);
      throw new WPError(WP_ERROR_IAM_USERS_NOT_FOUND, 'Custom auth error', undefined, '403');
    }
  }

  async processMobileLoginRegisterApple(appleIdToken: string, appDef?: string): Promise<MobileLoginResponse> {
    this.logger.debug('processMobileLoginApple', { appleIdToken, appDef });

    try {
      // Verify Apple token (fetch public keys from https://appleid.apple.com/auth/keys)
      const applePublicKeys = await axios.get('https://appleid.apple.com/auth/keys');
      const decodedToken = jwt.decode(appleIdToken, { complete: true }) as any;
      
      if (!decodedToken || !decodedToken.header || !decodedToken.header.kid) {
        throw new WPError(WP_ERROR_IAM_USERS_NOT_FOUND, 'Invalid Apple token format', undefined, '403');
      }

      const kid = decodedToken.header.kid;
      const publicKey = applePublicKeys.data.keys.find((key: any) => key.kid === kid);
      if (!publicKey) {
        throw new WPError(WP_ERROR_IAM_USERS_NOT_FOUND, 'Invalid Apple token - key not found', undefined, '403');
      }

      const appleUserInfo = jwt.verify(appleIdToken, jwkToPem(publicKey), { algorithms: ['RS256'] }) as any;

      if (!appleUserInfo.email) {
        throw new WPError(WP_ERROR_IAM_USERS_NOT_FOUND, 'Email not found in Apple token data', undefined, '403');
      }

      const infoSerialized = JSON.stringify(appleUserInfo);
      let tokens = await this.customAuthUtils.authenticateWithApple(appleUserInfo.email, infoSerialized);

      // Retry logic similar to Google
      for (let i = 0; i < 3; i++) {
        this.logger.debug(tokens, 'tokens');
        if (!tokens?.AuthenticationResult) {
          this.logger.debug('RETRYING, PROBABLY NEW USER');
          tokens = await this.customAuthUtils.authenticateWithApple(appleUserInfo.email, infoSerialized);
        }

        if (!tokens?.AuthenticationResult) {
          this.logger.error('Custom auth error', { tokens });
          throw new WPError(WP_ERROR_IAM_USERS_NOT_FOUND, 'Custom auth error', undefined, '403');
        }

        //default delay for the workflow to complete, lame
        await new Promise(resolve => setTimeout(resolve, 1500));

        //TODO: gotta find a better way to do this
        //try to find the user in the database, because the user might not be created yet in the database, workflow delay.
        let gotUser = false;
        for (let j = 0; j < 5; j++) {
          this.logger.debug(`trying to find user ${appleUserInfo.email} in the database, attempt: ${j}`);
          const user = await this.userRepo.findByEmail(appleUserInfo.email);
          if (user) {
            gotUser = true;
            break;
          }
          await new Promise(resolve => setTimeout(resolve, 1000));
        }

        if (!gotUser) {
          throw new WPError(WP_ERROR_IAM_USERS_NOT_FOUND, 'User not found in the database', undefined, '403');
        }

        const awsUser = await this.customAuthUtils.findUserByEmail(appleUserInfo.email);
        return {
          ...tokens?.AuthenticationResult,
          email: appleUserInfo.email,
          userId: awsUser?.Attributes?.find(attr => attr.Name === 'custom:user_id')?.Value!,
        };
      }

      throw new WPError(WP_ERROR_IAM_USERS_NOT_FOUND, 'Max retries exceeded', undefined, '403');
    } catch (error) {
      this.logger.error('Apple authentication error:', error);
      throw error instanceof WPError ? error : new WPError(WP_ERROR_IAM_USERS_NOT_FOUND, 'Apple auth error', undefined, '403');
    }
  }

  async refreshToken(token: string, email: string) {
    const nt = await this.customAuthUtils.refreshToken(token, email);

    if (nt.AuthenticationResult) {
      return nt.AuthenticationResult;
    }

    throw new WPError(WP_ERROR_IAM_USERS_NOT_FOUND, 'Refresh token error', undefined, '403');
  }

  async verifyGoogleToken(token: string) {
    const response = await fetch(`https://oauth2.googleapis.com/tokeninfo?id_token=${token}`);
    if (!response.ok) {
      throw new WPError(WP_ERROR_IAM_USERS_NOT_FOUND, 'Failed to verify Google token', undefined, '403');
    }
    const data = await response.json();
    if (!data.email) {
      throw new WPError(WP_ERROR_IAM_USERS_NOT_FOUND, 'Email not found in Google token data', undefined, '403');
    }
    return {
      email: data.email,
      name: data.name || '',
      sub: data.sub,
    };
  }

  async registerUser(userReg: UserRegistration): Promise<User | undefined> {
    this.logger.debug(`Registering user:${JSON.stringify(userReg)}`);
    let user: User | undefined = await this.register(userReg);
    return user;
  }

  async loginComplete(userId): Promise<void> {
    // messaging removed
    this.logger.debug(`loginComplete event: ${userId}`);
  }

  async kycComplete(userId: string): Promise<void> {
    // messaging removed
    this.logger.debug(`kycComplete event: ${userId}`);
  }
}
