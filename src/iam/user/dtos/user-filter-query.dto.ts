import { Type } from "class-transformer";
import { IsDate, IsInt, IsOptional, IsEmail, IsString } from "class-validator";
import { FilterQueryParams } from "../../../commons/filter-query-param.dto";

export class UserFilterQueryParams extends FilterQueryParams {
    // id is equal to userId
    @IsOptional()
    id?: string;

    @IsOptional()
    gender?: string;

    @IsOptional()
    @IsInt()
    @Type(() => Number)
    limit: number;

    @IsOptional()
    @IsInt()
    @Type(() => Number)
    page: number;

    @IsOptional()
    @IsString()
    sortBy?: string;

    @IsOptional()
    @IsString()
    sortOrder?: string;

    @IsOptional()
    @IsString()
    name?: string;

    //TODO: change default email validation message
    @IsOptional()
    @IsEmail()
    email?: string;

    @IsOptional()
    @IsString()
    nickname?: string;

    @IsOptional()
    @IsString()
    phoneNumber?: string;

    @IsOptional()
    @IsString()
    lastname?: string;

    @IsOptional()
    @IsString()
    address?: string;

    @IsOptional()
    @IsDate()
    @Type(() => Date)
    birthdate?: Date;

    @IsOptional()
    @IsDate()
    @Type(() => Date)
    from_birthdate?: Date;

    @IsOptional()
    @IsDate()
    @Type(() => Date)
    to_birthdate?: Date;

    @IsOptional()
    @IsString()
    referralCode: string;

    @IsOptional()
    @IsString()
    referrerCode: string;
}
