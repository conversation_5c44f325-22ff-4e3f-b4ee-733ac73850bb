import { KYCCLIENTTYPE } from "../entities/user.kyc.entity";

export class UserKycShuftiproDto {
    email: string;
    zoneinfo: string;
    locale: string;
    clientType: KYCCLIENTTYPE;
}

export class UserKycShuftiproResponseDto {
    kycReferenceId: string;
    kycEmail: string;
    kycCountry: string;
    kycFirstName: string;
    kycLastName: string;
    kycNationalId: string;
    kycDateOfBirth: string;
    kycResult: number;
    kycEvent?: string;
}
