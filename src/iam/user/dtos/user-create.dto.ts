import { GenderEnum } from "../../../commons/gender.enum";
import { KYCSTATUS } from "../entities/user.entity";

export class UserCreateDto {
    id?: string;
    uuid?: string;
    name: string;
    lastname:string;
    nickname:string;
    address: string;
    birthdate: Date;
    gender: number;
    image: string;
    password: string;
    phoneNumber: string;
    email: string;
    locale: string;
    zoneinfo: string;
    emailVerifiedAt: Date;
    phoneVerifiedAt: Date;
    phoneVerified:boolean;
    emailVerified:boolean;
    kycEnabled:boolean;
    kycStatus?: KYCSTATUS;
    referralCode: string;
    referrerCode?: string;
    errors?:any[];
}
