import { Column, <PERSON>, PrimaryKey, Table, DataType, Foreign<PERSON>ey, BelongsTo } from "sequelize-typescript";
import { User, UserEntity } from "./user.entity"; // Adjust the import path as needed

@Table({
    tableName: "user_kyc_info",
    version: true,
    timestamps: true,
    paranoid: true,
    omitNull: true,
    deletedAt: true,
})
export class UserKycInfo extends Model {

    @PrimaryKey
    @Column({ type: DataType.INTEGER, autoIncrement: true, allowNull: false })
    id: number;

    @ForeignKey(() => UserEntity)
    @Column({ type: DataType.STRING, allowNull: false, field: "user_id" })
    userId: string;

    @BelongsTo(() => UserEntity)
    user: UserEntity;

    @Column({ type: DataType.STRING, allowNull: false, field: "id_no" })
    idNo: string;

    @Column({ allowNull: false, field: "created_at" })
    createdAt: Date;

    @Column({ allowNull: false, field: "updated_at" })
    updatedAt: Date;

    @Column({ allowNull: true, field: "deleted_at" })
    deletedAt: Date;
}