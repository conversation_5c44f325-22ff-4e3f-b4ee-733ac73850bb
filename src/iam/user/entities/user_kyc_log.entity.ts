import { Column, Model, PrimaryKey, Table, DataType, ForeignKey } from "sequelize-typescript";
import { UserEntity } from "./user.entity";

@Table({
    tableName: "user_kyc_log",
    version: true,
    timestamps: true,
    paranoid: true,
    omitNull: true,
    deletedAt: true,
})
export class UserKycLogEntity extends Model {
    @PrimaryKey
    @Column({ allowNull: true, autoIncrement: true })
    id: number;

    @ForeignKey(() => UserEntity)
    @Column({ allowNull: true, type: DataType.STRING, field: "user_id" })
    userId: string;

    @Column({ allowNull: false, type: DataType.TEXT, field: "data" })
    data: string;

    @Column({ allowNull: false, field: "created_at" })
    createdAt: Date;

    @Column({ allowNull: false, field: "updated_at" })
    updatedAt: Date;

    @Column({ allowNull: true, field: "deleted_at" })
    deletedAt: Date;
}