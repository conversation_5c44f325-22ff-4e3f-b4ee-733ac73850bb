import { Colum<PERSON>, <PERSON>, PrimaryKey, Table, DataType, HasOne } from "sequelize-typescript";
import { UserKycEntity } from "./user.kyc.entity";

export class UserRegistration {
    id: string;
    uuid: string;
    nickname?:string;
    image?:string;
    password: string;
    // username:string; //cognito data
    email: string;
    locale: string;
    zoneinfo: string;
    emailVerifiedAt: Date;
    referralCode: string;
    referrerCode?: string;
}

export class UserCreate {
    id?: string;
    uuid?: string;
    name: string;
    lastname: string;
    nickname: string;
    address: string;
    birthdate: Date;
    gender: number;
    image: string;
    password: string;
    phoneNumber: string;
    email: string;
    locale: string;
    zoneinfo: string;
    kycEnabled: boolean;
    kycStatus?: KYCSTATUS;
    kycRequestedAt?: Date;
    emailVerifiedAt: Date;
    phoneVerifiedAt: Date;
    isAdmin?: boolean;
    cooldownEndAt?: Date;
    rememberToken?: string;
    twoFactorSecret?: string;
    twoFactorRecoveryCodes?: string;
    referralCode: string;
    referrerCode?: string;
}

export class UserUpdate extends UserCreate {
    id: string;
    updatedAt: Date;
}

export class User extends UserUpdate {
    version: number;
    createdAt: Date;
    deletedAt!: Date;
    errors?: any[];
}

export interface UserPagination {
    rows: User[];
    count: number;
}

export enum KYCSTATUS {
    PENDING = 0,
    VERIFIED = 1,
    REJECTED = 2,
    AGE_RESTRICTED = 3,
}

@Table({
    tableName: "users",
    version: true,
    timestamps: true,
    paranoid: true,
    omitNull: true,
})
export class UserEntity extends Model {

    @PrimaryKey
    @Column({ allowNull: false })
    id: string;

    @Column({ allowNull: true })
    name: string;

    @Column({ allowNull: true, field: "last_name" })
    lastname: string;

    @Column({ allowNull: true, field: "uuid" })
    uuid: string;

    @Column({ allowNull: true, field: "nickname" })
    nickname: string;

    @Column({ allowNull: true })
    address: string;

    @Column({ allowNull: true, field: "birthdate", type: DataType.DATEONLY })
    birthdate: Date;

    @Column({ allowNull: true })
    gender: number;

    @Column({ allowNull: true })
    image: string;

    @Column({ allowNull: true })
    password: string;

    @Column({ allowNull: false, unique: "users_email_unique" })
    email: string;

    @Column({ allowNull: true, field: "phone_number" })
    phoneNumber: string;

    @Column({ allowNull: true })
    locale: string;

    @Column({ allowNull: true, field: "zone_info" })
    zoneinfo: string;

    @Column({ allowNull: true, field: "email_verified", type: DataType.BOOLEAN })
    emailVerified: boolean;

    @Column({ allowNull: true, field: "phone_verified", type: DataType.BOOLEAN })
    phoneVerified: boolean;

    @Column({ allowNull: true, field: "kyc_enabled", type: DataType.BOOLEAN })
    kycEnabled: boolean;

    @Column({ allowNull: true, field: "kyc_status", defaultValue: null})
    kycStatus: KYCSTATUS;

    @Column({ allowNull: true, field: "kyc_requested_at" })
    kycRequestedAt: Date;

    @Column({ allowNull: true, field: "email_verified_at" })
    emailVerifiedAt: Date;

    //the register page shown at
    @Column({ allowNull: true, field: "register_seen_at", defaultValue: null })
    registerSeenAt: Date;
    
    //the register page shown at
    @Column({ allowNull: true, field: "utm_source" })
    utmSource: string;

    @Column({ allowNull: true, field: "phone_verified_at" })
    phoneVerifiedAt: Date;

    // needen by game hub php application
    @Column({ allowNull: false, field: "is_admin", type: DataType.BOOLEAN, defaultValue: false })
    isAdmin: boolean;

    @Column({ allowNull: true, field: "cooldown_end_at" })
    cooldownEndAt: Date;

    @Column({ allowNull: true, field: "remember_token", type: DataType.CHAR(100) })
    rememberToken: string;

    @Column({ allowNull: true, field: "two_factor_secret", type: DataType.TEXT })
    twoFactorSecret: string;

    @Column({ allowNull: true, field: "two_factor_recovery_codes", type: DataType.TEXT })
    twoFactorRecoveryCodes: string;

    @Column({ allowNull: true, type: DataType.CHAR(10), field: "referral_code", unique:true })
    referralCode: string;

    @Column({ allowNull: true, type: DataType.CHAR(10), field: "referrer_code" })
    referrerCode?: string;

    @HasOne(() => UserKycEntity, { foreignKey: 'userId', sourceKey: 'id' })
    kyc: UserKycEntity;
}
