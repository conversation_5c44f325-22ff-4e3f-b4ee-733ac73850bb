import { Column, Model, PrimaryKey, Table, DataType, Index } from "sequelize-typescript";

export enum KYCCLIENTTYPE {
    GAMEHUB = "GAMEHUB",
    DESKTOP = "DESKTOP",
    WALLET_WEB = "WALLET_WEB",
    WALLET_MOBILE = "WALLET_MOBILE",
}

@Table({
    tableName: "user_kyc",
    version: true,
    timestamps: true,
    paranoid: true,
    omitNull: true,
    deletedAt: true,
})
export class UserKycEntity extends Model {

    @PrimaryKey
    @Column({ allowNull: false })
    id: string;

    @Column({ allowNull: true, field: "user_id" })
    userId: string;

    @Column({ allowNull: true, field: "reference_id" })
    referenceId: string;

    @Column({ allowNull: true, field: "client_type" })
    clientType: KYCCLIENTTYPE;

    @Column({ allowNull: true, field: "email" })
    email: string;

    @Column({ allowNull: true, field: "country" })
    country: string;

    @Column({ allowNull: true, field: "first_name" })
    firstName: string;

    @Column({ allowNull: true, field: "last_name" })
    lastName: string;

    @Column({ allowNull: true, field: "national_id" })
    nationalId: string;

    @Column({ allowNull: true, field: "date_of_birth" })
    dateOfBirth: string;

    @Column({ allowNull: true, field: "result" })
    result: number;

    @Column({ allowNull: true, field: "declined_reason" })
    declinedReason: string;

    @Column({ allowNull: true, field: "declined_codes" })
    declinedCodes: string;

    @Column({ allowNull: true, field: "created_at" })
    createdAt: Date;

    @Column({ allowNull: true, field: "updated_at" })
    updatedAt: Date;

    @Column({ allowNull: true, field: "deleted_at" })
    deletedAt: Date;
}
