export  enum  GenderEnum {
    MALE = "MALE",
    FEMALE = "FEMALE",
    OTHER = "OTHER"
}

export type GenderId = 1 | 2 | 3;

export const genders: string[] = Object.values(GenderEnum);

export function genderToNumber(gender: GenderEnum): GenderId {
    switch (gender) {
      case GenderEnum.MALE:
        return 1;
      case GenderEnum.FEMALE:
        return 2;
      case GenderEnum.OTHER:
        return 3;
      default:
        throw new Error(`Invalid GenderEnum value: ${gender}`);
    }
  }
  
  // Convert GenderId to GenderEnum
  export function numberToGender(id: GenderId): GenderEnum {
    switch (id) {
      case 1:
        return GenderEnum.MALE;
      case 2:
        return GenderEnum.FEMALE;
      case 3:
        return GenderEnum.OTHER;
      default:
        throw new Error(`Invalid GenderId value: ${id}`);
    }
  }
