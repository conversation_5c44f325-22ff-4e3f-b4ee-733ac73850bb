import { Injectable, Logger } from "@nestjs/common";
import { InjectModel } from "@nestjs/sequelize";
import { Sequelize } from "sequelize-typescript";
import { KYCSTATUS, User, UserCreate, UserEntity, UserPagination, UserRegistration, UserUpdate } from "../entities/user.entity";
import { WPError } from "../../../commons/error/wp.error";
import {
    WP_ERROR_IAM_USER_NOT_CREATED,
    WP_ERROR_IAM_USER_NOT_UPDATED,
    WP_ERROR_IAM_USER_NOT_PURGED,
    WP_ERROR_IAM_USER_NOT_FOUND_BY_ID,
    WP_ERROR_IAM_USER_NOT_FOUND,
    WP_ERROR_IAM_USERS_NOT_FOUND,
    WP_ERROR_IAM_USER_VALIDATION_PARAMS,
    WP_ERROR_IAM_USER_PROFILE_KYC_ERROR,
    WP_ERROR_IAM_USER_NOT_FOUND_BY_EMAIL,
} from "../../../commons/error/error.codes";
import { QueryFilter, QueryFilterParam, QueryFilterSortParam, QueryParams, Where } from "../../../commons/pagination/query-filter";
import { QueryFilterUtil } from "../../../commons/pagination/query-filter.util";
import * as util from "util";
import { UserErrorUtils } from "../utils/user-error-utils";
import { Op } from "sequelize";
import { UserKycShuftiproDto, UserKycShuftiproResponseDto } from "../dtos/user.kyc.dto";
import { v4 as uuidv4 } from "uuid";
import { UserKycEntity } from "../entities/user.kyc.entity";
import { Ulid } from "../../../commons/id/ulid/uild";
import { ShuftiProRestClient } from "../../../commons/clients/shuftipro-rest-client";
import { Transactional } from "sequelize-transactional-decorator";
import { UserContext } from "../../../commons/auth/user-context";
import { join } from "path";
import { UserKycLogEntity } from "../entities/user_kyc_log.entity";
import { UserKycLogRepository } from "./user_kyc_log.repository";
import { UserKycLogDto } from "../dtos/user.kyc.log.dto";
const fs = require("fs");
const cassandra = require("cassandra-driver");
@Injectable()
export class UserRepository {
    private readonly logger = new Logger(UserRepository.name);

    private cass_inited = false;
    private cassClient: any;

    constructor(
        @InjectModel(UserEntity)
        private readonly userEntity: typeof UserEntity,
        @InjectModel(UserKycEntity)
        private readonly userKycEntity: typeof UserKycEntity,
        private readonly userKycLogRepository: UserKycLogRepository,
        private sequelize: Sequelize,
        private readonly shuftiProRestClient: ShuftiProRestClient,
    ) {}

    async connectToCassandra() {
        if (!this.cass_inited) {
            const auth = new cassandra.auth.PlainTextAuthProvider(process.env.KEYSPACE_USER, process.env.KEYSPACE_PASS);
            const certFilePath = join(__dirname, "..", "..", "..", "..", "sf-class2-root.crt");
            const sslOptions = {
                ca: [fs.readFileSync(certFilePath, "utf-8")],
                host: process.env.KEYSPACE_HOST,
                rejectUnauthorized: true,
            };
            this.cassClient = new cassandra.Client({
                contactPoints: [process.env.KEYSPACE_HOST],
                localDataCenter: process.env.AWS_REGION,
                authProvider: auth,
                sslOptions: sslOptions,
                consistency: cassandra.types.consistencies.localQuorum,
                logEmitter: function log(level: any, className: any, message: any, furtherInfo: any) {
                    // console.log("Cassandra client log:", level, className, message, furtherInfo);
                },
                protocolOptions: { port: 9142 },
            });

            await this.cassClient.connect();
            this.cass_inited = true;
        }
    }

    async create(userCreate: UserCreate): Promise<User | undefined> {
        if (!userCreate) {
            throw new WPError(WP_ERROR_IAM_USER_VALIDATION_PARAMS, `User can not be null.`, undefined, "403");
        }
        this.logger.debug(`creating user:${JSON.stringify(userCreate)}`);

        let user: User | undefined = undefined;

        try {
            user = (await this.userEntity.create<UserEntity>({ ...userCreate })) as User;
            return user;
        } catch (error) {
            this.logger.error("An error occurred while creating a user", error);
            UserErrorUtils.handleORMError(error, WP_ERROR_IAM_USER_NOT_CREATED);
        }
    }

    async register(userReg: UserRegistration): Promise<User | undefined> {
        if (!userReg) {
            throw new WPError(WP_ERROR_IAM_USER_VALIDATION_PARAMS, `User can not be null.`, undefined, "403");
        }
        this.logger.debug(`registering user:${JSON.stringify(userReg)}`);

        let user: User | undefined = undefined;

        try {
            user = (await this.userEntity.create<UserEntity>({ ...userReg })) as User;
            return user;
        } catch (error) {
            this.logger.error("an error occurred while registering a user", error);
            throw new WPError(WP_ERROR_IAM_USER_NOT_CREATED, `${error.message}`, error.stack, "500");
        }
    }

    async checkIpExistsAWS(ip: string): Promise<any[]> {
        await this.connectToCassandra();
        const query = "SELECT * FROM wodo_reg.ip_req WHERE ip = ? AND pool_id = ?";
        const result = await this.cassClient.execute(query, [ip, process.env.AWS_COGNITO_USER_POOL_ID], { prepare: true });
        return result.rows;
    }

    async verifyIp(key: string): Promise<any[]> {
        await this.connectToCassandra();
        const query = "UPDATE wodo_reg.ip SET verified=1 WHERE key=? AND pool_id=?";
        const result = await this.cassClient.execute(query, [key, process.env.AWS_COGNITO_USER_POOL_ID], { prepare: true });
        return result;
    }

    async prereg(ip: string) {
        this.logger.debug(`pre-registering user`);

        try {
            await this.connectToCassandra();

            const prvIps = await this.checkIpExistsAWS(ip);
            if (prvIps && prvIps.length > 0) {
                this.logger.log(`Ip already exists in AWS Keyspaces`, prvIps);
                prvIps.forEach((element: any) => {
                    if (Number(element.verified) == 0) return element.key;
                    else return "";
                });
            }
            const key = Ulid.getId(),
                batchQueries = [
                    {
                        query: "INSERT INTO wodo_reg.ip_req (ip, pool_id, date, key) VALUES (?, ?, ?, ?)",
                        params: [ip, process.env.AWS_COGNITO_USER_POOL_ID, Date.now(), key],
                    },
                    {
                        query: "INSERT INTO wodo_reg.ip_req_server (ip, pool_id, date, key) VALUES (?, ?, ?, ?)",
                        params: [ip, process.env.AWS_COGNITO_USER_POOL_ID, Date.now(), key],
                    },
                ];

            await this.cassClient.batch(batchQueries, { prepare: true, logged: false, consistency: cassandra.types.consistencies.localQuorum }).catch((err: any) => {
                console.log("ERROR WRITING IP : ", err);
            });

            return key;
        } catch (error) {
            this.logger.error("an error occurred while prereg a user", error);
            throw new WPError(WP_ERROR_IAM_USER_NOT_CREATED, `${error.message}`, error.stack, "500");
        }
    }

    async deleteEmailFromKS(email: string) {
        this.logger.debug(`deleteEmailFromKS`);
        try {
            await this.connectToCassandra();
            const modifiedEmail = email.replace(/\./g, '').toLowerCase();

            const query = "DELETE FROM wodo_reg.email WHERE email = ? AND pool_id = ?";
            const result = await this.cassClient.execute(query, [modifiedEmail, process.env.AWS_COGNITO_USER_POOL_ID], {
                prepare: true,
                consistency: cassandra.types.consistencies.localQuorum,
            });
            return result.rows;
        } catch (error) {
            this.logger.error("an error occurred while ks deleting a user", error);
            throw new WPError(WP_ERROR_IAM_USER_NOT_CREATED, `${error.message}`, error.stack, "500");
        }
    }

    async shuftiRequest(kycRequestDto: UserKycShuftiproDto): Promise<any> {
        try {
            // Shufti pro doesnt allow same reference id for multiple requests
            let reference = Ulid.getId();
            const callbackUrl = `${process.env.SHUFTIPRO_CALLBACK_URL}`;

            let user = await this.userEntity.findOne({
                where: { email: kycRequestDto.email },
            });

            if (!user) {
                throw new WPError(WP_ERROR_IAM_USER_NOT_FOUND, `by email:${kycRequestDto.email}`, undefined, "404");
            }

            if (user.kycRequestedAt && (new Date().getTime() - new Date(user.kycRequestedAt).getTime()) < 12 * 60 * 60 * 1000) {
                await this.userKycLogRepository.create({
                    userId: user.id,
                    data: 'KYC_REQUEST_LIMIT_EXCEEDED',
                    createdAt: new Date(),
                    updatedAt: new Date(),  
                });
                throw new WPError(WP_ERROR_IAM_USER_PROFILE_KYC_ERROR, `KYC Verification request limit exceeded, you can try every 12 hours.`, undefined, "403");
            }
            const userKYC = await this.userKycEntity.findOne({
                where: { email: user.email },
            });

            // Should we keep the previous kyc request which is not completed?
            if (userKYC && (user.kycStatus == KYCSTATUS.VERIFIED || user.kycStatus == KYCSTATUS.AGE_RESTRICTED)) {
                return;
            }
            
            if (userKYC && user.kycStatus == KYCSTATUS.PENDING) {
                await this.userKycEntity.destroy({ where: { email: kycRequestDto.email }, force: true });
            }

            const payload = {
                reference,
                callback_url: callbackUrl,
                email: kycRequestDto.email,
                country: kycRequestDto.locale,
                document: {
                    proof: '',
                    additional_proof: '',
                    supported_types: ['id_card', 'driving_license', 'passport'],
                    name: '',
                    dob: '',
                    national_id: '',
                    allow_offline: 0,
                    allow_online: 1,
                },
                language: kycRequestDto.locale,
                show_feedback_form: 0,
                allow_retry: 1
            };

            const credentials = `${process.env.SHUFTIPRO_CID}:${process.env.SHUFTIPRO_SC}`;
            const token = btoa(credentials);

            const response = await this.shuftiProRestClient.shuftiRequest(payload, token);

            if (response.statusCode != 200) {
                throw new WPError(WP_ERROR_IAM_USER_PROFILE_KYC_ERROR, `ShuftiPro request failed.`, undefined, "403");
            }

            let data;

            try {
                data = JSON.parse(response.body);
            } catch (error) {
                this.logger.error(`An error occurred while parsing shufti pro response`, error);
                throw error;
            }

            if (payload.reference != data.reference) {
                throw new WPError(WP_ERROR_IAM_USER_PROFILE_KYC_ERROR, `ShuftiPro reference mismatch.`, undefined, "403");
            }


            await this.userEntity.update(
                {
                    kycStatus: KYCSTATUS.PENDING,
                    kycRequestedAt: new Date(),
                },
                {
                    where: { email: user.email },
                },
            );

            await this.userKycEntity.create<UserKycEntity>({
                id: Ulid.getId(),
                userId: user.id,
                referenceId: payload.reference,
                clientType: kycRequestDto.clientType,
                email: user.email,
                country: null,
                firstName: null,
                lastName: null,
                nationalId: null,
                dateOfBirth: null,
                result: null,
                declinedReason: null,
                declinedCodes: null,
            });

            return {
                ...data,
                kycRequestedAt: new Date(),
            };
        } catch (error) {
            this.logger.error(`An error occurred while sending shuftipro request`, error);
            throw error;
        }
    }

    async update(id: string, userUpdate: UserUpdate): Promise<UserEntity> {
        this.logger.debug(`updating ${UserEntity.name}:${util.inspect(userUpdate)}`);

        let user: UserEntity | undefined = await this.findById(id);
        if (!user) {
            throw new WPError(WP_ERROR_IAM_USER_NOT_FOUND_BY_ID, `by id:${id}`, undefined, "404");
        }

        const userNick: UserEntity | null = await this.userEntity.findOne({
            where: [{ id: { [Op.ne]: id } }, { nickname: userUpdate.nickname }],
        });

        if (userNick) {
            throw new WPError(WP_ERROR_IAM_USER_NOT_UPDATED, `Message:NICKNAME_NOT_UNIQUE`, "NOT UNIQUE NICKNAME", "403");
        }

        try {
            user = await user.update({ ...userUpdate });
        } catch (error) {
            this.logger.error(`An internal error occurred while updating user by id:${id}, user:${util.inspect(userUpdate)}`, error);
            throw new WPError(WP_ERROR_IAM_USER_NOT_UPDATED, `Message:${error.message}`, error.stack, "500");
        }

        this.logger.debug(`updated ${UserEntity.name}:${util.inspect(user)}`);

        return user;
    }

    async updateUserIamge(id: string, image: string): Promise<void> {
        this.logger.debug(`updating ${UserEntity.name}:${id} with image:${image}`);
        let result;
        try {
            result = await this.userEntity.update(
                {
                    image,
                },
                {
                    where: { id },
                },
            );
        } catch (error) {
            this.logger.error(`An internal error occurred while updating user image by id:${id}, user:${id}`, error);
            throw new WPError(WP_ERROR_IAM_USER_NOT_UPDATED, `Message:${error.message}`, error.stack, "500");
        }

        if (!result[0] || result[0] < 1) {
            throw new WPError(WP_ERROR_IAM_USER_NOT_FOUND_BY_ID, `by id:${id}`, undefined, "404");
        }

        this.logger.debug(`updated ${UserEntity.name}:${id} with image:${image}`);
    }

    async updateReferalCode(id: string, referralCode: string): Promise<void> {
        this.logger.debug(`updating ${UserEntity.name}:${id} with referralCode:${referralCode}`);
        let result;
        try {
            result = await this.userEntity.update(
                {
                    referralCode,
                },
                {
                    where: { id },
                },
            );
        } catch (error) {
            this.logger.error(`An internal error occurred while updating user referralCode by id:${id}, user:${id}`, error);
            throw new WPError(WP_ERROR_IAM_USER_NOT_UPDATED, `Message:${error.message}`, error.stack, "500");
        }

        if (!result[0] || result[0] < 1) {
            throw new WPError(WP_ERROR_IAM_USER_NOT_FOUND_BY_ID, `by id:${id}`, undefined, "404");
        }

        this.logger.debug(`updated ${UserEntity.name}:${id} with referralCode:${referralCode}`);
    }

    async getWithFilter(queryFilter: QueryFilter): Promise<UserPagination> {
        const params: Map<string, QueryParams> = queryFilter.params;
        let orderClause: any = [];

        const filterParams = params.get(UserEntity.name);
        const where: Where = QueryFilterUtil.processWhereClause(filterParams?.filterParams ?? new Map<string, QueryFilterParam>());
        orderClause = QueryFilterUtil.processOrderClause(filterParams?.sortParams ?? new Map<string, QueryFilterSortParam>(), "updated_at", "DESC");
        this.logger.debug(`generated where clause ${util.inspect(where)} and order clause ${util.inspect(orderClause)}`);

        let result;
        try {
            result = await this.userEntity.findAndCountAll({
                where: where.whereClause,
                bind: where.bindClause,
                order: [orderClause],
                limit: queryFilter.pagination.limit,
                offset: queryFilter.pagination.offset,
            });
        } catch (error) {
            this.logger.error(`An error occurred while finding ${UserEntity.name}s `, error);
            throw new WPError(WP_ERROR_IAM_USERS_NOT_FOUND, `message:${error.message}`, error.stack, "500");
        }

        this.logger.debug(`found ${UserEntity.name} count:${util.inspect(result ? result.rows.length : 0)}`);

        const users: User[] = result.rows as User[];
        const count: number = result.count;
        const grPagination: UserPagination = {
            rows: users,
            count: count,
        };

        return grPagination;
    }

    // note: we don't have any endpoints for this method
    async findAll(): Promise<User[]> {
        this.logger.debug(`finding all users`);

        let users;

        try {
            users = (await this.userEntity.findAll()) as unknown as User[];
        } catch (error) {
            this.logger.error(`An error occurred while finding users`, error);
            throw new WPError(WP_ERROR_IAM_USERS_NOT_FOUND, `message: ${error.message}`, error.stack, "500");
        }

        if (users) {
            return users;
        } else {
            this.logger.error(`Could not find any users`);
            return [];
        }
    }

    // note: we don't have any endpoints for this method
    async purgeById(id: string): Promise<void> {
        if (!id) {
            throw new WPError(WP_ERROR_IAM_USER_VALIDATION_PARAMS, `Id can not be null.`, undefined, "403");
        }

        let count = 0;
        this.logger.debug(`purged user row(s): ${count} by id:${id}`);

        try {
            count = await this.userEntity.destroy({ where: { id }, force: true });
        } catch (error) {
            this.logger.error(`An error occurred while finding user record in the datastore by id:${id}`);
            throw new WPError(WP_ERROR_IAM_USER_NOT_PURGED, `by id:${id}, message: ${error.message}`, error.stack, "500");
        }

        if (!count || count == 0) {
            throw new WPError(WP_ERROR_IAM_USER_NOT_FOUND_BY_ID, `by id:${id}`, undefined, "404");
        } else {
            this.logger.log("user record successfully purged");
        }
    }

    async deleteById(id: string): Promise<void> {
        if (!id) {
            throw new WPError(WP_ERROR_IAM_USER_VALIDATION_PARAMS, `Id can not be null.`, undefined, "403");
        }

        let count = 0;
        try {
            console.log("USERID : " + id);
            count = await this.userEntity.destroy({ where: { id }, force: false });
        } catch (error) {
            this.logger.error(`An error occurred while finding user record in the datastore by id:${id}`);
            throw new WPError(WP_ERROR_IAM_USER_NOT_PURGED, `by id:${id}, message: ${error.message}`, error.stack, "500");
        }

        if (!count || count == 0) {
            throw new WPError(WP_ERROR_IAM_USER_NOT_FOUND_BY_ID, `by id:${id}`, undefined, "404");
        } else {
            this.logger.log("user record successfully deleted");
        }
    }

    // note: we don't have any endpoints for this method
    async findById(id: string): Promise<UserEntity | undefined> {
        this.logger.debug(`finding user by id: ${id}`);
        if (!id) {
            throw new WPError(WP_ERROR_IAM_USER_VALIDATION_PARAMS, `Id can not be null.`, undefined, "403");
        }

        let user: UserEntity | null;

        try {
            user = await this.userEntity.findByPk<UserEntity>(id, { raw: false });
        } catch (error) {
            this.logger.error(`An error occurred while finding user record in the datastore by id: ${id}`, error);
            throw new WPError(WP_ERROR_IAM_USER_NOT_FOUND, `by id: ${id}, message: ${error.message}`, error.stack, "500");
        }

        if (user) {
            this.logger.debug(`found user entity: ${JSON.stringify(user)} in the datastore by id: ${id}`);
        } else {
            this.logger.debug(`could not find any user record in the datastore by id: ${id} - 1`);
            throw new WPError(WP_ERROR_IAM_USER_NOT_FOUND_BY_ID, `by id: ${id}`, undefined, "404");
        }

        return user;
    }

    async findByEmail(email: string): Promise<UserEntity | undefined> {
        this.logger.debug(`finding user by email: ${email}`);
        if (!email) {
            throw new WPError(WP_ERROR_IAM_USER_VALIDATION_PARAMS, `email can not be null.`, undefined, "403");
        }

        let user: UserEntity | null;

        try {
            user = await this.userEntity.findOne<UserEntity>({ where: { email: email }, raw: false });
        } catch (error) {
            this.logger.error(`An error occurred while finding user record in the datastore by email: ${email}`, error);
            throw new WPError(WP_ERROR_IAM_USER_NOT_FOUND, `by email: ${email}, message: ${error.message}`, error.stack, "500");
        }

        if (user) {
            this.logger.debug(`found user entity: ${JSON.stringify(user)} in the datastore by email: ${email}`);
        } else {
            this.logger.debug(`could not find any user record in the datastore by email: ${email}`);
            return undefined;
        }

        return user;
    }

    async findByUuid(uuid: string): Promise<UserEntity | undefined> {
        this.logger.debug(`finding user by uuid: ${uuid}`);
        if (!uuid) {
            throw new WPError(WP_ERROR_IAM_USER_VALIDATION_PARAMS, `uuid can not be null.`, undefined, "403");
        }

        let user: UserEntity | null;

        try {
            user = await this.userEntity.findOne<UserEntity>({ where: { uuid: uuid }, raw: false });
        } catch (error) {
            this.logger.error(`An error occurred while finding user record in the datastore by uuid: ${uuid}`, error);
            throw new WPError(WP_ERROR_IAM_USER_NOT_FOUND, `by email: ${uuid}, message: ${error.message}`, error.stack, "500");
        }

        if (user) {
            this.logger.debug(`found user entity: ${JSON.stringify(user)} in the datastore by uuid: ${uuid}`);
        } else {
            this.logger.debug(`could not find any user record in the datastore by uuid: ${uuid}`);
            return undefined;
        }

        return user;
    }

    async findOneById(id: string, userContext?: UserContext): Promise<UserEntity | undefined> {
        this.logger.debug(`finding user by id: ${id}`);
        if (!id) {
            throw new WPError(WP_ERROR_IAM_USER_VALIDATION_PARAMS, `Id can not be null.`, undefined, "403");
        }

        let user: UserEntity | null;

        try {
            if (!userContext) {
                user = await this.userEntity.findByPk(id);
            } else {
                let where = { id: id };
                user = await this.userEntity.findOne<UserEntity>({ where });
            }
        } catch (error) {
            this.logger.error(`An error occurred while finding user record in the datastore by id: ${id}`, error);
            throw new WPError(WP_ERROR_IAM_USER_NOT_FOUND, `by id: ${id}, message: ${error.message}`, error.stack, "500");
        }

        if (user) {
            this.logger.debug(`found user entity: ${JSON.stringify(user)} in the datastore by id: ${id}`);
        } else {
            this.logger.debug(`could not find any user record in the datastore by id: ${id} - 2`);
            throw new WPError(WP_ERROR_IAM_USER_NOT_FOUND_BY_ID, `by id: ${id}`, undefined, "404");
        }

        return user;
    }

    async findByIds(ids: string[]): Promise<UserEntity[] | undefined> {
        this.logger.debug(`finding user by ids: ${ids}`);
        if (!(ids.length > 0)) {
            throw new WPError(WP_ERROR_IAM_USER_VALIDATION_PARAMS, `Id can not be null.`, undefined, "403");
        }

        //swagger sends the array as string
        //@ts-ignore
        ids = ids.split(",");

        let users: UserEntity[];

        try {
            console.log(ids.length);
            console.log("*************");
            users = await this.userEntity.findAll<UserEntity>({ where: { id: { [Op.in]: ids } }, logging: console.log });
        } catch (error) {
            this.logger.error(`An error occurred while finding user record in the datastore by id: ${ids}`, error);
            throw new WPError(WP_ERROR_IAM_USER_NOT_FOUND, `by ids: ${ids}, message: ${error.message}`, error.stack, "500");
        }

        if (users) {
            this.logger.debug(`found user entity: ${JSON.stringify(users)} in the datastore by ids: ${ids}`);
        } else {
            this.logger.debug(`could not find any user record in the datastore by ids: ${ids}`);
            throw new WPError(WP_ERROR_IAM_USER_NOT_FOUND_BY_ID, `by id: ${ids}`, undefined, "404");
        }

        return users;
    }
    async registerComplete(id: string, utmSource: string, refCode: string): Promise<void> {
        if (!id) {
            throw new WPError(WP_ERROR_IAM_USER_VALIDATION_PARAMS, `Id can not be null.`, undefined, "403");
        }

        let count = 0;
        try {
            console.log("USERID : " + id);
            const result = await this.userEntity.update(
                {
                    utmSource: utmSource,
                    referrerCode: refCode,
                    registerSeenAt: new Date().toISOString(),
                },
                {
                    where: {
                        id: id,
                        registerSeenAt: {
                            [Op.is]: null,
                        },
                    },
                },
            );
        } catch (error) {
            this.logger.error(`An error occurred while finding user record in the datastore by id:${id}`);
            throw new WPError(WP_ERROR_IAM_USER_NOT_PURGED, `by id:${id}, message: ${error.message}`, error.stack, "500");
        }
    }

    async isRegisterComplete(id: string): Promise<number> {
        if (!id) {
            throw new WPError(WP_ERROR_IAM_USER_VALIDATION_PARAMS, `Id can not be null.`, undefined, "403");
        }

        let count = 0;
        try {
            console.log("USERID : " + id);
            // const userNick: UserEntity = await this.userEntity.findOne({
            //     where: [{ id: { [Op.ne]: id } }, { nickname: userUpdate.nickname }],
            // });

            count = await this.userEntity.count({
                where: {
                    id: id,
                    registerSeenAt: {
                        [Op.ne]: null,
                    },
                },
                logging: console.log,
            });
        } catch (error) {
            this.logger.error(`An error occurred while finding user record in the datastore by id:${id}`);
            throw new WPError(WP_ERROR_IAM_USER_NOT_FOUND_BY_ID, `by id:${id}, message: ${error.message}`, error.stack, "500");
        }

        if (!count || count == 0) {
            return 0;
        } else {
            this.logger.log("user reg control done");

            return 1;
        }
    }

    async resetKycRequestedAt(id: string): Promise<void> {
        await this.userEntity.update(
            { kycRequestedAt: null },
            {
                where: {
                    id,
                    kycRequestedAt: {
                        [Op.lt]: new Date(new Date().getTime() - 24 * 60 * 60 * 1000),
                    },
                    kycStatus: KYCSTATUS.PENDING,
                },
            }
        );
    }
}
