import { Injectable, Logger, OnModuleInit } from "@nestjs/common";
import { UserRepository } from "./user.repository";


@Injectable()
export class UserSeederRepository implements OnModuleInit {
    private logger: Logger = new Logger(UserRepository.name);

    constructor(private readonly userRepo: UserRepository) { }

    async onModuleInit() {
        this.logger.log(`${UserRepository.name} onModuleInit...`);
        if ("true" == (process.env.DB_SEED || "false")) {
        }
    }


}
