import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { UserKycLogEntity } from '../entities/user_kyc_log.entity';

@Injectable()
export class UserKycLogRepository {
    constructor(
        @InjectModel(UserKycLogEntity)
        private readonly userKycLogEntity: typeof UserKycLogEntity,
    ) {}

    async create(data: Partial<UserKycLogEntity>): Promise<UserKycLogEntity> {
        return this.userKycLogEntity.create(data);
    }

    async findAll(): Promise<UserKycLogEntity[]> {
        return this.userKycLogEntity.findAll();
    }

    async findOne(id: number): Promise<UserKycLogEntity | null> {
        return this.userKycLogEntity.findByPk(id);
    }

    async update(id: number, data: Partial<UserKycLogEntity>): Promise<[number, UserKycLogEntity[]]> {
        return this.userKycLogEntity.update(data, { where: { id }, returning: true });
    }

    async remove(id: number): Promise<number> {
        return this.userKycLogEntity.destroy({ where: { id } });
    }
}