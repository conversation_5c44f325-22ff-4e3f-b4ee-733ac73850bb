import { Injectable, Logger } from "@nestjs/common";
import { UserKycEntity } from "../entities/user.kyc.entity";
import { InjectModel } from "@nestjs/sequelize";
import { WPError } from "../../../commons/error/wp.error";
import {
    WP_ERROR_IAM_USER_IDS_DONT_MATCH,
    WP_ERROR_IAM_USER_KYC_DUPLICATE_NATIONAL_ID,
    WP_ERROR_IAM_USER_KYC_NOT_FOUND,
    WP_ERROR_IAM_USER_NOT_FOUND,
} from "../../../commons/error/error.codes";
import { UserKycShuftiproResponseDto } from "../dtos/user.kyc.dto";
import { KYCSTATUS, UserEntity } from "../entities/user.entity";
import { Op } from "sequelize";
import { NotificationService } from "../../../commons/notifications/notification.service";
import { NotificationsDto } from "../../../commons/notifications/notifications-dto";
import * as util from "util";
import { NotificationSendDto } from "../../../commons/notifications/notification-send.dto";
import { NotificationChannelType } from "../../../commons/notifications/enums";
import { UserKycLogRepository } from "./user_kyc_log.repository";
import { UserKycLogDto } from "../dtos/user.kyc.log.dto";
 

@Injectable()
export class UserKycRepository {
    private readonly logger = new Logger(UserKycRepository.name);

    constructor(
        @InjectModel(UserKycEntity)
        private readonly userKycEntity: typeof UserKycEntity,
        @InjectModel(UserEntity)
        private readonly userEntity: typeof UserEntity,
        private readonly userKycLogRepository: UserKycLogRepository,
        private readonly notificationService: NotificationService,
    ) {}

    async create(userKyc: UserKycEntity): Promise<UserKycEntity> {
        const user = (await this.userKycEntity.create<UserKycEntity>({ ...userKyc })) as UserKycEntity;
        return user;
    }

    async update(userKyc: UserKycEntity): Promise<UserKycEntity> {
        let user: UserKycEntity | null = await this.userKycEntity.findByPk(userKyc.id);
        if (!user) {
            throw new WPError(WP_ERROR_IAM_USER_KYC_NOT_FOUND);
        }

        user = (await user.update({ ...userKyc })) as UserKycEntity;
        return user;
    }

    async findById(id: string): Promise<UserKycEntity | null> {
        const userKyc: UserKycEntity | null = await this.userKycEntity.findByPk(id);
        return userKyc;
    }

    async delete(id: string): Promise<void> {
        const userKyc: UserKycEntity | null = await this.userKycEntity.findByPk(id);
        if (!userKyc) {
            throw new WPError(WP_ERROR_IAM_USER_KYC_NOT_FOUND);
        }

        await userKyc.destroy();
    }

    async shuftiKycResult(kycResultDto: any): Promise<UserKycEntity> {
        this.logger.log(kycResultDto);
        try {
            this.logger.debug("For reference id: " + kycResultDto.reference + " verification status is => " + kycResultDto.event);

            const userKyc: UserKycEntity | null = await this.userKycEntity.findOne({
                where: { referenceId: kycResultDto.reference },
            });

            if (!userKyc) {
                this.logger.error(`An error occured while finding the user with kyc reference id:${kycResultDto.reference}`);
                // await this.createUserKycLog({ userId: kycResultDto.reference, data: 'KYC_NOT_FOUND' });
                throw new WPError(WP_ERROR_IAM_USER_KYC_NOT_FOUND, `by kycReferenceId:${kycResultDto.reference}`, undefined, "404");
            }

            const user: UserEntity | null = await this.userEntity.findOne({
                where: { email: userKyc.email },
            });

            if (!user) {
                this.logger.error(`An error occured while finding the user with email:${userKyc.email}`);
                await this.createUserKycLog({ userId: userKyc.userId, data: "USER_NOT_FOUND" });
                throw new WPError(WP_ERROR_IAM_USER_NOT_FOUND, `by email:${userKyc.email}`, undefined, "404");
            }

            if (user.id !== userKyc.userId) {
                this.logger.error(`User ids dont match: ${user.id} - ${userKyc.userId}`);
                await this.createUserKycLog({ userId: userKyc.userId, data: "USER_IDS_DONT_MATCH" });
                throw new WPError(WP_ERROR_IAM_USER_IDS_DONT_MATCH, `by email:${userKyc.email}`, undefined, "404");
            }

            if (kycResultDto.event == "request.pending") {
                this.logger.debug("Shuftipro link generated for reference id: " + kycResultDto.reference + " verification status is => " + kycResultDto.event);
                await this.createUserKycLog({ userId: userKyc.userId, data: "REQUEST_PENDING" });
                return userKyc;
            }

            if (kycResultDto.event == "request.timeout" || kycResultDto.event == "verification.cancelled" || kycResultDto.event == "request.deleted") {
                this.logger.debug(
                    "Shuftipro verification " + kycResultDto.event + " for reference id: " + kycResultDto.reference + " verification status is => " + kycResultDto.event,
                );

                await this.userEntity.update(
                    {
                        kycStatus: null,
                        kycRequestedAt: null,
                    },
                    {
                        where: { email: user.email },
                    },
                );

                await this.createUserKycLog({ userId: userKyc.userId, data: "REQUEST_TIMEOUT " + JSON.stringify(kycResultDto) });
                return userKyc;
            }

            if (kycResultDto.verification_result.document.document == 1) {
                // Check if national ID exists in the database
                // disabled because we can't get the national ID from shuftipro :(
/*                 if (
                    kycResultDto.verification_data.ekyc.personal_details &&
                    kycResultDto.verification_data.ekyc.personal_details.national_id &&
                    kycResultDto.verification_data.ekyc.personal_details.national_id !== ""
                ) {
                    const existingUserKyc = await this.userKycEntity.findOne({
                        where: {
                            nationalId: kycResultDto.verification_data.ekyc.personal_details.national_id,
                            id: { [Op.ne]: userKyc.id }, // Exclude the current userKyc
                        },
                    });

                    this.logger.log("Existing user kyc: " + existingUserKyc);

                    if (existingUserKyc) {
                        await this.createUserKycLog({ userId: userKyc.userId, data: "DUPLICATE_NATIONAL_ID " + JSON.stringify(existingUserKyc.id) });
                        await this.sendNotification(userKyc, "Duplicate National ID", "The national ID you declared has been used before.");
                        await this.userEntity.update({ kycStatus: KYCSTATUS.REJECTED }, { where: { email: userKyc.email } });
                        throw new WPError(WP_ERROR_IAM_USER_KYC_DUPLICATE_NATIONAL_ID, `National ID already exists in the database`, undefined, "409");
                    }
                }
 */
                if (kycResultDto.verification_data.document) {
                    const dob = kycResultDto.verification_data.document.dob;

                    const ageDifMs = Date.now() - new Date(dob).getTime();
                    const ageDate = new Date(ageDifMs);
                    const age = Math.abs(ageDate.getUTCFullYear() - 1970);

                    let status;
                    if (age < 18) {
                        status = KYCSTATUS.AGE_RESTRICTED;
                        await this.sendNotification(
                            userKyc,
                            "Profile Verification Succeeded",
                            "Your profile has been verified BUT since you are under 18 years old, you are not allowed to transfer funds.",
                        );
                        await this.createUserKycLog({ userId: userKyc.userId, data: "AGE_RESTRICTED " + JSON.stringify(kycResultDto) });
                    } else {
                        status = KYCSTATUS.VERIFIED;
                        await this.sendNotification(userKyc, "Profile Verified", "Your profile has been verified.");
                        await this.createUserKycLog({ userId: userKyc.userId, data: "SUCCESS" });
                    }

                    await Promise.all([
                        this.userKycEntity.update(
                            {
                                result: kycResultDto.verification_result.document.document,
                            },
                            { where: { referenceId: kycResultDto.reference } },
                        ),
                        this.userEntity.update(
                            {
                                name: kycResultDto.verification_data.document.name.first_name || "",
                                lastname: kycResultDto.verification_data.document.name.last_name || "",
                                kycStatus: status,
                                kycEnabled: status === KYCSTATUS.VERIFIED,
                                nationalId: '',
                                birthdate: kycResultDto.verification_data.document.dob || "",
                            },
                            { where: { email: userKyc.email } },
                        ),
                    ]).catch(async error => {
                        this.logger.error(`An error occurred while updating the user with kyc results`, error);
                        await this.createUserKycLog({ data: "An error occurred while updating the user with kyc results " + JSON.stringify(error) });
                        throw error;
                    });
                } else {
                    this.logger.error(`Kyc result has accepted but there are no personal details for reference id:${kycResultDto.reference}`);
                    await this.sendNotification(userKyc, "Profile Verification Failed", "There are no personal details for your profile.");
                    await this.userEntity.update({ kycStatus: KYCSTATUS.REJECTED }, { where: { email: userKyc.email } });
                    await this.createUserKycLog({ userId: userKyc.userId, data: "REJECTED " + JSON.stringify(kycResultDto) });
                }
            } else {
                this.logger.error(`Kyc result has been denied:${kycResultDto.reference}`);
                await this.sendNotification(userKyc, "Profile Verification Failed", "Your KYC Request has been declined.");
                await this.createUserKycLog({ userId: userKyc.userId, data: "DENIED " + JSON.stringify(kycResultDto) });
                await this.userEntity.update({ kycStatus: KYCSTATUS.REJECTED }, { where: { email: userKyc.email } });
            }

            return userKyc;
        } catch (error) {
            this.logger.error(`An error occurred while updating the user with kyc results`, error);
            await this.createUserKycLog({ data: "An error occurred while updating the user with kyc results " + JSON.stringify(error) });
            throw error;
        }
    }

    private async createUserKycLog(logDto: UserKycLogDto): Promise<void> {
        await this.userKycLogRepository.create({
            userId: logDto.userId,
            data: logDto.data,
            createdAt: new Date(),
            updatedAt: new Date(),
        });
    }

    private async sendNotification(userKyc: UserKycEntity, title: string, message: string): Promise<void> {
        const notifyBody: NotificationSendDto = {
            type: "GENERIC",
            channelTypes: [NotificationChannelType.EMAIL, NotificationChannelType.SOCKET],
            userIds: [userKyc.userId],
            data: { title, message },
            idempotencyId: userKyc.id,
        };
        try {
            await this.notificationService.create(notifyBody);
            this.logger.debug(`Activity ==> sent notification for request: ${JSON.stringify(notifyBody)}`);
        } catch (error) {
            this.logger.error(`Activity ==> error: ${util.inspect(error)}, while sending notification for request: ${JSON.stringify(notifyBody)}`);
            await this.createUserKycLog({ userId: userKyc.userId, data: "NOTIFICATION_ERROR " + JSON.stringify(error) });
            throw error;
        }
    }
}
