import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { UserKycInfo } from '../entities/user_kyc_info.entity';

@Injectable()
export class UserKycInfoRepository {
  constructor(
    @InjectModel(UserKycInfo)
    private userKycInfoModel: typeof UserKycInfo,
  ) {}

  async create(data: Partial<UserKycInfo>): Promise<UserKycInfo> {
    return this.userKycInfoModel.create(data);
  }

  async findAll(): Promise<UserKycInfo[]> {
    return this.userKycInfoModel.findAll();
  }

  async findOne(id: number): Promise<UserKycInfo | null> {
    return this.userKycInfoModel.findByPk(id);
  }

  async update(id: number, data: Partial<UserKycInfo>): Promise<[number, UserKycInfo[]]> {
    return this.userKycInfoModel.update(data, { where: { id }, returning: true });
  }

  async remove(id: number): Promise<number> {
    return this.userKycInfoModel.destroy({ where: { id } });
  }
}