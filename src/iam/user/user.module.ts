import { <PERSON><PERSON><PERSON> } from "@nestjs/common";
import { SequelizeModule } from "@nestjs/sequelize";
import { UserEntity } from "./entities/user.entity";
import { UserRepository } from "./repositories/user.repository";
import { UserService } from "./services/user.service";
import { UserController } from "./controllers/user.controller";
import { AwsModule } from "../aws/aws.module";
import { ServiceLevelMetricsSender } from "../../commons/o11y/service.metric";
 
import { UserRegistrationService } from "./services/user-registration.service";
import { UserKycEntity } from "./entities/user.kyc.entity";
import { UserKycRepository } from "./repositories/user.kyc.repository";
import { ShuftiProRestClient } from "../../commons/clients/shuftipro-rest-client";
 
import { UserKycInfo } from './entities/user_kyc_info.entity';
import { UserKycInfoRepository } from './repositories/user_kyc_info.repository';
import { UserKycInfoService } from './services/user_kyc_info.service';
import { NotificationService } from "../../commons/notifications/notification.service";
import { UserKycLogEntity } from './entities/user_kyc_log.entity';
import { UserKycLogRepository } from './repositories/user_kyc_log.repository';
import { UserKycLogService } from './services/user_kyc_log.service';
import { CustomAuthUtils } from "./utils/custom-auth-utils";
import { MobileController } from "./controllers/mobile-controller";
import { UserAdminController } from "./controllers/user-admin-controller";

@Module({
    imports: [
        SequelizeModule.forFeature([UserEntity, UserKycEntity, UserKycInfo, UserKycLogEntity]),
        AwsModule
    ],
    controllers: [UserController, UserAdminController, MobileController],
    providers: [
        UserRepository,
        UserKycRepository,
        UserKycInfoRepository,
        UserKycInfoService,
        UserService,
        CustomAuthUtils,
        ServiceLevelMetricsSender,
        UserRegistrationService,
        ShuftiProRestClient,
        UserKycInfoService,
        NotificationService,
        UserKycLogRepository,
        UserKycLogService
    ],
    exports: [
        UserRepository,
        UserKycRepository,
        UserKycInfoRepository,
        UserKycInfoService,
        UserService,
        ServiceLevelMetricsSender,
        CustomAuthUtils,
        UserRegistrationService,
        ShuftiProRestClient,
        NotificationService,
        UserKycLogRepository,
        UserKycLogService
    ]
})
export class UserModule { }
