import { Inject, Injectable, Logger, OnApplicationShutdown } from '@nestjs/common';
import { BaseService } from "../../commons/services/base.service";
import { Cluster, Redis } from 'ioredis';
import { ConfigService } from '@nestjs/config';
import { REDIS_CLIENT } from '../utils/redis.utils';

/**
 * Redis service class to interact with the Redis. It provides abstraction to the Redis client.
 */
@Injectable()
export class RedisService extends BaseService implements OnApplicationShutdown{

    private readonly logger = new Logger(RedisService.name);

    constructor(@Inject(REDIS_CLIENT) private readonly redis: Redis | Cluster,
        private readonly configService: ConfigService) {
        super(configService);
    }

    /**
     * 
     * @param key 
     * @returns 
     */
    async get(key: string): Promise<string | null> {
        this.logger.debug(`Getting value from Redis for key: ${key}`);
        const result:string | null = await this.redis.get(key);
        this.logger.debug(`Value from Redis for key: ${key} is: ${result}`);
        return result;
    }

    /**
     * 
     * @param key 
     * @param value 
     * @param expiry in milliseconds
     */
    async set(key: string, value: string, expiry?:number): Promise<void> {
        this.logger.debug(`Setting value: ${value}  in Redis for key: ${key}`);
        if(expiry != undefined && expiry > 0) {
            this.logger.debug(`Setting value: ${value} with expiry ${expiry} in Redis for key: ${key} if not exists`);
            await this.redis.set(key, value, 'PX', expiry);
        }
        else {
            this.logger.debug(`Setting value: ${value} in Redis for key: ${key}`);
            await this.redis.set(key, value);
        }
    }

    /**
     *  Set the value of a key, only if the key does not exist.
     *  Returns true if the key was set, false if it was not.
     * 
     * @param key 
     * @param value
     * @param expiry in milliseconds 
     * @returns boolean
     */
    async setnx(key: string, value: string, expiry?:number): Promise<boolean> {
        this.logger.debug(`Setting value: ${value} with expiry:${expiry} in Redis for key: ${key} if not exists`);
        const result = await this.redis.setnx(key, value);
        const rb:boolean = result == 1;
        if(rb && expiry != undefined && expiry > 0) {
            await this.redis.pexpire(key, expiry);
        }
        return rb;
    }

    /**
     * 
     * @param key 
     */
    async delete(key: string): Promise<void> {
        this.logger.debug(`deleting record from Redis for key: ${key}`);
        await this.redis.del(key);
        this.logger.debug(`record deleted from Redis for key: ${key}`);
    }

    onApplicationShutdown(signal?: string) {
        if(this.redis) {
            this.logger.debug('Closing Redis connection');
            this.redis.disconnect();
        }
    }
}