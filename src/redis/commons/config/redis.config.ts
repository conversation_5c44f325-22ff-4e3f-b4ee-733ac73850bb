import { RedisClusterNode } from "./redis-cluster-node";

/**
 * Redis configurations.
 * 
 * if clusterEnabled is true, the host should be a comma separated list of host:port pairs.
 * Cluster nodes will be resolved from the host string.
 */
export class RedisConfig {
    host: string;
    port: number;
    username: string;
    password: string;
    clusterEnabled: boolean;
    clusterNodes?: RedisClusterNode[];
    cacheKeyPrefix?: string;

    /**
     * If a command does not return a reply within a set number of milliseconds, a "Command timed out" error will be thrown.
     */
    commandTimeout: number;

    /**
     * How long the client will wait before killing a socket due to inactivity during initial connection.
     */
    connectTimeout: number;
    napMap?: Map<string, string>;

    constructor(host: string, port: number, username: string, password: string, clusterEnabled: boolean, commandTimeout: number, connectTimeout: number, 
        napMap?: Map<string, string>, cacheKeyPrefix?:string) {
        this.host = host;
        this.port = port;
        this.username = username;
        this.password = password;
        this.clusterEnabled = clusterEnabled;
        this.commandTimeout = commandTimeout;
        this.connectTimeout = connectTimeout;
        this.napMap = napMap;
        this.cacheKeyPrefix = cacheKeyPrefix;
        this.resolveClusterNodes();
    }

    /**
     * Resolve cluster nodes from the host string.
     */
    resolveClusterNodes(): void {
        if (this.clusterEnabled) {
            this.clusterNodes = [];
            this.host.split(",").forEach((value: string, index: number) => {
                let hostPort = value.split(":");
                this.clusterNodes?.push(new RedisClusterNode(hostPort[0], Number.parseInt(hostPort[1])));
            }
            );
        }
    }
}