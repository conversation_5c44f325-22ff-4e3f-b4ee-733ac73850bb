import { ConfigService } from "@nestjs/config";
import { RedisConfig } from "../commons/config/redis.config";
import { WP_ERROR_CONFIG_NOT_FOUND } from "../../commons/error/error.codes";
import { REDIS_COMMAND_TIME_OUT_DEFAULT_VALUE, REDIS_COMMAND_TIME_OUT_PARAM_NAME, REDIS_CONNECT_TIME_OUT_DEFAULT_VALUE, REDIS_CONNECT_TIME_OUT_PARAM_NAME, REDIS_HOST_PARAM_NAME, REDIS_NAT_MAP_DEFAULT_VALUE, REDIS_NAT_MAP_PARAM_NAME, REDIS_PORT_PARAM_NAME, REDIS_PASSWORD_DEFAULT_VALUE, REDIS_PASSWORD_PARAM_NAME, REDIS_USERNAME_DEFAULT_VALUE, REDIS_USERNAME_PARAM_NAME, REDIS_CLUSTER_ENABLED_PARAM_NAME, REDIS_CLUSTER_ENABLED_DEFAULT_VALUE, IDEMPOTENCY_CACHE_KEY_PREFIX_PARAM_NAME, IDEMPOTENCY_CACHE_KEY_PREFIX_DAFULT_VALUE } from "../../utils/confs";
import Redis, { Cluster, NatMap } from "ioredis";
import { WPError } from "../../commons/error/wp.error";

export const REDIS_CLIENT = 'REDIS_CLIENT';

/**
 * Utility class for Redis.
 */
export class RedisUtils {

    static async resolveRedisConfigs(configService: ConfigService): Promise<RedisConfig> {
        const host: string = await this.getConfig<string>(configService, REDIS_HOST_PARAM_NAME);
        const port: number = await this.getConfig<number>(configService, REDIS_PORT_PARAM_NAME);
        const username: string = await this.getConfig<string>(configService, REDIS_USERNAME_PARAM_NAME, REDIS_USERNAME_DEFAULT_VALUE, false);
        const password: string = await this.getConfig<string>(configService, REDIS_PASSWORD_PARAM_NAME, REDIS_PASSWORD_DEFAULT_VALUE, false);
        const commandTimeout: number = await this.getConfig<number>(configService, REDIS_COMMAND_TIME_OUT_PARAM_NAME, Number.parseInt(REDIS_COMMAND_TIME_OUT_DEFAULT_VALUE));
        const connectTimeout: number = await this.getConfig<number>(configService, REDIS_CONNECT_TIME_OUT_PARAM_NAME, Number.parseInt(REDIS_CONNECT_TIME_OUT_DEFAULT_VALUE));
        const clusterEnabled: string = await this.getConfig<string>(configService, REDIS_CLUSTER_ENABLED_PARAM_NAME, REDIS_CLUSTER_ENABLED_DEFAULT_VALUE);
        let clusterEnabledB: boolean = clusterEnabled.toLowerCase() === 'true';

        // Disable cluster mode in non-production environments
        const isProd = process.env.APP_ENV === 'prod' || process.env.APP_ENV === 'production';
        if (!isProd) {
            clusterEnabledB = false;
        }

        const cacheKeyPrefix: string = await this.getConfig<string>(configService, IDEMPOTENCY_CACHE_KEY_PREFIX_PARAM_NAME, IDEMPOTENCY_CACHE_KEY_PREFIX_DAFULT_VALUE);
        const natMapString: string = await this.getConfig<string>(configService, REDIS_NAT_MAP_PARAM_NAME, REDIS_NAT_MAP_DEFAULT_VALUE, false);
        const natMap: Map<string, string> | undefined = this.resolveNatMap(natMapString);

        const config: RedisConfig = new RedisConfig(host, port, username, password, clusterEnabledB , commandTimeout, connectTimeout, natMap, undefined);
        return config;
    }

    /**
     * 
     * @param natMapString 
     * @returns Map<string, string>
     */
    static resolveNatMap(natMapString: string): Map<string, string>  | undefined {
        if (natMapString) {
            const natMapreverse = new Map<string, string>();

            const mappings = natMapString.split(',');
            for (const mapping of mappings) {
                const [externalHost, externalPort, internalHost, internalPort] = mapping.split(':');
                const externalKey = `${externalHost}:${externalPort}`;
                const internalValue = `${internalHost}:${internalPort}`;
                natMapreverse.set(externalKey, internalValue);
            }

            return natMapreverse;
        }
        return undefined;
    }

    /**
     * why natMap? bec of container env -> https://stackoverflow.com/questions/75047758/ioredis-cannot-connect-to-redis-cluster-running-in-docker
     * A sample NatMap
     * let natMap = {
                "**********:6379": { host: '127.0.0.1', port: 6379 },
                "**********:6379": { host: '127.0.0.1', port: 6380 },
                "**********:6379": { host: '127.0.0.1', port: 6381 },
                "**********:6379": { host: '127.0.0.1', port: 6382 },
                "**********:6379": { host: '127.0.0.1', port: 6383 },
                "**********:6379": { host: '127.0.0.1', port: 6384 },
            }
     * @param natMapreverse 
     * @returns NatMap
     */
    static createIoredisNatMap(natMapreverse: Map<string, string>): NatMap  | undefined {
        if (natMapreverse && natMapreverse.size > 0) {
            const natMap: NatMap = {};

            for (const [externalKey, internalValue] of natMapreverse) {
                const [internalHost, internalPort] = internalValue.split(':');
                natMap[externalKey] = {
                    host: internalHost,
                    port: parseInt(internalPort, 10),
                };
            }

            return natMap;
        }
        return undefined;
    }


    /**
    * Get the configuration value from the environment variables.
    * @param param 
    * @param defaultValue 
    * @param mandatory  default is true
    * @returns T
    */
    static async getConfig<T>(configService: ConfigService, param: string, defaultValue?: T, mandatory: boolean = true): Promise<T> {
        let value: T;
        if (defaultValue != undefined) {
            value = configService.get<T>(param, defaultValue);
        }
        else {
            value = configService.get<T>(param, { infer: true }) as T;
            if (mandatory && (value == undefined || value == null || value == "")) {
                throw new WPError(WP_ERROR_CONFIG_NOT_FOUND, `parameter:${param}`, undefined, WP_ERROR_CONFIG_NOT_FOUND.headerStatusCode + "");
            }
        }
        return value;
    }

    static instantiateRedis(restConfig: RedisConfig): Redis {
        return new Redis({
            host: restConfig.host,
            port: restConfig.port,
            commandTimeout: +restConfig.commandTimeout,
            connectTimeout: +restConfig.connectTimeout,
            keyPrefix: restConfig.cacheKeyPrefix,
            stringNumbers: true,
        }
        );
    }

    static instantiateRedisCluster(restConfig: RedisConfig): Cluster {
        // why natMap? bec of container env -> https://stackoverflow.com/questions/75047758/ioredis-cannot-connect-to-redis-cluster-running-in-docker
        const natMap: NatMap | undefined = this.createIoredisNatMap(restConfig.napMap!);
        if(!restConfig.clusterNodes) {
            throw new WPError(WP_ERROR_CONFIG_NOT_FOUND, `cluster nodes`, undefined, WP_ERROR_CONFIG_NOT_FOUND.headerStatusCode + "");
        }
        return new Redis.Cluster(restConfig.clusterNodes,
            {
                redisOptions: {
                    commandTimeout: +restConfig.commandTimeout,
                    connectTimeout: +restConfig.connectTimeout,
                    stringNumbers: true,
                    tls: undefined,
                    keyPrefix: restConfig.cacheKeyPrefix,
                },
                natMap: natMap,
            });
    }
}
