import { ConfigService } from '@nestjs/config';
import { RedisConfig } from '../commons/config/redis.config';
import { RedisUtils } from './redis.utils';
import { REDIS_CLUSTER_ENABLED_PARAM_NAME, REDIS_COMMAND_TIME_OUT_PARAM_NAME, REDIS_CONNECT_TIME_OUT_PARAM_NAME, REDIS_HOST_PARAM_NAME, REDIS_NAT_MAP_PARAM_NAME, REDIS_PORT_PARAM_NAME, REDIS_PASSWORD_PARAM_NAME, REDIS_USERNAME_PARAM_NAME } from '../../utils/confs';
import { WPError } from '../../commons/error/wp.error';

/**
 * Mocked environment variable names
 * let natMap = {
        "127.0.0.1:6379": { host: '**********', port: 6379 },
        "127.0.0.1:6380": { host: '**********', port: 6379 },
        "127.0.0.1:6381": { host: '**********', port: 6379 },
        "127.0.0.1:6382": { host: '**********', port: 6379 },
        "127.0.0.1:6383": { host: '**********', port: 6379 },
        "127.0.0.1:6384": { host: '**********', port: 6379 },
    }

    let natMapreverse = {
        "**********:6379": { host: '127.0.0.1', port: 6379 },
        "**********:6379": { host: '127.0.0.1', port: 6380 },
        "**********:6379": { host: '127.0.0.1', port: 6381 },
        "**********:6379": { host: '127.0.0.1', port: 6382 },
        "**********:6379": { host: '127.0.0.1', port: 6383 },
        "**********:6379": { host: '127.0.0.1', port: 6384 },
    }

    // either provide master hosts or haproxy host
    let hosts = [
        { host: '127.0.0.1', port: 6379 },
        { host: '127.0.0.1', port: 6380 },
        { host: '127.0.0.1', port: 6381 }
    ]

    let haProxyHost = [
        { host: '127.0.0.1', port: 6378 }
    ]
 */
describe('RedisUtils', () => {
    let mockConfigService: ConfigService;

    beforeEach(() => {
        mockConfigService = {
            get: jest.fn(),
        } as unknown as ConfigService;
    });

    describe('resolveRedisConfigs', () => {
        it('should return RedisConfig with correct values', async () => {
            // Mock environment variable values
            (mockConfigService.get as jest.Mock)
                .mockImplementation((param: string, defaultValue?: any) => {
                    const mockValues: Map<string, any> = new Map<string, any>([
                        [`${REDIS_HOST_PARAM_NAME}`, 'localhost'],
                        [`${REDIS_PORT_PARAM_NAME}`, 6379],
                        [`${REDIS_CLUSTER_ENABLED_PARAM_NAME}`, false],
                        [`${REDIS_USERNAME_PARAM_NAME}`, 'user'],
                        [`${REDIS_PASSWORD_PARAM_NAME}`, 'pass'],
                        [`${REDIS_COMMAND_TIME_OUT_PARAM_NAME}`, 3000],
                        [`${REDIS_CONNECT_TIME_OUT_PARAM_NAME}`, 5000],
                        [`${REDIS_NAT_MAP_PARAM_NAME}`, '**********:6379:127.0.0.1:6379,**********:6379:127.0.0.1:6380'],
                    ]);
                    return mockValues.get(param) || defaultValue;
                });

            const redisConfig: RedisConfig = await RedisUtils.resolveRedisConfigs(mockConfigService);

            expect(redisConfig).toBeInstanceOf(RedisConfig);
            expect(redisConfig.host).toBe('localhost');
            expect(redisConfig.port).toBe(6379);
            expect(redisConfig.username).toBe('user');
            expect(redisConfig.password).toBe('pass');
            expect(redisConfig.commandTimeout).toBe(3000);
            expect(redisConfig.connectTimeout).toBe(5000);
            expect(redisConfig.napMap).toEqual(
                new Map([
                    ['**********:6379', '127.0.0.1:6379'],
                    ['**********:6379', '127.0.0.1:6380'],
                ])
            );
        });

        it('should return clustered RedisConfig with correct values', async () => {
            // Mock environment variable values
            (mockConfigService.get as jest.Mock)
                .mockImplementation((param: string, defaultValue?: any) => {
                    const mockValues: Map<string, any> = new Map<string, any>([
                        [`${REDIS_HOST_PARAM_NAME}`, '127.0.0.1:6379,127.0.0.1:6380'],
                        [`${REDIS_PORT_PARAM_NAME}`, 6379],
                        [`${REDIS_CLUSTER_ENABLED_PARAM_NAME}`, "true"],
                        [`${REDIS_USERNAME_PARAM_NAME}`, 'user'],
                        [`${REDIS_PASSWORD_PARAM_NAME}`, 'pass'],
                        [`${REDIS_COMMAND_TIME_OUT_PARAM_NAME}`, 3000],
                        [`${REDIS_CONNECT_TIME_OUT_PARAM_NAME}`, 5000],
                        [`${REDIS_NAT_MAP_PARAM_NAME}`, '**********:6379:127.0.0.1:6379,**********:6379:127.0.0.1:6380'],
                    ]);
                    return mockValues.get(param) || defaultValue;
                });

            const redisConfig: RedisConfig = await RedisUtils.resolveRedisConfigs(mockConfigService);

            expect(redisConfig).toBeInstanceOf(RedisConfig);
            expect(redisConfig.host).toBe('127.0.0.1:6379,127.0.0.1:6380');
            expect(redisConfig.port).toBe(6379);
            expect(redisConfig.clusterEnabled).toBe(true);
            expect(redisConfig.clusterNodes).toBeDefined();
            expect(redisConfig.clusterNodes?.length).toBe(2);
            if (redisConfig.clusterNodes) {
                expect(redisConfig.clusterNodes[0].host).toBe('127.0.0.1');
                expect(redisConfig.clusterNodes[0].port).toBe(6379);
                expect(redisConfig.clusterNodes[1].host).toBe('127.0.0.1');
                expect(redisConfig.clusterNodes[1].port).toBe(6380);
            }
            expect(redisConfig.username).toBe('user');
            expect(redisConfig.password).toBe('pass');
            expect(redisConfig.commandTimeout).toBe(3000);
            expect(redisConfig.connectTimeout).toBe(5000);
            expect(redisConfig.napMap).toEqual(
                new Map([
                    ['**********:6379', '127.0.0.1:6379'],
                    ['**********:6379', '127.0.0.1:6380'],
                ])
            );
        });

        it('should throw WPError if a mandatory config is missing', async () => {
            // Mock missing environment variable
            (mockConfigService.get as jest.Mock).mockImplementation((param: string) => {
                if (param ===`${REDIS_HOST_PARAM_NAME}`) {
                    return undefined; // Simulate missing mandatory param
                }
                return 'some-value';
            });

            await expect(RedisUtils.resolveRedisConfigs(mockConfigService)).rejects.toThrow(WPError);
        });
    });

    describe('resolveNatMap', () => {
        it('should return a valid Map<string, string> from natMapString', () => {
            const natMapString = '**********:6379:127.0.0.1:6379,**********:6379:127.0.0.1:6380';
            const natMap = RedisUtils.resolveNatMap(natMapString);

            expect(natMap).toEqual(
                new Map([
                    ['**********:6379', '127.0.0.1:6379'],
                    ['**********:6379', '127.0.0.1:6380'],
                ])
            );
        });

        it('should return undefined for an empty natMapString', () => {
            const natMap = RedisUtils.resolveNatMap('');
            expect(natMap).toBeUndefined();
        });
    });

    describe('createIoredisNatMap', () => {
        it('should convert a Map<string, string> into NatMap for ioredis', () => {
            const natMapreverse = new Map([
                ['**********:6379', '127.0.0.1:6379'],
                ['**********:6379', '127.0.0.1:6380'],
            ]);

            const ioredisNatMap = RedisUtils.createIoredisNatMap(natMapreverse);

            expect(ioredisNatMap).toEqual({
                '**********:6379': { host: '127.0.0.1', port: 6379 },
                '**********:6379': { host: '127.0.0.1', port: 6380 },
            });
        });

        it('should return undefined for an empty Map', () => {
            const ioredisNatMap = RedisUtils.createIoredisNatMap(new Map());
            expect(ioredisNatMap).toBeUndefined();
        });
    });
});
