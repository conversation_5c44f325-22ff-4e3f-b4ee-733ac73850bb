import { Provider } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import Redis, { NatMap } from 'ioredis';
import { REDIS_CLIENT, RedisUtils } from '../utils/redis.utils';
import { RedisConfig } from '../commons/config/redis.config';
import { WP_ERROR_INTERNAL_SERVER } from '../../commons/error/error.codes';
import { WPError } from '../../commons/error/wp.error';

export const redisProvider: Provider = {
    provide: REDIS_CLIENT,
    useFactory: async (configService: ConfigService) => {
        try {

            const restConfig: RedisConfig = await RedisUtils.resolveRedisConfigs(configService);

            if (restConfig.clusterEnabled) {
                return RedisUtils.instantiateRedisCluster(restConfig);
            }
            else {
                return RedisUtils.instantiateRedis(restConfig);
            }
        } catch (error) {
            throw new WPError(WP_ERROR_INTERNAL_SERVER, 'cloud not load redis module', error, WP_ERROR_INTERNAL_SERVER.headerStatusCode + "");
        }
    },
    inject: [ConfigService],
} as Provider;