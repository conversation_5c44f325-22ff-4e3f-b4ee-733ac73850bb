import { Module } from '@nestjs/common';

import { RedisService } from './services/redis.service';
import { ConfigModule } from '@nestjs/config';
import { redisProvider } from './providers/redis.provider';
import { REDIS_CLIENT } from './utils/redis.utils';

@Module({
    imports: [ConfigModule],
    providers: [
        redisProvider,
        RedisService
    ],
    exports: [REDIS_CLIENT,RedisService],
})
export class RedisModule { }
