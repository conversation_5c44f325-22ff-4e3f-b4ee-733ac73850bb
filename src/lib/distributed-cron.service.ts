import { Injectable, OnModuleInit, Logger } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import { APP_NAME } from '../app.module';
import { uniqueNamesGenerator, Config, adjectives, colors, animals } from 'unique-names-generator';
import cronParser from 'cron-parser';
import { RedisService } from '../redis/services/redis.service';



interface ScheduledFunction {
  func: () => Promise<void>;
  label: string;
  intervalSeconds?: number;
  cronExpression?: string;
  type: 'interval' | 'cron';
}

@Injectable()
export class DistributedCronService implements OnModuleInit {
  private readonly instanceName: string;
  private readonly lockKey: string;
  private readonly lockTTL = 60000; // 60 seconds
  private readonly logger = new Logger(DistributedCronService.name);
  private scheduledFunctions: ScheduledFunction[] = [];

  constructor(
    private readonly redisService: RedisService,
  ) {
     if(!this.instanceName || this.instanceName.length==0) {
      this.instanceName = this.generateInstanceName();
     }
    this.lockKey = APP_NAME;
  }

  async onModuleInit() {
    this.logger.debug(`Instance ${this.instanceName} starting cron job`);
  }

  /**
   * Acquires a lock using Redis setnx operation
   * @param key The lock key
   * @param value The value to set (usually instance name)
   * @param ttl Time to live in milliseconds
   * @returns true if lock was acquired, false otherwise
   */
  private async acquireLock(key: string, value: string, ttl: number): Promise<boolean> {
    return await this.redisService.setnx(key, value, ttl);
  }

  /**
   * Releases a lock by deleting the key if it contains the expected value
   * @param key The lock key
   * @param value The expected value (usually instance name)
   */
  private async releaseLock(key: string, value: string): Promise<void> {
    try {
      const currentValue = await this.redisService.get(key);
      if (currentValue === value) {
        await this.redisService.delete(key);
      }
    } catch (error) {
      this.logger.error(`Failed to release lock for key ${key}: ${error.message}`);
    }
  }

  /**
   * Gets the Redis key for storing the next execution time
   */
  private getNextExecutionKey(label: string): string {
    return `${this.lockKey}_${label}_nextExecution`.replace(/[^a-zA-Z0-9]/g, '');
  }

  /**
   * Clears the cached next execution time for a scheduled function
   * @param label The label of the scheduled function
   */
  async resetNextExecutionTime(label: string): Promise<void> {
    const redisKey = this.getNextExecutionKey(label);
    try {
      await this.redisService.delete(redisKey);
      this.logger.log(`Reset next execution time for ${label}`);
    } catch (error) {
      this.logger.error(`Failed to reset execution time for ${label}: ${error.message}`);
    }
  }

  @Cron(CronExpression.EVERY_SECOND)
  async handleCron() {
    const now = Date.now();
    for (const scheduledFunc of this.scheduledFunctions) {
      const redisKey = this.getNextExecutionKey(scheduledFunc.label);

      // Get the next execution time from Redis
      let nextExecutionTime = await this.redisService.get(redisKey);

      // this.logger.debug(`Instance ${this.instanceName} checking scheduled function ${scheduledFunc.label}. Next execution time: ${nextExecutionTime/1000}`);

      // If no next execution time is set, initialize it
      if (!nextExecutionTime) {
        nextExecutionTime = this.calculateNextExecutionTime(scheduledFunc, now).toString();
        await this.redisService.set(redisKey, nextExecutionTime, 86400 * 1000); // 24 hours TTL as safety (in milliseconds)
      }

      if (now >= parseInt(nextExecutionTime)) {
        const lockKey = `${this.lockKey}_${scheduledFunc.label}`.replace(/[^a-zA-Z0-9]/g, '');
        const lockAcquired = await this.acquireLock(lockKey, this.instanceName, this.lockTTL);

        if (lockAcquired) {
          // this.logger.debug(`Instance ${this.instanceName} acquired lock for scheduled function ${scheduledFunc.label}`);
          try {
            // Double-check the next execution time after acquiring the lock
            nextExecutionTime = await this.redisService.get(redisKey);
            if (!nextExecutionTime || now >= parseInt(nextExecutionTime)) {
              await scheduledFunc.func();

              // Calculate and update next execution time in Redis
              const newNextExecutionTime = this.calculateNextExecutionTime(scheduledFunc, now);
              
              // For interval-based functions, use twice the interval as TTL
              // For cron-based functions, use 24 hours as TTL (or until next execution + buffer)
              const ttl = scheduledFunc.type === 'interval' && scheduledFunc.intervalSeconds 
                ? 2 * scheduledFunc.intervalSeconds * 1000 // Convert to milliseconds
                : 86400 * 1000; // 24 hours default in milliseconds
              
              await this.redisService.set(redisKey, newNextExecutionTime.toString(), ttl);

              this.logger.debug(`${this.instanceName} : Executed ${scheduledFunc.label} at ${new Date().toISOString()}. Updated next execution time for ${scheduledFunc.label}: ${new Date(newNextExecutionTime).toISOString()}`);
            } else {
              //this should never happen
              this.logger.debug(`Skipping execution for ${scheduledFunc.label}: another instance has updated the time`);
            }
          } finally {
            await this.releaseLock(lockKey, this.instanceName);
            this.logger.debug(`Instance ${this.instanceName} released lock for scheduled function ${scheduledFunc.label}`);
          }
        }
      }
    }
  }

  /**
   * Calculates the next execution time based on the scheduling type
   */
  private calculateNextExecutionTime(scheduledFunc: ScheduledFunction, now: number): number {
    if (scheduledFunc.type === 'interval' && scheduledFunc.intervalSeconds) {
      // For interval-based functions, just add the interval to the current time
      return now + scheduledFunc.intervalSeconds * 1000;
    } else if (scheduledFunc.type === 'cron' && scheduledFunc.cronExpression) {
      // For cron-based functions, parse the expression and get the next occurrence
      try {
        const interval = cronParser.parse(scheduledFunc.cronExpression, {
          currentDate: new Date(now),
          tz: 'UTC',
        });
        
        const next = interval.next();
        // this.logger.debug(`Next execution time for ${scheduledFunc.label}: ${JSON.stringify(interval)} ${next.getTime()}`);
        return next.getTime();
      } catch (error) {
        this.logger.error(`Error parsing cron expression ${scheduledFunc.cronExpression}: ${error.message}`);
        // Fallback: run in 1 hour if parsing fails
        return now + 3600000;
      }
    }
    // Fallback: run in 1 hour
    return now + 3600000;
  }

  /**
   * Schedules a function to run at specific intervals (in seconds)
   * @param forceUpdate If true, will reset any existing cached execution time
   */
  scheduleExternalFunction(
    func: () => Promise<void>, 
    label: string, 
    intervalSeconds: number, 
    forceUpdate: boolean = false
  ): void {
    this.scheduledFunctions.push({
      func,
      label,
      intervalSeconds,
      type: 'interval'
    });

    this.logger.log(`Scheduled new function to run every ${intervalSeconds} seconds: ${label}`);
    
    if (forceUpdate) {
      this.resetNextExecutionTime(label)
        .catch(err => this.logger.error(`Failed to force update for ${label}: ${err.message}`));
    }
  }

  /**
   * Schedules a function to run according to a cron expression
   * @param func The function to execute
   * @param label A unique label for the scheduled function
   * @param cronExpression A cron expression (e.g., '0 0 * * *' for daily at midnight)
   * @param forceUpdate If true, will reset any existing cached execution time
   */
  scheduleCronFunction(
    func: () => Promise<void>, 
    label: string, 
    cronExpression: string, 
    forceUpdate: boolean = false
  ): void {
    try {
      // Validate the cron expression by parsing it
      cronParser.parse(cronExpression);
      
      this.scheduledFunctions.push({
        func,
        label,
        cronExpression,
        type: 'cron'
      });

      this.logger.log(`Scheduled new function with cron expression '${cronExpression}': ${label}`);
      
      if (forceUpdate) {
        this.resetNextExecutionTime(label)
          .catch(err => this.logger.error(`Failed to force update for ${label}: ${err.message}`));
      }
    } catch (error) {
      this.logger.error(`Invalid cron expression '${cronExpression}' for function ${label}: ${error.message}`);
      throw new Error(`Invalid cron expression: ${error.message}`);
    }
  }

  private async executeCronTask(): Promise<void> {
    await this.delay(5000);
  }

  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  private generateInstanceName(): string {
    const customConfig: Config = {
      dictionaries: [adjectives, colors, animals],
      separator: '',
      style: 'capital',
      length: 3,
    };

    return uniqueNamesGenerator(customConfig);
  }

  private generateRandomDelay(): number {
    return Math.floor(Math.random() * 5000); // Random delay between 0 and 5000 ms
  }
}
