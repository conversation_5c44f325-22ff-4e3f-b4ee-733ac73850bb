import newrelic from 'newrelic';
import { request, Dispatcher } from 'undici';
import * as util from 'util';
import {
    StatusCodes,
} from 'http-status-codes';
import { IncomingHttpHeaders } from 'undici/types/header';
import { WP_ERROR_INTERNAL_SERVER, WP_ERROR_SERVICE_UNAVAILABLE_ERROR, WPErrorCode } from '../commons/error/error.codes';
import { WPExceptionMessage } from '../commons/error/wp.exception.message';
import { WPError } from '../commons/error/wp.error';
export class UndinciClient {

    /**
     * Type guard to check if a response body is a WPExceptionMessage
     */
    private isWPExceptionMessage(obj: unknown): obj is WPExceptionMessage {
        return (
            typeof obj === 'object' &&
            obj !== null &&
            'statusCode' in obj &&
            'message' in obj &&
            'errorCode' in obj
            // &&
            // 'details' in obj &&
            // 'path' in obj
        );
    }

    private async makeRequest<ReqBody, ResBody>(
        method: 'GET' | 'POST' | 'PUT' | 'PATCH' | 'DELETE',
        url: string,
        body?: ReqBody,
        headers: Record<string, string> = {},
    ): Promise<HttpResponse<ReqBody, ResBody>> {
        let requestBody: string | undefined;

        /*const transaction = newrelic.getTransaction();
        const distributedTracingHeaders: Record<string, string> = {};
        if(transaction) {
            transaction.insertDistributedTraceHeaders(distributedTracingHeaders);
        }
        console.log(transaction,transaction);
        */

        headers['Content-Type'] =  'application/json';
        // Validate and serialize request body if needed
        if (body) {
            try {
                requestBody = JSON.stringify(body);
            } catch (error) {
                const wpError: WPError = new WPError(WP_ERROR_INTERNAL_SERVER, `Failed to serialize request body: ${error.message}`, undefined, WP_ERROR_INTERNAL_SERVER.headerStatusCode + "");
                return new HttpResponse<ReqBody, ResBody>(url, headers, body, undefined, undefined, StatusCodes.INTERNAL_SERVER_ERROR, wpError, false);

            }
        }

        try {
            const response = await request(`${url}`, {
                method,
                headers,
                body: requestBody,
            });

            const contentType = response.headers['content-type'] || '';
            const responseHeaders: Record<string, string> = Object.fromEntries(
                Object.entries(response.headers).map(([key, value]) => [key, Array.isArray(value) ? value.join(', ') : value || ''])
            );
            const rawBody = await response.body.text();
            let parsedBody: ResBody | WPExceptionMessage | string;

            // Check if the response is JSON
            if (contentType.includes('application/json')) {
                try {
                    parsedBody = JSON.parse(rawBody) as ResBody | WPExceptionMessage;
                } catch {
                    parsedBody = rawBody;
                    const wpError: WPError = new WPError(WP_ERROR_INTERNAL_SERVER, `Could not parse JSON response: ${rawBody}`,
                        undefined, WP_ERROR_INTERNAL_SERVER.headerStatusCode + "");
                    return new HttpResponse<ReqBody, ResBody>(url, headers, body, responseHeaders, parsedBody, StatusCodes.INTERNAL_SERVER_ERROR, wpError, false);
                }
            } else {
                // Handle non-JSON responses
                parsedBody = rawBody;
            }

            if (response.statusCode >= 200 && response.statusCode < 300) {
                return new HttpResponse<ReqBody, ResBody>(url, headers, body, responseHeaders, parsedBody, response.statusCode, undefined, false);
            } else {
                let wpError: WPError;
                // If it's a WPExceptionMessage, throw that specifically
                if (this.isWPExceptionMessage(parsedBody)) {
                    let wpExceptionMessage = parsedBody as WPExceptionMessage;
                    const wpErrorCode: WPErrorCode = {
                        code: wpExceptionMessage.errorCode,
                        description: wpExceptionMessage.message,
                        headerStatusCode: wpExceptionMessage.statusCode,
                    } as WPErrorCode;
                    wpError = new  WPError(wpErrorCode, wpExceptionMessage.details, wpExceptionMessage.stack, response.statusCode + "");
                }
                else {
                     // Otherwise, throw a generic error
                    wpError = new  WPError(WP_ERROR_INTERNAL_SERVER, typeof parsedBody === 'string' ? parsedBody as string : util.inspect(parsedBody) || 'HTTP request failed', response.statusCode + "");
                }
                return new HttpResponse<ReqBody, ResBody>(url, headers, body, responseHeaders, parsedBody, response.statusCode, wpError, true);

            }
        } catch (error) {
            let wpError: WPError;
            // Handle network-related errors (e.g., ECONNREFUSED, ENOTFOUND)
            if ((error as NodeJS.ErrnoException).code) {
                wpError = new WPError(WP_ERROR_SERVICE_UNAVAILABLE_ERROR, `Network error occurred: ${(error as NodeJS.ErrnoException).code}`, error, WP_ERROR_SERVICE_UNAVAILABLE_ERROR.headerStatusCode + "");
            }
            else {
                wpError = new WPError(WP_ERROR_SERVICE_UNAVAILABLE_ERROR, `Unexpected error occurred`, error, WP_ERROR_SERVICE_UNAVAILABLE_ERROR.headerStatusCode + "");
            }

            return new HttpResponse<ReqBody, ResBody>(url, headers, body, undefined, undefined, StatusCodes.INTERNAL_SERVER_ERROR, wpError, false);

        }
    }

    /**
     * 
     * @param url 
     * @param headers 
     * @returns 
     */
    async get<ResBody>(url: string, headers?: Record<string, string>): Promise<HttpResponse<never,ResBody>> {
        return await this.makeRequest<never,ResBody>('GET', url, undefined, headers);
    }

    /**
     * 
     * @param url 
     * @param body 
     * @param headers 
     * @returns 
     */
    async post<ReqBody, ResBody>(
        url: string,
        body: ReqBody,
        headers?: Record<string, string>,
    ): Promise<HttpResponse<ReqBody, ResBody>> {
        return await this.makeRequest<ReqBody, ResBody>('POST', url, body, headers);
    }

    async put<ReqBody, ResBody>(
        url: string,
        body: ReqBody,
        headers?: Record<string, string>,
    ): Promise<HttpResponse<ReqBody, ResBody>> {
        return await this.makeRequest<ReqBody, ResBody>('PUT', url, body, headers);
    }

    /**
     * 
     * @param url 
     * @param headers 
     * @returns  
     */
    async delete<ResBody>(url: string, headers?: Record<string, string>): Promise<HttpResponse<never,ResBody>> {
        return await this.makeRequest<never, ResBody>('DELETE', url, undefined, headers);
    }
}


export class HttpResponse<ReqBody, ResBody> {
    url: string;
    reqHeaders?: Record<string, string>;
    reqBody?: ReqBody;
    statusCode?: number;
    responseHeaders?: Record<string, string> = {};
    responseBody?: ResBody | WPExceptionMessage | string;
    error?: Error;
    wpError: WPError | undefined = undefined;
    isError:boolean = false;
    isServiceError: boolean = false; // determines if the response is a meaning full service error

    constructor(url: string, reqHeaders: Record<string, string>, reqBody?: ReqBody, responseHeaders?: Record<string, string>, responseBody?: ResBody | WPExceptionMessage | string, statusCode?: number, wpError?: WPError, isServiceError: boolean = false, error?: Error) {
        this.url = url;
        this.reqHeaders = reqHeaders;
        this.reqBody = reqBody;
        this.responseHeaders = responseHeaders;
        this.responseBody = responseBody;
        this.statusCode = statusCode;
        this.error = error;
        this.wpError = wpError;
        if(wpError) {
            this.isError = true;
        }
        this.isServiceError = isServiceError;
    }

}
