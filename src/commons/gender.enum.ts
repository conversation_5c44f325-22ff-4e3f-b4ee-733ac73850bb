import { WPError } from "./error/wp.error";
import { WP_ERROR_IAM_USER_VALIDATION_PARAMS } from "./error/error.codes";
import { UserValidator } from "../iam/user/utils/user.validator";

export enum GenderEnum {
    male = 1,
    female =2,
    other =3,
}

export const genderEnumsArray: (string | GenderEnum)[] = Object.values(GenderEnum).filter(v => isNaN(Number(v)));
export const genderEnumsVAlueArray: (string | GenderEnum)[] = Object.values(GenderEnum).filter(v => !isNaN(Number(v)));

export function getGenderEnum(value: string): GenderEnum {
    return GenderEnum[value.toLowerCase()];
}

export function getGenderEnumByKey(key: string, validate = true): GenderEnum {
    const gender: GenderEnum = validate ? UserValidator.validateGender(key, false) : getGenderEnum(key);
    return gender;
}

export function getGenderEnumByValue(value: number, validate = true): GenderEnum {
    switch (value) {
        case 1:
            return GenderEnum.male;
        case 2:
            return GenderEnum.female;
        case 3:
            return GenderEnum.other;
        default:
            if (validate) {
                throw new WPError(WP_ERROR_IAM_USER_VALIDATION_PARAMS, `Invalid gender value provided. The valid values: ${genderEnumsVAlueArray}`, undefined, "400");
            }
            return GenderEnum.other;
    }
}
