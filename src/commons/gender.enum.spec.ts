import { GenderEnum, getGenderEnum, getGenderEnumByValue } from "./gender.enum";

/**
 * There are two ways to get enum keys by values
 * 
 * export enum GenderEnum {
 *      m = 1,
 *      f,
 *      o,
 *   }
 * 
 * // This will log "m" to the console
 * 
 * Or
 * 
 */
const asseertObj = function (obj?: any) {
    expect(obj).not.toBeUndefined();
    expect(obj).not.toBeNull();
    expect(obj).not.toBeNaN();
};

describe('Gender Enum', () => {
    beforeEach(async () => { });

    describe('GenderEnum.getGenderEnum', () => {
        it('should return a male GenderEnum', () => {

            let genderEnum: GenderEnum = getGenderEnum("male");
            asseertObj(genderEnum);
        });

        it('should return a male GenderEnum - capital', () => {

            let genderEnum: GenderEnum = getGenderEnum("MALE");
            asseertObj(genderEnum);
        });

        it('should return a female GenderEnum', () => {

            let genderEnum: GenderEnum = getGenderEnum("female");
            asseertObj(genderEnum);
        });

        it('should return a female GenderEnum - capital', () => {

            let genderEnum: GenderEnum = getGenderEnum("FEMALE");
            asseertObj(genderEnum);
        });

        it('should return a other GenderEnum', () => {

            let genderEnum: GenderEnum = getGenderEnum("other");
            asseertObj(genderEnum);
        });

        it('should return a other GenderEnum - capital', () => {

            let genderEnum: GenderEnum = getGenderEnum("OTHER");
            asseertObj(genderEnum);
        });

        it('should return a male GenderEnum - negative', () => {

            let genderEnum: GenderEnum = getGenderEnum("X");
            expect(genderEnum).toBeUndefined();

        });
    });

    describe('GenderEnum.getGenderEnumByValue', () => {

        it('should return a male GenderEnum', () => {

            let genderEnum: GenderEnum = getGenderEnumByValue(1);
            asseertObj(genderEnum);
        });

        it('should return a female GenderEnum', () => {

            let genderEnum: GenderEnum = getGenderEnumByValue(2);
            asseertObj(genderEnum);
        });

        it('should return a other GenderEnum', () => {

            let genderEnum: GenderEnum = getGenderEnumByValue(3);
            asseertObj(genderEnum);
        });

        it('should return a male GenderEnum - negative', () => {

            try {
                let genderEnum: GenderEnum = getGenderEnumByValue(4);
                fail("invalid gender valie should fail")
            } catch (error) {

            }
        });
    });


});
