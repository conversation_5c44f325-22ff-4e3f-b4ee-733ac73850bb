import { Test, TestingModule } from '@nestjs/testing';
import { ExecutionContext } from '@nestjs/common';
import { RecaptchaGuard } from './recaptcha.guard';
import { RecaptchaService } from '../services/recaptcha.service';

describe('RecaptchaGuard', () => {
    let guard: RecaptchaGuard;
    let recaptchaService: jest.Mocked<RecaptchaService>;

    beforeEach(async () => {
        const module: TestingModule = await Test.createTestingModule({
            providers: [
                RecaptchaGuard,
                {
                    provide: RecaptchaService,
                    useValue: {
                        verifyRecaptcha: jest.fn(),
                        isValidTokenFormat: jest.fn(),
                    },
                },
            ],
        }).compile();

        guard = module.get<RecaptchaGuard>(RecaptchaGuard);
        recaptchaService = module.get(RecaptchaService);
    });

    afterEach(() => {
        jest.clearAllMocks();
    });

    const createMockExecutionContext = (body: any, headers: any = {}): ExecutionContext => {
        return {
            switchToHttp: () => ({
                getRequest: () => ({
                    body,
                    headers,
                    connection: { remoteAddress: '***********' },
                    socket: { remoteAddress: '***********' },
                }),
            }),
        } as ExecutionContext;
    };

    describe('canActivate', () => {
        it('should return false when reCAPTCHA token is missing', async () => {
            const context = createMockExecutionContext({});
            
            const result = await guard.canActivate(context);
            
            expect(result).toBe(false);
            expect(recaptchaService.isValidTokenFormat).not.toHaveBeenCalled();
            expect(recaptchaService.verifyRecaptcha).not.toHaveBeenCalled();
        });

        it('should return false when reCAPTCHA token format is invalid', async () => {
            const context = createMockExecutionContext({
                recaptchaToken: 'invalid-token',
            });
            
            recaptchaService.isValidTokenFormat.mockReturnValue(false);
            
            const result = await guard.canActivate(context);
            
            expect(result).toBe(false);
            expect(recaptchaService.isValidTokenFormat).toHaveBeenCalledWith('invalid-token');
            expect(recaptchaService.verifyRecaptcha).not.toHaveBeenCalled();
        });

        it('should return true when reCAPTCHA verification succeeds', async () => {
            const context = createMockExecutionContext({
                recaptchaToken: 'valid-token-123456789012345',
            });
            
            recaptchaService.isValidTokenFormat.mockReturnValue(true);
            recaptchaService.verifyRecaptcha.mockResolvedValue(true);
            
            const result = await guard.canActivate(context);
            
            expect(result).toBe(true);
            expect(recaptchaService.isValidTokenFormat).toHaveBeenCalledWith('valid-token-123456789012345');
            expect(recaptchaService.verifyRecaptcha).toHaveBeenCalledWith('valid-token-123456789012345', '***********');
        });

        it('should return false when reCAPTCHA verification fails', async () => {
            const context = createMockExecutionContext({
                recaptchaToken: 'valid-format-but-invalid-token',
            });
            
            recaptchaService.isValidTokenFormat.mockReturnValue(true);
            recaptchaService.verifyRecaptcha.mockResolvedValue(false);
            
            const result = await guard.canActivate(context);
            
            expect(result).toBe(false);
            expect(recaptchaService.verifyRecaptcha).toHaveBeenCalled();
        });

        it('should return false when reCAPTCHA verification throws an error', async () => {
            const context = createMockExecutionContext({
                recaptchaToken: 'valid-format-token-123456789',
            });
            
            recaptchaService.isValidTokenFormat.mockReturnValue(true);
            recaptchaService.verifyRecaptcha.mockRejectedValue(new Error('Verification failed'));
            
            const result = await guard.canActivate(context);
            
            expect(result).toBe(false);
            expect(recaptchaService.verifyRecaptcha).toHaveBeenCalled();
        });

        it('should extract client IP from x-forwarded-for header', async () => {
            const context = createMockExecutionContext(
                { recaptchaToken: 'valid-token-123456789012345' },
                { 'x-forwarded-for': '***********, ***********' }
            );
            
            recaptchaService.isValidTokenFormat.mockReturnValue(true);
            recaptchaService.verifyRecaptcha.mockResolvedValue(true);
            
            await guard.canActivate(context);
            
            expect(recaptchaService.verifyRecaptcha).toHaveBeenCalledWith('valid-token-123456789012345', '***********');
        });

        it('should extract client IP from x-real-ip header', async () => {
            const context = createMockExecutionContext(
                { recaptchaToken: 'valid-token-123456789012345' },
                { 'x-real-ip': '***********' }
            );
            
            recaptchaService.isValidTokenFormat.mockReturnValue(true);
            recaptchaService.verifyRecaptcha.mockResolvedValue(true);
            
            await guard.canActivate(context);
            
            expect(recaptchaService.verifyRecaptcha).toHaveBeenCalledWith('valid-token-123456789012345', '***********');
        });

        it('should extract client IP from cf-connecting-ip header (Cloudflare)', async () => {
            const context = createMockExecutionContext(
                { recaptchaToken: 'valid-token-123456789012345' },
                { 'cf-connecting-ip': '***********' }
            );
            
            recaptchaService.isValidTokenFormat.mockReturnValue(true);
            recaptchaService.verifyRecaptcha.mockResolvedValue(true);
            
            await guard.canActivate(context);
            
            expect(recaptchaService.verifyRecaptcha).toHaveBeenCalledWith('valid-token-123456789012345', '***********');
        });

        it('should handle missing IP gracefully', async () => {
            const contextWithoutIP = {
                switchToHttp: () => ({
                    getRequest: () => ({
                        body: { recaptchaToken: 'valid-token-123456789012345' },
                        headers: {},
                        connection: {},
                        socket: {},
                    }),
                }),
            } as ExecutionContext;
            
            recaptchaService.isValidTokenFormat.mockReturnValue(true);
            recaptchaService.verifyRecaptcha.mockResolvedValue(true);
            
            await guard.canActivate(contextWithoutIP);
            
            expect(recaptchaService.verifyRecaptcha).toHaveBeenCalledWith('valid-token-123456789012345', 'unknown');
        });
    });
}); 