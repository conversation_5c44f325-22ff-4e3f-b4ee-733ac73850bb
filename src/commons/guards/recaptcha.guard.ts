import { Injectable, CanActivate, ExecutionContext, Logger } from '@nestjs/common';
import { Request } from 'express';
import { RecaptchaService } from '../services/recaptcha.service';

@Injectable()
export class RecaptchaGuard implements CanActivate {
    private readonly logger = new Logger(RecaptchaGuard.name);

    constructor(private readonly recaptchaService: RecaptchaService) {}

    async canActivate(context: ExecutionContext): Promise<boolean> {
        const request = context.switchToHttp().getRequest<Request>();
        
        // Extract reCAPTCHA token from request body
        const recaptchaToken = request.body?.recaptchaToken;
        
        if (!recaptchaToken) {
            this.logger.warn('reCAPTCHA token missing in request body');
            return false;
        }

        // Validate token format first
        if (!this.recaptchaService.isValidTokenFormat(recaptchaToken)) {
            this.logger.warn('Invalid reCAPTCHA token format');
            return false;
        }

        try {
            // Get client IP for additional verification
            const clientIp = this.getClientIp(request);
            
            // Verify the reCAPTCHA token
            const isValid = await this.recaptchaService.verifyRecaptcha(recaptchaToken, clientIp);
            
            if (isValid) {
                this.logger.debug('reCAPTCHA verification successful');
                return true;
            } else {
                this.logger.warn('reCAPTCHA verification failed');
                return false;
            }
        } catch (error) {
            this.logger.error('Error during reCAPTCHA verification in guard:', error);
            return false;
        }
    }

    
    /**
     * Extracts the client IP address from the request
     * @param request Express request object
     * @returns Client IP address
     */
    private getClientIp(request: Request): string {
        // Check various headers for the real IP address
        const forwarded = request.headers['x-forwarded-for'];
        const realIp = request.headers['x-real-ip'];
        const cfConnectingIp = request.headers['cf-connecting-ip']; // Cloudflare
        
        if (forwarded) {
            // x-forwarded-for can contain multiple IPs, take the first one
            const ips = Array.isArray(forwarded) ? forwarded[0] : forwarded;
            return ips.split(',')[0].trim();
        }
        
        if (realIp) {
            return Array.isArray(realIp) ? realIp[0] : realIp;
        }
        
        if (cfConnectingIp) {
            return Array.isArray(cfConnectingIp) ? cfConnectingIp[0] : cfConnectingIp;
        }
        
        // Fallback to connection remote address
        return request.connection?.remoteAddress || 
               request.socket?.remoteAddress || 
               (request as any).ip || 
               'unknown';
    }
} 