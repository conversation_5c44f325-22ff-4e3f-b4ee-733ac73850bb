import { Pagination } from "./pagination";

export class Where {
    whereClause: any
    bindClause: any
}

export class QueryFilter {
    params: Map<string, QueryParams>;
    pagination: Pagination;
    associations?: Map<string, Association>;
}

export class QueryParams {
    filterParams: Map<string, QueryFilterParam>;
    sortParams: Map<string, QueryFilterSortParam>;
}

export class Association {
    name: string;
    load: boolean;
    associations?: Map<string, Association>;

    constructor(name: string, load: boolean, associations?: Map<string, Association>) {
        this.name = name
        this.load = load
        this.associations = associations
    }
}

export interface QueryFilterParam {
    name: string;
    entityName: string;
}

export abstract class QueryFilterParamBase implements QueryFilterParam {
    name: string;
    entityName: string;

    constructor(name: string, entityName: string) {
        this.name = name;
        this.entityName = entityName;
    }
}
export class QueryFilterSearchParam extends QueryFilterParamBase {
    value: any;

    constructor(name: string, value: any, entityName: string) {
        super(name, entityName)
        this.value = value;
    }
}

/**
 * Param valie can be a function to make bind variables work so that
 * `bindParamValue` is added to pass actual parameter value
 */
export class QueryFilterSearchBindParam extends QueryFilterParamBase {
    value: any;
    bindParam: string;
    bindParamValue: any;


    constructor(name: string, value: any, bindParam: string, bindParamValue: any, entityName: string) {
        super(name, entityName)
        this.value = value;
        this.bindParam = bindParam;
        this.bindParamValue = bindParamValue;
    }
}

export class QueryFilterRelationParam extends QueryFilterParamBase {
    value: boolean;

    constructor(name: string, value: boolean, entityName: string) {
        super(name, entityName)
        this.value = value;
    }
}

export class QueryFilterSortParam extends QueryFilterParamBase {
    sort: string;

    constructor(name: string, sort: string, entityName: string) {
        super(name, entityName)
        this.sort = sort;
    }
}

export class QueryFilterOperatorParam extends QueryFilterParamBase {
    value: any;
    operator: string;

    constructor(name: string, value: any, operator: string, entityName: string) {
        super(name, entityName)
        this.value = value;
        this.operator = operator;
    }
}

export class QueryFilterBetweenOperatorParam extends QueryFilterParamBase {
    start: any;
    end: any;

    constructor(name: string, start: any, end: any, entityName: string) {
        super(name, entityName)
        this.start = start;
        this.end = end;
    }
}

export class QueryFilterBetweenDatesParam extends QueryFilterParamBase {
    start: Date;
    end: Date;

    constructor(name: string, start: Date, end: Date, entityName: string) {
        super(name, entityName)
        this.start = start;
        this.end = end;
    }
}
