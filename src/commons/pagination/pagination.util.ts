import { Pagination } from "./pagination";
import { PaginationDto } from "./pagination-dto";
import { PaginationLinkDto } from "./pagination-link-dto";
import { PaginationMetaDto } from "./pagination-meta-dto";

export class PaginationUtil {
    public static calculatePagination(page: number, limit: number): Pagination {
        // pages always starts with 1
        if (!page || page <= 0) {
            page = 1;
        }

        // In sql:
        // The offset clause skips the rows before beginning to return the rows.
        // The LIMIT determines the number of rows (row_count) returned by the query .
        // Start index of the rows is determined by the offset

        // limit = perPage (15)
        // offset for:
        // page = 1 -> 15 * 1 = 15
        // page = 2 -> 15 * 2 = 30

        let offset: number = (page - 1) * limit;
        let pagination: Pagination = { page: page, limit: limit, offset: offset };

        return pagination;
    }

    public static createPaginationDto<T>(pagination: Pagination, total: number, requestPath: string, data: T[]): PaginationDto<T> {
        let lastPage = Math.ceil(total / pagination.limit);
        let currentPage = pagination.page;
        let prevPage: number;

        if (currentPage <= 1) {
            prevPage = 1;
        } else {
            prevPage = currentPage - 1;
        }

        let nextPage = currentPage + 1;

        if (nextPage >= lastPage) {
            nextPage = lastPage;
        }

        let links: PaginationLinkDto = {
            first: `${requestPath}?page=${1}`,
            last: `${requestPath}?page=${lastPage}`,
            prev: `${requestPath}?page=${prevPage}`,
            next: `${requestPath}?page=${nextPage}`,
        };
        let meta: PaginationMetaDto = {
            current_page: currentPage,
            from: pagination.offset + 1,
            last_page: lastPage,
            path: requestPath,
            per_page: pagination.limit,
            to: pagination.offset + pagination.limit,
            total: total,
        };
        let paginationDto: PaginationDto<T> = {
            data: data,
            links: links,
            meta: meta,
        };

        return paginationDto;
    }
}
