
import { 
    QueryFilterBetweenDatesParam, 
    QueryFilterBetweenOperatorParam, 
    QueryFilterOperatorParam, 
    QueryFilterParam, 
    QueryFilterRelationParam, 
    QueryFilterSearchBindParam, 
    QueryFilterSearchParam, 
    QueryFilterSortParam, 
    Where}  from "./query-filter";
import { Op } from "sequelize";
import * as util from "util";
import { WPError } from "../error/wp.error";
import { WP_ERROR_GENERIC_OPERATOR_TYPE_NOT_FOUND } from "../error/error.codes";

export class QueryFilterUtil {


    public static addBetweenCriteria(entityName: string, params: Map<string, QueryFilterParam>, queryAttrName: string, queryAttrFrom?: any, queryAttrTo?: any) {
        if (queryAttrFrom && queryAttrTo) {
            let searchParam: QueryFilterBetweenDatesParam = new QueryFilterBetweenDatesParam(queryAttrName, queryAttrFrom, queryAttrTo, entityName);
            params.set(searchParam.name, searchParam);
        } else if (queryAttrFrom && !queryAttrTo) {
            let searchParam: QueryFilterOperatorParam = new QueryFilterOperatorParam(queryAttrName, queryAttrFrom, ">=", entityName);
            params.set(searchParam.name, searchParam);
        } else if (!queryAttrFrom && queryAttrTo) {
            let searchParam: QueryFilterOperatorParam = new QueryFilterOperatorParam(queryAttrName, queryAttrTo, "<=", entityName);
            params.set(searchParam.name, searchParam);
        }
    }

    public static processOrderClause(params: Map<string, QueryFilterSortParam>, defaultAttrName?:string, dafaultSortOrder?:string,nestedEntities?:string[]):any[]|undefined {
        let orderClause: any = [];
        if (!params || params.size < 1) {
            if(defaultAttrName) {
                if(!dafaultSortOrder) {
                    dafaultSortOrder = 'DESC';
                }
                return nestedEntities? [...nestedEntities, defaultAttrName, dafaultSortOrder] : [defaultAttrName, dafaultSortOrder];
            }
            return undefined;
        }
        
        let orderIndex: number = 0;
        params.forEach((param: QueryFilterSortParam, key: string) => {
            orderClause[orderIndex] = nestedEntities? [...nestedEntities, param.name, param.sort] : [param.name, param.sort];
            orderIndex++;
        });
        return orderClause;
    }
    public static processWhereClause(params: Map<string, QueryFilterParam>, entityName?: string):Where  {
        if (!params || params.size < 1) {
            let where:Where = {whereClause:undefined,bindClause:undefined};
            return  where;
        }

        let whereClause: any = {};
        let bindClause:any = {};
    
        params.forEach((param: QueryFilterParam, key: string) => {
    
            if (param instanceof QueryFilterSearchParam) {
                whereClause[key] = param.value;
            } else if(param instanceof QueryFilterRelationParam) {
                whereClause[key] = param.value;
            }
            else if (param instanceof QueryFilterSearchBindParam) {

                whereClause[key] = param.value;
                bindClause[param.bindParam] = param.bindParamValue;

            } 
            else if (param instanceof QueryFilterBetweenOperatorParam) {
                whereClause[key] = {
                    [Op.between]: [param.start, param.end],
                };
            } else if (param instanceof QueryFilterBetweenDatesParam) {
                whereClause[key] = {
                    [Op.gt]: param.start,
                    [Op.lt]: param.end,
                };
            } else if (param instanceof QueryFilterOperatorParam) {
                if (param.operator == ">=") {
                    whereClause[key] = {
                        [Op.gte]: param.value,
                    };
                } else if (param.operator == "<=") {
                    whereClause[key] = {
                        [Op.lte]: param.value,
                    };
                } else if (param.operator == "<") {
                    whereClause[key] = {
                        [Op.lt]: param.value,
                    };
                } else if (param.operator == ">") {
                    whereClause[key] = {
                        [Op.gt]: param.value,
                    };
                } else {
                    throw new WPError(WP_ERROR_GENERIC_OPERATOR_TYPE_NOT_FOUND, `Unrecognized operator type ${param.operator}`, undefined, "403");
                }
            }
        });

        //this.logger.debug(`generated where clause ${util.inspect(whereClause)} and order clause ${util.inspect(orderClause)}`);
        let where:Where = {whereClause,bindClause};
        return where;

    }

}