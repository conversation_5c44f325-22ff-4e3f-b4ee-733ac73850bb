import { HttpStatus, Injectable, Logger } from "@nestjs/common";
import { HttpResponse, UndinciClient } from "../../lib/undici-client";
import { WPError } from "../error/wp.error";
import { WP_ERROR_BC_GENERAL, WP_ERROR_IAM_USER_PROFILE_KYC_ERROR } from "../error/error.codes";
import * as util from "util";
import { SHUFTIPRO_API_URL_PARAM_NAME, WALLET_SERVICE_API_URL_PARAM_NAME } from "../../utils/confs";
import { BaseService } from "../services/base.service";
import { ConfigService } from "@nestjs/config";


@Injectable()
export class ShuftiProRestClient extends BaseService {
    private readonly logger = new Logger(ShuftiProRestClient.name);

    constructor(private readonly configService: ConfigService) {
        super(configService);
    }


    async shuftiRequest(payload: any, token: any): Promise<any> {
        const apiUrl: string = await this.getConfig(SHUFTIPRO_API_URL_PARAM_NAME);
        this.logger.debug(`sending shufti request: ${util.inspect(payload)} via API:${apiUrl}`);

        const client = new UndinciClient();
        const response: HttpResponse<any, any> = await client.post<any, any>(
            apiUrl,
            payload,
            {
                "Content-Type": "application/json",
                "Accept": "application/json",
                "Authorization": "Basic " + token,
            },
        );

        if (response.error) {
            throw new WPError(
                WP_ERROR_IAM_USER_PROFILE_KYC_ERROR,
                `Could not execute ${util.inspect(response.reqBody)} request on : ${response.url} .Error:${response.error.message}`,
                response.error.stack,
            );
        } else if (
            response.statusCode &&
            response.statusCode != HttpStatus.ACCEPTED.valueOf() &&
            response.statusCode != HttpStatus.OK.valueOf() &&
            response.statusCode != HttpStatus.CREATED.valueOf() &&
            response.statusCode != HttpStatus.NO_CONTENT.valueOf()
        ) {
            throw new WPError(
                WP_ERROR_BC_GENERAL,
                `Could not execute ${util.inspect(response.reqBody)} request on : ${response.url}. Error:${response.statusCode},${response.responseBody}`,
            );
        }
        return response;
    } 

}