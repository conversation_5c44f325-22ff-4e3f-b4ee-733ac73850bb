import { WP_ERROR_API_AUDIT_LOG_VALIDATION_PARAMS } from "./error/error.codes";
import { WPError } from "./error/wp.error";

export enum HttpMethodEnum {
    GET = 0,
    POST = 1,
    PUT = 2,
    DELETE = 3,
    PATCH = 4,
    HEAD= 5,
    OPTIONS= 6,
    TRACE= 7,
}

export type HttpMethod = 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH' | 'HEAD' | 'OPTIONS' | 'TRACE';

// Function to map HTTP method string to enum value
export function mapHttpMethodToEnum(method: HttpMethod): HttpMethodEnum {
    switch (method) {
        case 'GET':
            return HttpMethodEnum.GET;
        case 'POST':
            return HttpMethodEnum.POST;
        case 'PUT':
            return HttpMethodEnum.PUT;
        case 'DELETE':
            return HttpMethodEnum.DELETE;
        case 'PATCH':
            return HttpMethodEnum.PATCH;
        case 'HEAD':
            return HttpMethodEnum.HEAD;
        case 'OPTIONS':
            return HttpMethodEnum.OPTIONS;
        case 'TRACE':
            return HttpMethodEnum.TRACE;
        default:
            throw new WPError(WP_ERROR_API_AUDIT_LOG_VALIDATION_PARAMS, "Invalid HTTP method: " + method, undefined, WP_ERROR_API_AUDIT_LOG_VALIDATION_PARAMS.headerStatusCode + "");
    }
}

// Function to map enum value back to HTTP method string
export function mapEnumToHttpMethod(enumValue: HttpMethodEnum): HttpMethod {
    switch (enumValue) {
        case HttpMethodEnum.GET:
            return 'GET';
        case HttpMethodEnum.POST:
            return 'POST';
        case HttpMethodEnum.PUT:
            return 'PUT';
        case HttpMethodEnum.DELETE:
            return 'DELETE';
        case HttpMethodEnum.PATCH:
            return 'PATCH';
        default:
            throw new WPError(WP_ERROR_API_AUDIT_LOG_VALIDATION_PARAMS, "Invalid HTTP method enum value: " + enumValue, undefined, WP_ERROR_API_AUDIT_LOG_VALIDATION_PARAMS.headerStatusCode + "");}
}
