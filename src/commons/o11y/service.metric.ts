// eslint-disable-next-line prettier/prettier
const newrelic = require('newrelic');
import { Injectable, Logger } from "@nestjs/common";
import { DistributedTraceHeaders } from "./dto/distributed.tracing.headers";

export enum SpanName {

    INITIALIZE_SESSION = "InitializeSession",

    WITHDRAW_TRANSACTION = "WithdrawTransaction",

    SERVICE_NAME = "EAZY-WALLET",

    /**
     * Workflow is scheduled by a client
     */
    WORKFLOW_START = 'StartWorkflow',
  
    /**
     * Workflow is client calls signalWithStart
     */
    WORKFLOW_SIGNAL_WITH_START = 'SignalWithStartWorkflow',
  
    /**
     * Workflow run is executing
     */
    WORKFLOW_EXECUTE = 'RunWorkflow',
    /**
     * Child Workflow is started (by parent Workflow)
     */
    CHILD_WORKFLOW_START = 'StartChildWorkflow',
    /**
     * Activity is scheduled by a Workflow
     */
    ACTIVITY_START = 'StartActivity',
    /**
     * Activity is executing
     */
    ACTIVITY_EXECUTE = 'RunActivity',
    /**
     * Workflow is continuing as new
     */
    CONTINUE_AS_NEW = 'ContinueAsNew',
  }
  
export const SPAN_DELIMITER = ':';

export interface InstrumentOptions<T> {
    spanName: string;
    fn: () => Promise<T>;
    context?: any;
    acceptableErrors?: (err: unknown) => boolean;
}

/** Default trace header for opentelemetry interceptors */
export const TRACE_HEADER = 'newrelicdata'
export const RUN_ID_ATTR_KEY = 'run_id';

/**
 * https://docs.newrelic.com/docs/data-apis/custom-data/custom-events/data-requirements-limits-custom-event-data/
 * https://docs.newrelic.com/docs/apm/agents/nodejs-agent/api-guides/nodejs-agent-api/#custom-metric-api
 * https://docs.newrelic.com/docs/apm/agents/nodejs-agent/api-guides/nodejs-agent-api/#custom-events-api
 *
 * https://docs.newrelic.com/docs/query-your-data/explore-query-data/browse-data/introduction-data-explorer/
 *
 */
@Injectable()
export class ServiceLevelMetricsSender {
    private readonly logger = new Logger(ServiceLevelMetricsSender.name);

    /**
     * https://docs.newrelic.com/docs/apm/agents/nodejs-agent/api-guides/nodejs-agent-api/#custom-metric-api
     * @param name
     * @param amount
     */
    incrementMetric(name: string, amount: number): void {
        newrelic.incrementMetric(name, amount);
    }

    /**
     * https://docs.newrelic.com/docs/apm/agents/nodejs-agent/api-guides/nodejs-agent-api/#custom-metric-api
     * @param name
     */
    incrementMetricBy1(name: string): void {
        if (!name) {
            this.logger.debug("Check name. The metric could not be incremented.");
            return;
        }
        //add 1 to counter
        newrelic.incrementMetric(name);
    }

    /**
     * https://docs.newrelic.com/docs/apm/agents/nodejs-agent/api-guides/nodejs-agent-api/#custom-metric-api

     * Use recordMetric to record an event-based metric, usually associated with a particular duration. 
     * The name must be a string following standard metric naming rules. 
     * The value will usually be a number, but it can also be an object.
     * @param name 
     * @param value 
     * 
     * When value is a numeric value, it should represent the magnitude of a measurement associated with an event; 
     * for example, the duration for a particular method call.
       When value is an object, it must contain count, total, min, max, and sumOfSquares keys, all with number values. 
       This form is useful to aggregate metrics on your own and report them periodically; for example, from a setInterval. 
       These values will be aggregated with any previously collected values for the same metric. 
       The names of these keys match the names of the keys used by the platform API.
     */
    recordMetric(name: string, value: number): void {
        if (!name || !value) {
            this.logger.debug("Check name / value. The metric could not be created.");
            return;
        }
        newrelic.recordMetric(name, value);
    }

    /**
     * 
        const attributes = {
            attribute1: 'value1',
            attribute2: 2
        };

        newrelic.recordCustomEvent('MessagingEvent', attributes);
     
     * https://docs.newrelic.com/docs/apm/agents/nodejs-agent/api-guides/nodejs-agent-api/#custom-events-api
     * @param eventType 
     * @param attributes 
     */
    recordCustomEvent(eventType: string, attributes: any): void {
        if (!eventType || !attributes) {
            this.logger.debug("Check eventType / attributes. The event could not be recoreded.");
            return;
        }
        newrelic.recordCustomEvent(eventType, attributes);
    }

    /**
     * https://docs.newrelic.com/docs/apm/agents/nodejs-agent/api-guides/nodejs-agent-api/#setUserID
     * @param id
     */
    setUserId(id: string): void {
        if (!id) {
            this.logger.debug("User id is empty. UserId could not be added to transaction trace.");
            return;
        }
        newrelic.setUserID(id);
    }

    /**
     * https://docs.newrelic.com/docs/apm/agents/nodejs-agent/api-guides/nodejs-agent-api/#add-custom-attribute
     * @param name
     * @param value
     */
    addCustomAttributeToTransaction(name: string, value: any) {
        if (!value || !name) {
            this.logger.debug("name/value is empty. The attribute could not be added to transaction trace.");
            return;
        }
        newrelic.addCustomAttribute(name, value);
    }
    
}

const loggerFn = new Logger(ServiceLevelMetricsSender.name);

/**
 * Given headers, return new headers with the current otel context inserted
 */
export async function headersWithContext(headers: Headers): Promise<Headers> {
    const carrier = getDistributedTraceHeaders(SpanName.INITIALIZE_SESSION);
    //otel.propagation.inject(otel.context.active(), carrier, otel.defaultTextMapSetter);
    //return { ...headers, [TRACE_HEADER]: payloadConverter.toPayload(carrier) };
    return null!;
}

/**
 * If found, return an otel Context deserialized from the provided headers
 * any: new relic header object
 */
export async function  extractContextFromHeaders(headers: Headers): Promise<any | undefined> {
    const encodedSpanContext = headers[TRACE_HEADER];
    if (encodedSpanContext === undefined) {
        return undefined;
    }
    //const textMap: Record<string, string> = payloadConverter.fromPayload(encodedSpanContext);
    //return otel.propagation.extract(otel.context.active(), textMap, otel.defaultTextMapGetter);
    //return textMap;
    return null;
}

export async function getDistributedTraceHeaders(spanName: SpanName | string): Promise<DistributedTraceHeaders> {
    // Call newrelic.getTransaction to retrieve a handle on the current transaction.    
    newrelic.getTransaction();
    //manuel
    ////https://docs.newrelic.com/docs/distributed-tracing/concepts/how-new-relic-distributed-tracing-works/#head-based
    // This could be a header object from an incoming request as well
    let headers: DistributedTraceHeaders = {
        traceparent: "",
        tracestate: "",
        newrelic: ""
    };
    await newrelic.startWebTransaction(spanName, async function executeTransaction() {
        const transaction = newrelic.getTransaction();
        // generate the headers
        transaction.insertDistributedTraceHeaders(headers);        
    });
    loggerFn.debug(`Distributed transaction info :${JSON.stringify(headers)}`);

    return headers;
}

export async function  instrument<T>({spanName, fn, context}: InstrumentOptions<T>): Promise<T> {
    return await wrapWithSpan(spanName, fn, context);
}

async function wrapWithSpan<T>(spanName: string, fn: () => Promise<T>, context): Promise<T> {
    let result = null;

    //https://github.com/newrelic/node-newrelic/blob/main/examples/api/distributed-tracing/example1-background.js
    //https://docs.newrelic.com/docs/apm/agents/nodejs-agent/extend-your-instrumentation/nodejs-custom-instrumentation/
    // Call newrelic.getTransaction to retrieve a handle on the current transaction.
    const transactionHandle = newrelic.getTransaction();
    //node_modules/newrelic/api.js
    result = await newrelic.startWebTransaction(spanName, async function () {
        //node_modules/newrelic/lib/transaction/index.js                       
        const transaction = newrelic.getTransaction();
        transaction.acceptDistributedTraceHeaders('Other', context);   
        
        //https://docs.newrelic.com/docs/apm/agents/nodejs-agent/api-guides/nodejs-agent-api/#transaction-handle-acceptDistributedTraceHeaders
        //https://docs.newrelic.com/docs/apm/agents/nodejs-agent/extend-your-instrumentation/nodejs-custom-instrumentation/#example-webtx-socket-io

        const result = await fn();   
        transaction.end();//should be in finally?     
        return result;
    }).catch(function reject(error) {
        newrelic.noticeError(error)
        throw error;
    })
    
    return result as any;
}