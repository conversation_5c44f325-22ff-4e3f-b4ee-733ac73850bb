import { Injectable, NestInterceptor, ExecutionContext, CallHandler } from "@nestjs/common";
import { Observable } from "rxjs";
const newrelic = require('newrelic');

import { ServiceLevelMetricsSender } from "./service.metric";
import { O11yHeaderParameters, O11yTransactonAttributes } from "./observability.constants";
import { O11yUtils } from "./o11y-utils";

@Injectable()
export class ObservabilityInterceptor implements NestInterceptor {
    constructor(private readonly metricSender: ServiceLevelMetricsSender) {}

    intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
        const request = context.switchToHttp().getRequest();
        const userId = request.headers[O11yHeaderParameters.USER_ID];
        const userRole = request.headers[O11yHeaderParameters.USER_ROLE];
        const version = process.env.npm_package_version;

        this.metricSender.setUserId(userId);
        this.metricSender.addCustomAttributeToTransaction(O11yTransactonAttributes.USER_ROLE, userRole);
        this.metricSender.addCustomAttributeToTransaction(O11yTransactonAttributes.SERVICE_VERSION, version);

        if(O11yUtils.ignoreHealthCheckMetrics(request.originalUrl)){
            const transactionHandle = newrelic.getTransaction();
            transactionHandle.ignore();
        }
        return next.handle();
    }
}
