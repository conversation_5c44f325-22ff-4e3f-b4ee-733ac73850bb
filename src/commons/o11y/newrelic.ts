require("dotenv").config();

const config = {
  app_name: process.env.NEW_RELIC_APP_NAME,
  license_key: process.env.NEW_RELIC_LICENSE_KEY,
  logging: {
    level: process.env.NEW_RELIC_LOG_LEVEL,
  },
  labels: process.env.NEW_RELIC_LABELS,
  allow_all_headers: true,
  code_level_metrics_enable: JSON.parse(process.env.NEW_RELIC_CODE_LEVEL_METRICS_ENABLED!),
  agent_enabled: JSON.parse(process.env.NEW_RELIC_AGENT_ENABLED!),
  /**
   * Enable Distributed Tracing
   */
  distributed_tracing: {
    enabled: process.env.NEW_RELIC_DISTRIBUTED_TRACING_ENABLED == "true" || process.env.NEW_RELIC_DISTRIBUTED_TRACING_ENABLED == "TRUE",
  },
  attributes: {
    exclude: [
      "request.headers.cookie",
      "request.headers.authorization",
      "request.headers.proxyAuthorization",
      "request.headers.setCookie*",
      "request.headers.x*",
      "response.headers.cookie",
      "response.headers.authorization",
      "response.headers.proxyAuthorization",
      "response.headers.setCookie*",
      "response.headers.x*",
    ],
  },
};

export { config };
