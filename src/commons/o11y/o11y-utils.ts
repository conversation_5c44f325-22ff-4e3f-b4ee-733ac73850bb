export class O11yUtils {
    static ignorePaths: string[] = ["actuator/health", "docs"];

    /**
     * This method is used to ignore processing for observability for certain paths
     *
     * Nestjs request flow:
     *
     * Incoming Request -> Middleware -> Guards -> Interceptors (pre-route handler) -> Pipes
     *  -> <PERSON> Handler (Controller) -> Interceptors (post-route handler) -> Exception Filters -> Outgoing Response
     * @param originalUrl
     * @returns boolean
     * @param ignorePaths
     * @returns boolean
     */
    static ignoreURL(originalUrl: string, ignorePaths: string[]): boolean {
        if(!originalUrl || !ignorePaths || ignorePaths.length === 0){
            return false;
        }
        let ignore: boolean = false;
        for (let i = 0; i < ignorePaths.length; i++) {
            const ignorePath: string = ignorePaths[i];
            if (originalUrl.includes(ignorePath)) {
                ignore = true;
                break;
            }
        }
        return ignore;
    }

    static ignoreHealthCheckLogs(originalUrl: string): boolean {
        return this.ignoreURL(originalUrl, this.ignorePaths);
    }
    static ignoreHealthCheckMetrics(originalUrl: string): boolean {
        return this.ignoreURL(originalUrl, this.ignorePaths);
    }
}