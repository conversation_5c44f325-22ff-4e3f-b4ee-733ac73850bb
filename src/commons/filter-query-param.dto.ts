import { IsOptional, IsString, IsDateString, IsInt, Min } from 'class-validator';
import { Type } from "class-transformer";
import { ApiProperty } from '@nestjs/swagger';

export class FilterQueryParams {

    @IsOptional()
    @IsString()
    @ApiProperty({ required: false, description: 'The user id of the record to retrive.' })
    userId?: string;

    @IsOptional()
    @Type(() => Number)  // Ensure the value is cast to a number
    @IsInt()
    @Min(1)
    @ApiProperty({ required: false, description: 'The number of items to retrieve.' })
    limit: number;

    @IsOptional()
    @Type(() => Number)  // Ensure the value is cast to a number
    @IsInt()
    @Min(1)
    @ApiProperty({ required: false, description: 'The page number to retrieve.' })
    page: number

    @IsOptional()
    @ApiProperty({ required: false, description: 'The field to sort by.' })
    sortBy?: string;

    @IsOptional()
    @ApiProperty({ required: false, description: 'The sort order.' })
    sortOrder?: string;
}
