import { ValidationArguments, ValidatorConstraint, ValidatorConstraintInterface } from "class-validator";

@ValidatorConstraint({ name: 'isPositiveNumberString', async: false })
export class IsPositiveNumberStringConstraint implements ValidatorConstraintInterface {
  validate(value: string, args: ValidationArguments) {
    const number = parseFloat(value);
    return !isNaN(number) && number > 0;
  }

  defaultMessage(args: ValidationArguments) {
    return 'Value must be a positive number';
  }
}