export type Jsonable = string | number | boolean | null | undefined | readonly Jsonable[] | { readonly [key: string]: Jsonable } | { toJSON(): Jsonable }

export interface WPErrorCode {
    code: string;
    description: string;
    headerStatusCode: number;
}

export const WP_ERRORCODE_PREFIX = "WP_";

export const WP_ERROR_SERVICE_UNAVAILABLE_ERROR: WPErrorCode = {
    code: WP_ERRORCODE_PREFIX + "2",
    description: "Service unavailable.",
    headerStatusCode: 503,
};

export const WP_UNAUTHORIZED: WPErrorCode = {
    code: WP_ERRORCODE_PREFIX + "401",
    description: "Unauthorized.",
    headerStatusCode: 401,
};

export const WP_ERROR_CONFIG_NOT_FOUND: WPErrorCode = {
    code: WP_ERRORCODE_PREFIX + "99",
    description: "Service onfiguration parameter not found.",
    headerStatusCode: 404,
};

export const WP_FORBIDDEN: WPErrorCode = {
    code: WP_ERRORCODE_PREFIX + "403",
    description: "Forbidden.",
    headerStatusCode: 401,
};

export const WP_ERROR_VALIDATION_PARAMS: WPErrorCode = {
    code: WP_ERRORCODE_PREFIX + "119",
    description: "Validation Error: Invalid input params",
    headerStatusCode: 400,
};

export const WP_ERROR_INTERNAL_SERVER: WPErrorCode = {
    code: WP_ERRORCODE_PREFIX + "1",
    description: "Internal server error. Something has gone wrong.",
    headerStatusCode: 500,
};

export const WP_ERROR_INPUT_VALIDATION: WPErrorCode = {
    code: WP_ERRORCODE_PREFIX + "50",
    description: "Input validation error.",
    headerStatusCode: 400,
};

export const WP_IS_KYC_COMPLETED_NOT_FOUND_NFT: WPErrorCode = {
    code: WP_ERRORCODE_PREFIX + "105",
    description: "Kyc has not completed. Kyc is required for nft transfer.",
    headerStatusCode: 400,
};

export const WP_IS_KYC_COMPLETED_NOT_FOUND: WPErrorCode = {
    code: WP_ERRORCODE_PREFIX + "106",
    description: "Kyc has not completed. Kyc is required for withdraw.",
    headerStatusCode: 400,
};

export const WP_USER_ID_NOT_FOUND: WPErrorCode = {
    code: WP_ERRORCODE_PREFIX + "108",
    description: "User id not found.",
    headerStatusCode: 400,
};

// iddempotency

export const WP_ERROR_IDEMPOTENCY_KEY_NOT_PROVIDED: WPErrorCode = {
    code: WP_ERRORCODE_PREFIX + "18",
    description: "Idempotency key is not provied in the request.",
    headerStatusCode: 400,
};

export const WP_ERROR_IDEMPOTENCY_KEY_VALIDATION: WPErrorCode = {
    code: WP_ERRORCODE_PREFIX + "19",
    description: "Idempotency key params validation error.",
    headerStatusCode: 400,
};

export const WP_ERROR_IDEMPOTENCY_GENERAL: WPErrorCode = {
    code: WP_ERRORCODE_PREFIX + "20",
    description: "Something has gone wrong while handling idempotency key. Try aaain later.",
    headerStatusCode: 500,
};

export const WP_ERROR_IDEMPOTENCY_CONCURRENT: WPErrorCode = {
    code: WP_ERRORCODE_PREFIX + "21",
    description: "A request with the same idempotency key is being processed now. Try with a different idempotency key.",
    headerStatusCode: 500,
};

export const WP_ERROR_IDEMPOTENCY_INCOMPLETE: WPErrorCode = {
    code: WP_ERRORCODE_PREFIX + "22",
    description: "Idempotency reqcord found but the result is not yet available. Eeither the request is being processed or the result is not available.",
    headerStatusCode: 500,
};

export const WP_ERROR_IDEMPOTENT_API_RECORD_NOT_CREATED: WPErrorCode = {
    code: WP_ERRORCODE_PREFIX + "23",
    description: "An internal error occurred while creating idempotent api record",
    headerStatusCode: 500,
};

export const WP_ERROR_IDEMPOTENT_API_RECORD_NOT_UPDATED: WPErrorCode = {
    code: WP_ERRORCODE_PREFIX + "24",
    description: "An internal error occurred while updating idempotent api record",
    headerStatusCode: 500,
};

export const WP_ERROR_IDEMPOTENT_API_RECORD_NOT_DELETED: WPErrorCode = {
    code: WP_ERRORCODE_PREFIX + "25",
    description: "An internal error occurred while deleting idempotent api record",
    headerStatusCode: 500,
};

export const WP_ERROR_IDEMPOTENT_API_RECORD_NOT_PURGED: WPErrorCode = {
    code: WP_ERRORCODE_PREFIX + "26",
    description: "An internal error occurred while purging idempotent api record",
    headerStatusCode: 500,
};

export const WP_ERROR_IDEMPOTENT_API_RECORD_NOT_FOUND_BY_ID: WPErrorCode = {
    code: WP_ERRORCODE_PREFIX + "27",
    description: "Idempotent api record could not be found by identifier",
    headerStatusCode: 404,
};

export const WP_ERROR_IDEMPOTENT_API_RECORD_NOT_FOUND: WPErrorCode = {
    code: WP_ERRORCODE_PREFIX + "28",
    description: "An internal error occurred while finding idempotent api record by identifier",
    headerStatusCode: 500,
};

export const WP_ERROR_IDEMPOTENT_API_RECORDS_NOT_FOUND: WPErrorCode = {
    code: WP_ERRORCODE_PREFIX + "29",
    description: "An internal error occurred while finding idempotent api records",
    headerStatusCode: 500,
};

export const WP_ERROR_IDEMPOTENT_API_RECORD_VALIDATION_PARAMS: WPErrorCode = {
    code: WP_ERRORCODE_PREFIX + "30",
    description: "Validation Error: Invalid input params",
    headerStatusCode: 400,
};

// 
// audit logs

export const WP_ERROR_API_AUDIT_LOG_NOT_CREATED: WPErrorCode = {
    code: WP_ERRORCODE_PREFIX + "3100",
    description: "An internal error occurred while creating api audit log",
    headerStatusCode: 500,
};

export const WP_ERROR_API_AUDIT_LOG_NOT_UPDATED: WPErrorCode = {
    code: WP_ERRORCODE_PREFIX + "3101",
    description: "An internal error occurred while updating api audit log",
    headerStatusCode: 500,
};

export const WP_ERROR_API_AUDIT_LOG_NOT_DELETED: WPErrorCode = {
    code: WP_ERRORCODE_PREFIX + "3102",
    description: "An internal error occurred while deleting api audit log",
    headerStatusCode: 500,
};

export const WP_ERROR_API_AUDIT_LOG_NOT_PURGED: WPErrorCode = {
    code: WP_ERRORCODE_PREFIX + "3103",
    description: "An internal error occurred while purging api audit log",
    headerStatusCode: 500,
};

export const WP_ERROR_API_AUDIT_LOG_NOT_FOUND_BY_ID: WPErrorCode = {
    code: WP_ERRORCODE_PREFIX + "3104",
    description: "Api audit log could not be found by identifier",
    headerStatusCode: 404,
};

export const WP_ERROR_API_AUDIT_LOG_NOT_FOUND: WPErrorCode = {
    code: WP_ERRORCODE_PREFIX + "3105",
    description: "An internal error occurred while finding api audit log by identifier",
    headerStatusCode: 500,
};

export const WP_ERROR_API_AUDIT_LOGS_NOT_FOUND: WPErrorCode = {
    code: WP_ERRORCODE_PREFIX + "3106",
    description: "An internal error occurred while finding api audit logs",
    headerStatusCode: 500,
};

export const WP_ERROR_API_AUDIT_LOG_VALIDATION_PARAMS: WPErrorCode = {
    code: WP_ERRORCODE_PREFIX + "3107",
    description: "Validation Error: Invalid input params",
    headerStatusCode: 400,
};

export const WP_ERROR_API_ANALYTICS_APPLICATIONS_NEWRELIC_GRAPHQL_ERROR: WPErrorCode = {
    code: WP_ERRORCODE_PREFIX + "3110",
    description: "Could not execute query on newrelic graphql api",
    headerStatusCode: 500,
};

export const WP_ERROR_IAM_USERS_NOT_FOUND: WPErrorCode = {
    code: WP_ERRORCODE_PREFIX + "7406",
    description: "An internal error occurred while finding users",
    headerStatusCode: 404,
};
export const WP_ERROR_BC_GENERAL: WPErrorCode = {
    code: WP_ERRORCODE_PREFIX + "100",
    description: "An error occurred while executing blockchain operation.",
    headerStatusCode: 500,
};
export const WP_ERROR_IAM_USER_EXISTS: WPErrorCode = {
    code: WP_ERRORCODE_PREFIX + "7417",
    description: "USer already exists in the repository",
    headerStatusCode: 404,
};


export const WP_ERROR_IAM_USER_NOT_CREATED: WPErrorCode = {
    code: WP_ERRORCODE_PREFIX + "7400",
    description: "An internal error occurred while creating user",
    headerStatusCode: 500,
};
export const WP_ERROR_IAM_USER_NOT_UPDATED: WPErrorCode = {
    code: WP_ERRORCODE_PREFIX + "7401",
    description: "An internal error occurred while updating user",
    headerStatusCode: 500,
};
export const WP_ERROR_IAM_USER_NOT_DELETED: WPErrorCode = {
    code: WP_ERRORCODE_PREFIX + "7402",
    description: "An internal error occurred while deleting user",
    headerStatusCode: 500,
};
export const WP_ERROR_IAM_USER_NOT_PURGED: WPErrorCode = {
    code: WP_ERRORCODE_PREFIX + "7403",
    description: "An internal error occurred while purging user",
    headerStatusCode: 500,
};
export const WP_ERROR_IAM_USER_NOT_FOUND_BY_ID: WPErrorCode = {
    code: WP_ERRORCODE_PREFIX + "7404",
    description: "User could not be found by identifier",
    headerStatusCode: 404,
};
export const WP_ERROR_IAM_USER_NOT_FOUND: WPErrorCode = {
    code: WP_ERRORCODE_PREFIX + "7405",
    description: "An internal error occurred while finding user by identifier",
    headerStatusCode: 404,
};
export const WP_ERROR_IAM_USER_VALIDATION_PARAMS: WPErrorCode = {
    code: WP_ERRORCODE_PREFIX + "7407",
    description: "Validation Error: Invalid input params",
    headerStatusCode: 500,
};
export const WP_ERROR_IAM_USER_IMAGE_UPLOAD: WPErrorCode = {
    code: WP_ERRORCODE_PREFIX + "7408",
    description: "An error occurred while uploading image",
    headerStatusCode: 500,
};
export const WP_ERROR_IAM_USER_KYC_NOT_FOUND: WPErrorCode = {
    code: WP_ERRORCODE_PREFIX + "7409",
    description: "An error occurred while finding user kyc",
    headerStatusCode: 500,
};
export const WP_ERROR_IAM_USER_IDS_DONT_MATCH: WPErrorCode = {
    code: WP_ERRORCODE_PREFIX + "7410",
    description: "User ids don't match",
    headerStatusCode: 500,
};
export const WP_ERROR_IAM_USER_NOT_FOUND_BY_EMAIL: WPErrorCode = {
    code: WP_ERRORCODE_PREFIX + "7411",
    description: "User could not be found by identifier",
    headerStatusCode: 404,
};
export const WP_ERROR_IAM_USER_NOT_FOUND_BY_UUID: WPErrorCode = {
    code: WP_ERRORCODE_PREFIX + "7412",
    description: "User could not be found by identifier",
    headerStatusCode: 404,
    };
export const WP_ERROR_IAM_USER_ATTRIBUTE_NOT_AVAILABLE: WPErrorCode = {
    code: WP_ERRORCODE_PREFIX + "7413",
    description: "User attribute is not in the list of available attributes",
    headerStatusCode: 400,
};
export const WP_ERROR_IAM_USER_ATTRIBUTE_NOT_UPDATABLE: WPErrorCode = {
    code: WP_ERRORCODE_PREFIX + "7414",
    description: "User attribute is not updatable",
    headerStatusCode: 400,
};
export const WP_ERROR_IAM_USER_ATTRIBUTE_VALIDATION: WPErrorCode = {
    code: WP_ERRORCODE_PREFIX + "7415",
    description: "Invalid user attribute(s)",
    headerStatusCode: 400,
};
export const WP_ERROR_IAM_USER_KYC_DUPLICATE_NATIONAL_ID: WPErrorCode = {
    code: WP_ERRORCODE_PREFIX + "7416",
    description: "National ID already exists in the database",
    headerStatusCode: 409,
};
export const WP_ERROR_AWS_KMS_VALIDATION: WPErrorCode = {
    code: WP_ERRORCODE_PREFIX + "7950",
    description: "Validation Error: Invalid input params.",
    headerStatusCode: 400,
  };
export const WP_ERROR_IAM_USER_ATTRIBUTE_NOT_CREATABLE: WPErrorCode = {
    code: WP_ERRORCODE_PREFIX + "7427",
    description: "User attribute is not creatable",
    headerStatusCode: 400,
}
export const WP_ERROR_USER_COGNITO_UNAUTHORIZED: WPErrorCode = {
    code: WP_ERRORCODE_PREFIX + "7420",
    description: "Insufficient permissions to call cognito operations on AWS",
    headerStatusCode: 403,
};
export const WP_ERROR_USER_COGNITO_NOT_FOUND_EXCEPTION: WPErrorCode = {
    code: WP_ERRORCODE_PREFIX + "7421",
    description: "User not found in AWS Cognito",
    headerStatusCode: 404,
};
export const WP_ERROR_USER_COGNITO_VALIDATION_PARAMS: WPErrorCode = {
    code: WP_ERRORCODE_PREFIX + "7422",
    description: "Validation Error: Invalid input params on AWS Cognito",
    headerStatusCode: 400,
};
export const WP_ERROR_COGNITO_GENERAL_ERROR: WPErrorCode = {
    code: WP_ERRORCODE_PREFIX + "7423",
    description: "An internal error occurred while running cognito operation on AWS Cognito",
    headerStatusCode: 500,
};
export const WP_ERROR_USER_COGNITO_AUTH_USER_NOTAUTHORIZED: WPErrorCode = {
    code: WP_ERRORCODE_PREFIX + "7424",
    description: "User provided invalid credentials.",
    headerStatusCode: 403,
};
export const WP_ERROR_USER_COGNITO_AUTH_USER_INVALID_TOKEN: WPErrorCode = {
    code: WP_ERRORCODE_PREFIX + "7425",
    description: "Invalid token provided.",
    headerStatusCode: 403,
};
export const WP_ERROR_USER_COGNITO_AUTH_GENERAL_ERROR: WPErrorCode = {
    code: WP_ERRORCODE_PREFIX + "7426",
    description: "An internal error occurred while running cognito auth operation on AWS Cognito",
    headerStatusCode: 500,
};
export const WP_ERROR_IAM_USER_PROFILE_KYC_ERROR: WPErrorCode = {
    code: WP_ERRORCODE_PREFIX + "7508",
    description: "An error occurred while processing KYC",
    headerStatusCode: 400,
};
export const WP_ERROR_GENERIC_OPERATOR_TYPE_NOT_FOUND: WPErrorCode = {
    code: WP_ERRORCODE_PREFIX + "8000",
    description: "Filter operator type is not found.",
    headerStatusCode: 404,
};

