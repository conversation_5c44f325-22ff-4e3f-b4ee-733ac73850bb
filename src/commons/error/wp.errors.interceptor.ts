import { Injectable, NestInterceptor, ExecutionContext, CallH<PERSON>ler, HttpException, HttpStatus, Logger, ServiceUnavailableException } from "@nestjs/common";
import { Observable, throwError, tap } from "rxjs";
import { catchError } from "rxjs/operators";
import { WP_ERROR_INTERNAL_SERVER } from "./error.codes";
import { WPError } from "./wp.error";
import { WPExceptionMessage } from "./wp.exception.message";

/**
 * Generic error interceptor to intercept errors including (@link WPError) and convert them meaningful HttpExceptions.
 * The interceptor is used with http controller in Nest framework. For that reason, all errors are bundled into HttpException.
 *
 * Other controllers and transports should implement different interceptors in order to do proper exception handling.
 *
 * Each microservice or modules implemented with nodejs & nest framework should configure and enable the interceptor in their module definitions
 *
 * ```nodejs
 *  providers: [
 *   {
 *     provide: APP_INTERCEPTOR,
 *     useClass: ErrorsInterceptor,
 *   },
 *   AppService
 *  ]
 * ```
 */
@Injectable()
export class WPErrorsInterceptor implements NestInterceptor {
    private readonly logger = new Logger(WPErrorsInterceptor.name);

    constructor() { }

    intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
        return next.handle().pipe(
            catchError((err: Error) =>
                throwError(() => {
                    this.logger.error("error: ", err.stack);
                    let http_status: number = HttpStatus.INTERNAL_SERVER_ERROR;

                    if (err instanceof WPError) {
                        const wge: WPError = err as WPError;
                        const transportErrorCode: string | undefined = wge.transportErrorCode;
                        if (transportErrorCode) {
                            http_status = Number.parseInt(transportErrorCode);
                        } else {
                            switch (wge.getErrorCode().code) {
                                case WP_ERROR_INTERNAL_SERVER.code:
                                    http_status = HttpStatus.INTERNAL_SERVER_ERROR;
                                    break;
                                default:
                                    http_status = HttpStatus.INTERNAL_SERVER_ERROR;
                                    break;
                            }
                        }
                        return new HttpException(new WPExceptionMessage(http_status, wge.message, wge.getErrorCode().code, err.message, "", wge.stack), http_status);
                    } else if (err instanceof ServiceUnavailableException) {
                        return err;
                    } else {
                        return new HttpException(new WPExceptionMessage(http_status, err.message, WP_ERROR_INTERNAL_SERVER.code, err.message, "", err.stack), http_status);
                    }
                }),
            ),
        );
    }
}
