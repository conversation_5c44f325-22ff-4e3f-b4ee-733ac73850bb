import { ExceptionFilter, Catch, ArgumentsHost, HttpException, ForbiddenException, BadRequestException, UnauthorizedException, HttpStatus, Logger } from '@nestjs/common';
import { Request, Response } from 'express';
import { WP_ERROR_INTERNAL_SERVER, WP_ERROR_VALIDATION_PARAMS, WP_FORBIDDEN, WP_UNAUTHORIZED } from './error.codes';
import { WPErrorUtils } from './wp.error.utils';
import { WPError } from './wp.error';

@Catch(Error)
export class HttpExceptionFilter implements ExceptionFilter {

    private readonly logger = new Logger(HttpExceptionFilter.name);

    catch(exception: Error, host: ArgumentsHost) {
        const ctx = host.switchToHttp();
        const response = ctx.getResponse<Response>();
        const request = ctx.getRequest<Request>();
        let http_status = 500;
        let errorCode = "";
        let message;
        let details = "";
        let stack:string | undefined = "";

        this.logger.error(exception);
        
        details = exception.message;
        stack = exception.stack;

        if (exception instanceof WPError) {

            const wpError = exception as WPError;

            errorCode = wpError.errorCode.code;
            http_status = wpError.errorCode.headerStatusCode;
            message = wpError.errorCode.description;
            details = wpError.message
            stack = (wpError.stack ? wpError.stack + ' ' : '')  + wpError["cause"];
        }
        else if (exception instanceof UnauthorizedException) {
            http_status = HttpStatus.UNAUTHORIZED;
            errorCode = WP_UNAUTHORIZED.code;
            message = WP_UNAUTHORIZED.description;
        } else if (exception instanceof ForbiddenException) {
            http_status = HttpStatus.FORBIDDEN;
            errorCode = WP_FORBIDDEN.code;
            message = WP_FORBIDDEN.description;
        } else if(exception instanceof BadRequestException) {
            const exceptionDetailMessage = WPErrorUtils.processBadRequestException(exception);
            http_status = HttpStatus.BAD_REQUEST;
            errorCode = WP_ERROR_VALIDATION_PARAMS.code;
            message = exceptionDetailMessage;
        } else {
            http_status = HttpStatus.INTERNAL_SERVER_ERROR;
            errorCode = WP_ERROR_INTERNAL_SERVER.code;
            message = WP_ERROR_INTERNAL_SERVER.description;
        }
        if(!http_status) {
            http_status = 500;
        }

        let jsonObj:any;
        if(process.env.APP_ENV == "dev" || process.env.APP_ENV == "development") {
            jsonObj =  {
                statusCode: http_status,
                message:message,
                details: details,
                errorCode:errorCode,
                path: request.url,
                stack:stack
            }
        }
        else {
            jsonObj =  {
                statusCode: http_status,
                message:message,
                errorCode:errorCode,
            }
        }

        response
            .status(http_status)
            .json(
                jsonObj
            );
    }
}