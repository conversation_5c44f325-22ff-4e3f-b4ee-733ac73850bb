import { BadRequestException, ValidationError } from "@nestjs/common";
import { isArray, isString } from "class-validator";

export class WPErrorUtils {
    public static processBadRequestException(bde: BadRequestException) {
        const response = bde.getResponse();
        let messageStr = "Invalid request";

        if(response instanceof Object) {
            const message = response['message'];

            if(message && isArray(message)) {
                const validationError = message[0] as ValidationError;
                const messages: string[] = [];
                
                if (validationError.constraints) {
                    Object.values(validationError.constraints).forEach((message) => {
                      messages.push(message);
                    });

                    messageStr = messages.join(', ');
                }
            } else if (message && message instanceof String) {
                messageStr = message.toString();
            } else {
                messageStr = response["error"];
            }
        } else {
            messageStr = response;
        }

        return messageStr;
    }
}