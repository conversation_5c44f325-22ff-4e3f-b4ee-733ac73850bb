import { ExceptionFilter, Catch, ArgumentsHost, BadRequestException, HttpException } from "@nestjs/common";
import { WP_ERROR_INPUT_VALIDATION } from "./error.codes";
import { Request, Response } from "express";

@Catch(HttpException)
export class BadRequestHttpExceptionFilter implements ExceptionFilter<HttpException> {
    catch(exception: HttpException, host: ArgumentsHost) {
        const ctx = host.switchToHttp();
        const response = ctx.getResponse<Response>();
        const request = ctx.getRequest<Request>();
        const status = exception.getStatus();

        let error: string;
        let responseObj;
        if (exception instanceof BadRequestException) {
            responseObj = {
                statusCode: status,
                message: exception.getResponse()["message"].toString() ?? exception.getResponse(),
                errorCode: WP_ERROR_INPUT_VALIDATION.code,
                details: exception.stack,
            };
        } else {
            responseObj = exception.getResponse();
        }

        response
            .status(status)
            // you can manipulate the response here
            .json(responseObj);
    }
}
