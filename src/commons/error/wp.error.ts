import { WPErrorCode } from "./error.codes";

/**
 * Generic error definitions for E. WPErrors contain a specific WPErrorCode that is defined in the general error catalog.
 *
 * WPError extends Error class in type script so that it can be used/handled system wide.
 * 
 * `transportErrorCode` relates to the transport such as http or grpc and is assigned to `statusCode` in the  export class WPExceptionMessage class.
 */
export class WPError extends Error {
    public errorCode: WPErrorCode;
    public transportErrorCode?:string
    public nonRetryable:boolean;

    public cause?: string | Error | null;

    constructor(errorCode: WPErrorCode, message?: string, cause?: string | Error, transportErrorCode?:string, nonRetryable:boolean = false) {
        let msg = message ? message : `${errorCode.code} : ${errorCode.description}`;
    
        super(msg);
        this.cause = cause ?? null;
        this.errorCode = errorCode;
        this.transportErrorCode = transportErrorCode;
        this.nonRetryable = nonRetryable;

        if(cause) {
            if (cause instanceof Error) {
                if(cause.stack) {
                    this.stack = `${this.stack}\nCaused by: ${cause.stack}`;
                }
            }
            else {
                this.stack = `${this.stack}\nCaused by: ${cause}`;
            }
        }
       
    }

    public getErrorCode(): WPErrorCode {
        return this.errorCode;
    }

    public static buildNonRetryable(errorCode: WPErrorCode, message?: string, stack?: string, transportErrorCode?:string) {
        return new WPError(errorCode, message, stack, transportErrorCode,true);
    }
}