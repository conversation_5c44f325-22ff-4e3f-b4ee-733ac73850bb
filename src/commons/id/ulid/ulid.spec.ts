import { Ulid } from './uild';


const sleep = (ms) => new Promise(r => setTimeout(r, ms));

describe('Ulid Service', () => {
    beforeEach(async () => { });


    it('should return a ulid id', () => {
        let id = Ulid.getId();
        expect(id).not.toBeUndefined();
        expect(id).not.toBeNull();
    });

    it('should return a ulid id from current time', () => {
        let now = new Date().getMilliseconds();
        let id = Ulid.getId(now);

        expect(id).not.toBeUndefined();
        expect(id).not.toBeNull();
    });


    it('should return monotonic ulid ids from current time', () => {
        let now = new Date().getMilliseconds();
        let id = Ulid.getId(now);

        expect(id).not.toBeUndefined();
        expect(id).not.toBeNull();

        sleep(300);

        let id2 = Ulid.getId(now);

        expect(id2).not.toBeUndefined();
        expect(id2).not.toBeNull();

        let time1 = Ulid.decodeTime(id);

        expect(time1).not.toBeUndefined();
        expect(time1).not.toBeNull();

        let time2 = Ulid.decodeTime(id2);

        expect(time2).not.toBeUndefined();
        expect(time2).not.toBeNull();
        expect(time2).toEqual(time1);

        let id3 = Ulid.getId();

        expect(id3).not.toBeUndefined();
        expect(id3).not.toBeNull();

        let time3 = Ulid.decodeTime(id3);

        expect(time3).not.toBeUndefined();
        expect(time3).not.toBeNull();
        expect(time3).toBeGreaterThan(time1);

    });

    it('should decode and encode uild', () => {
        let id = Ulid.getId();

        let decoded = Ulid.decode(id);
        expect(decoded).not.toBeUndefined();
        expect(decoded).not.toBeNull();

        let encoded = Ulid.encode(decoded);
        expect(encoded).not.toBeUndefined();
        expect(encoded).not.toBeNull();

        expect(encoded).toEqual(id);

    });
});
