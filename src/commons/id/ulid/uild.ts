import { monotonicFactory, decodeTime, isValid, ULIDFactory } from "ulidx";
import { Crockford32Coder } from "./crockford32";


/**
 * Universal Unique Lexicographically Sortable Identifier (ULID)
 * A ULID is a 128-bit identifier that is lexicographically sortable and unique across space and time.
 * It is composed of a timestamp and a random component.
 * The timestamp is the number of milliseconds since the Unix epoch.
 * The random component is generated from a secure random number generator.
 * The combination of these two components ensures that ULIDs are unique and sortable.
 * 
 * const id1 = Ulid.getId();
 * const id2 = Ulid.getId(); // id2 >= id1 within the same process
 */
export class Ulid {


   private static crockford32Coder = new Crockford32Coder();

   /**
    * One monotonic generator per process/module instance. Itkeeps state in-process. 
    * If two calls land in the same millisecond, it increments the 80-bit random field so the next ULID is strictly larger, not equal
    */
   private static ulidFactory = monotonicFactory();

   /**
    * Generate a monotonic ULID (lexicographically increasing within this process).
    * Pass seedTime (ms since epoch) only for tests; defaults to Date.now().
    */
   static getId(seedTime?: number): string {
      return this.ulidFactory(seedTime);
   }

   static decode(data: string): Uint8Array {
      return this.crockford32Coder.decodeTrusted(data);
   }

   static encode(bytes: Uint8Array) {
      return this.crockford32Coder.encodeTrusted(bytes);
   }

   static decodeTime(id: string): number {
      return decodeTime(id);
   }

   static isValid(id: string): boolean {
      return isValid(id);
   }
}