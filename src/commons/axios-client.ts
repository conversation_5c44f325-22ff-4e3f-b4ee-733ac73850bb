import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from "axios";
import axiosRetry from "axios-retry";
import { WPError } from "./error/wp.error";
import * as util from "util";
import { Logger } from "@nestjs/common";
import { WP_ERROR_BC_GENERAL } from "./error/error.codes";

const headers: Readonly<Record<string, string | boolean>> = {
    Accept: "application/json",
    "Content-Type": "application/json; charset=utf-8",
    "Access-Control-Allow-Credentials": true,
    "X-Requested-With": "XMLHttpRequest",
};

class Http {
    private instance: AxiosInstance | null = null;
    private apiURL = "";
    logger = new Logger(Http.name);

    private get http(): AxiosInstance {
        return this.instance != null ? this.instance : this.initHttp();
    }

    initHttp() {
        const http: AxiosInstance = axios.create({
            baseURL: this.apiURL,
            headers,
            withCredentials: false,
        });

        // Add retry logic with axios-retry
        axiosRetry(http, {
            retries: 1, // You can adjust the number of retries
            retryDelay: axiosRetry.exponentialDelay,
            shouldResetTimeout: true,
            retryCondition: error => axiosRetry.isNetworkOrIdempotentRequestError(error),
            onRetry: (err, retryCount) => {
                this.logger.error(`Retry attempt #${retryCount}, error: ${util.inspect(err)}`);
            },
        });

        http.interceptors.response.use(
            response => response,
            error => {
                return this.handleError(error);
            },
        );

        http.interceptors.request.use(
            request => request,
            error => {
                return this.handleError(error);
            },
        );

        this.instance = http;
        return http;
    }

    request<T = any, R = AxiosResponse<T>>(config: AxiosRequestConfig): Promise<R> {
        return this.http.request(config);
    }

    get<T = any, R = AxiosResponse<T>>(url: string, config?: AxiosRequestConfig): Promise<R> {
        return this.http.get<T, R>(url, config);
    }

    post<T = any, R = AxiosResponse<T>>(url: string, data?: T, config?: AxiosRequestConfig): Promise<R> {
        return this.http.post<T, R>(url, data, config);
    }

    put<T = any, R = AxiosResponse<T>>(url: string, data?: T, config?: AxiosRequestConfig): Promise<R> {
        return this.http.put<T, R>(url, data, config);
    }

    delete<T = any, R = AxiosResponse<T>>(url: string, config?: AxiosRequestConfig): Promise<R> {
        return this.http.delete<T, R>(url, config);
    }

    // Handle global app errors
    // We can handle generic app errors depending on the status code
    // https://axios-http.com/docs/handling_errors
    private handleError(error) {
        this.logger.error(`WPAxios Error: ${util.inspect(error)}`);
        const { status } = error;
        const wpError = new WPError(WP_ERROR_BC_GENERAL, `error: ${util.inspect(error)}, status code: ${status}, request: ${util.inspect(error.config?.data)}`);
        return Promise.reject(wpError);
    }
}

export const WPAxios = new Http();
