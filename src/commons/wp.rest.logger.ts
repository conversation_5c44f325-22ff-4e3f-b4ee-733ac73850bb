import { Injectable, Logger, NestMiddleware } from "@nestjs/common";
import { Request, Response, NextFunction } from "express";
import { O11yUtils } from "./o11y/o11y-utils";

/**
 * Middleware for logging incoming HTTP requests and HTTP responses.
 */
@Injectable()
export class LoggerMiddleware implements NestMiddleware {
  private readonly logger = new Logger(LoggerMiddleware.name);

  use(req: Request, res: Response, next: NextFunction) {
    const { ip, method, originalUrl, url } = req;

    if (O11yUtils.ignoreHealthCheckLogs(originalUrl)) {
      // Ends middleware function execution, hence allowing to move on
      if (next) {
        next();
      }
      return;
    }

    const userAgent = req.get("user-agent") || "";
    // Gets the request log
    this.logger.log(
      `req: ${JSON.stringify(
        {
          ip: ip,
          userAgent: userAgent,
          method: method,
          originalUrl: originalUrl,
          path: url,
          headers: req.headers,
          body: req.body,
        },
        null,
        "\t",
      )}`,
    );

    res.on("finish", () => {
      const { statusCode } = res;
      const contentLength = res.get("content-length");

      this.logger.log(
        `res: ${JSON.stringify(
          {
            ip: ip,
            userAgent: userAgent,
            method: method,
            originalUrl: originalUrl,
            path: url,
            headers: res.getHeaders(),
            statusCode: statusCode,
            contentLength: contentLength,
          },
          null,
          "\t",
        )}`,
      );
    });

    // Ends middleware function execution, hence allowing to move on
    if (next) {
      next();
    }
  }

  getResponseLog = (ip, method, originalUrl, url, userAgent, res: Response) => {
    const rawResponse = res.write;
    const rawResponseEnd = res.end;
    const chunkBuffers = <any>[];
    res.write = (...chunks) => {
      const resArgs = <any>[];
      for (let i = 0; i < chunks.length; i++) {
        resArgs[i] = chunks[i];
        if (!resArgs[i]) {
          res.once("drain", res.write);
          i--;
        }
      }
      // Sonar typescript:S2310
      if (resArgs[0]) {
        chunkBuffers.push(Buffer.from(resArgs[0]));
      }
      return rawResponse.apply(res, resArgs);
    };
    res.end = (...chunk) => {
      const resArgs = <any>[];
      for (let i = 0; i < chunk.length; i++) {
        resArgs[i] = chunk[i];
      }
      if (resArgs[0]) {
        chunkBuffers.push(Buffer.from(resArgs[0]));
      }
      const body = Buffer.concat(chunkBuffers).toString("utf8");
      const responseLog = JSON.stringify(
        {
          ip: ip,
          userAgent: userAgent,
          method: method,
          originalUrl: originalUrl,
          path: url,
          statusCode: res.statusCode,
          headers: res.getHeaders(),
          body: body,
        },
        null,
        "\t",
      );
      rawResponseEnd.apply(res, resArgs);
      return responseLog as unknown as Response;
    };
  };
}
