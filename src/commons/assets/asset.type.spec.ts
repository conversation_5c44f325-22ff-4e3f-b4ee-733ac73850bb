import { ALL_ASSETS, AVALANCHE, CryptoAsset } from "./asset.type";

describe('Asset Types', () => {
  beforeEach(async () => {});

  describe('All Assets', () => {
    it('should return all assets with networks', () => {
      let avax: CryptoAsset = AVALANCHE;
      expect(ALL_ASSETS).not.toBeUndefined();
      expect(ALL_ASSETS[0].networks).not.toBeUndefined();
      expect(ALL_ASSETS[0].networks).not.toBeNull();
      expect(ALL_ASSETS[0].networks.length).toEqual(1)
    });

  });

  describe('Asset Type Avalanche', () => {
    it('should return alalanche with networks', () => {
      let avax: CryptoAsset = AVALANCHE;
      expect(avax.networks).not.toBeUndefined();
      expect(avax.networks).not.toBeNull();
      expect(avax.networks.length).toEqual(3);
    });

  });

  
});
