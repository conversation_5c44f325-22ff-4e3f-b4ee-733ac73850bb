export type CryptoAsset = {
    id: number;
    name: string;
    symbol: string;
    imageUrl: string;
    // 0 Coin, 1 Token, 2 Fiat
    type: number;
    enabled: boolean;
    withdrawEnabled: boolean;
    depositEnabled: boolean;
    description: string;
    dailyLimit: number;
    tenantId: string;
    networks: BlockchainNetwork[];
};

export type BlockchainNetwork = {
    id: number;
    name: string;
    description: string;
    derivationPath: string, //  m/purpose'/ coin_type'/account'/change/${i} addresses
    minWithdrawal: number;
    fee: string;
    feeless: boolean;
    tenantId: string;
    // 2 Enable, 1 Disabled, 0 Paused
    status: number;
};

export var BINANCE_SMART_CHAIN_NETWORK: BlockchainNetwork = {
    id: 1,
    name: "Binance Smart Chain",
    description: "Binance Smart Chain Network",
    derivationPath: "m/44'/60'/0'/0", //  m/purpose'/ coin_type'/account'/change/${i} addresses
    minWithdrawal: 0.1,
    fee: "0.001",
    feeless: false,
    tenantId: "01GQ7Y2FHD0C8C5H8F342NEJF5",
    status: 2,
},
    NANO_NETWORK: BlockchainNetwork = {
        id: 2,
        name: "Nano",
        description: "Nano Network",
        derivationPath: "m/44'/165'", // m/purpose'/ coin_type'/account'/change/${i} addresses
        minWithdrawal: 0.1,
        fee: "0",
        feeless: true,
        tenantId: "01GQ7Y2FHD0C8C5H8F342NEJF5",
        status: 2,
    },
    BANANO_NETWORK: BlockchainNetwork = {
        id: 3,
        name: "Banano",
        description: "Banano Network",
        derivationPath: "m/44'/198'", // m/purpose'/ coin_type'/account'/change/${i} addresses
        minWithdrawal: 0.1,
        fee: "0",
        feeless: true,
        tenantId: "01GQ7Y2FHD0C8C5H8F342NEJF5",
        status: 2,
    },
    ETHEREUM_NETWORK: BlockchainNetwork = {
        id: 4,
        name: "Ethereum",
        description: "Ethereum Network",
        derivationPath: "m/44'/60'/0'/0", //  m/purpose'/ coin_type'/account'/change/${i} addresses
        minWithdrawal: 0.001,
        fee: "0.01",
        feeless: false,
        tenantId: "01GQ7Y2FHD0C8C5H8F342NEJF5",
        status: 1,
    },
    AVALANCHE_X_NETWORK: BlockchainNetwork = {
        id: 5,
        name: "Avalanche X Chain",
        description: "Avalanche X Chain Network",
        derivationPath: "m/44'/9000'/0'/0", // m/purpose'/ coin_type'/account'/change/${i} addresses
        minWithdrawal: 0.01,
        fee: "0.001",
        feeless: false,
        tenantId: "01GQ7Y2FHD0C8C5H8F342NEJF5",
        status: 1,
    },
    AVALANCHE_C_NETWORK: BlockchainNetwork = {
        id: 6,
        name: "Avalanche C Chain",
        description: "Avalanche C Chain Network",
        derivationPath: "m/44'/60'/0'/0", //  m/purpose'/ coin_type'/account'/change/${i} addresses
        minWithdrawal: 0.1,
        fee: "0.01",
        feeless: false,
        tenantId: "01GQ7Y2FHD0C8C5H8F342NEJF5",
        status: 1,
    },
    SOLANA_NETWORK: BlockchainNetwork = {
        id: 7,
        name: "Solana",
        description: "Solana Network",
        derivationPath: "m/44'/501'/0'/0", // m/purpose'/ coin_type'/account'/change/${i} addresses
        minWithdrawal: 0.5,
        fee: "0.01",
        feeless: false,
        tenantId: "01GQ7Y2FHD0C8C5H8F342NEJF5",
        status: 1,
    },

    TETHER_NETWORK: BlockchainNetwork = {
        id: 8,
        name: "Tether",
        description: "tether Nework",
        derivationPath: "m/44'/0'/0'/0", // m/purpose'/ coin_type'/account'/change/${i} addresses
        minWithdrawal: 0.5,
        fee: "0.01",
        feeless: false,
        tenantId: "01GQ7Y2FHD0C8C5H8F342NEJF5",
        status: 1,
    },
    AVALANCHE_TEST_NETWORK: BlockchainNetwork = {
        id: 9,
        name: "Avalanche Fuji Test Network",
        description: "Avalanche  Fuji Test Network",
        derivationPath: "m/44'/60'/0'/0", //  m/purpose'/ coin_type'/account'/change/${i} addresses
        minWithdrawal: 0.1,
        fee: "0.01",
        feeless: false,
        tenantId: "01GQ7Y2FHD0C8C5H8F342NEJF5",
        status: 1,
    },
    CHILIZ_NETWORK: BlockchainNetwork = {
        id: 10,
        name: "Chiliz",
        description: "Chiliz Nework",
        derivationPath: "m/44'/0'/0'/0", // m/purpose'/ coin_type'/account'/change/${i} addresses
        minWithdrawal: 0.5,
        fee: "0.01",
        feeless: false,
        tenantId: "01GQ7Y2FHD0C8C5H8F342NEJF5",
        status: 1,
    },
    SKALE_NETWORK: BlockchainNetwork = {
        id: 11,
        name: "Skale",
        description: "Skale Nework",
        derivationPath: "m/44'/60'/0'/0", // m/purpose'/ coin_type'/account'/change/${i} addresses
        minWithdrawal: 0.5,
        fee: "0.01",
        feeless: false,
        tenantId: "01GQ7Y2FHD0C8C5H8F342NEJF5",
        status: 1,
    };

export var WODO_TOKEN: CryptoAsset = {
    id: 1, name: "Wodo TOKEN", symbol: "XWGT", tenantId: "01GQ7Y2FHD0C8C5H8F342NEJF5",
    imageUrl: "https://static.wodo.io/asset/wodo-gaming-token.png",
    type: 1, enabled: true, withdrawEnabled: false, depositEnabled: true, description: "Wodo Gaming token", dailyLimit: 200,
    networks: [BINANCE_SMART_CHAIN_NETWORK]
},
    NANO: CryptoAsset = {
        id: 2, name: "Nano", symbol: "XNO", tenantId: "01GQ7Y2FHD0C8C5H8F342NEJF5",
        imageUrl: "https://static.wodo.io/asset/nano-xno-logo.png",
        type: 0, enabled: true, withdrawEnabled: false, depositEnabled: true, description: "NANO blockchain", dailyLimit: 100,
        networks: [NANO_NETWORK]
    },
    BANANO: CryptoAsset = {
        id: 3, name: "Banano", symbol: "BAN", tenantId: "01GQ7Y2FHD0C8C5H8F342NEJF5",
        imageUrl: "https://static.wodo.io/asset/banano-ban-logo.png",
        type: 0, enabled: true, withdrawEnabled: false, depositEnabled: true, description: "Banano blockchain", dailyLimit: 100,
        networks: [BANANO_NETWORK]
    },
    ETHEREUM: CryptoAsset = {
        id: 4, name: "Ethereum", symbol: "ETH", tenantId: "01GQ7Y2FHD0C8C5H8F342NEJF5",
        imageUrl: "https://static.wodo.io/asset/ethereum-eth-logo.png",
        type: 0, enabled: true, withdrawEnabled: false, depositEnabled: false, description: "Ethereum blockchain", dailyLimit: 200,
        networks: [ETHEREUM_NETWORK]
    },
    AVALANCHE: CryptoAsset = {
        id: 5, name: "Avalanche", symbol: "AVAX", tenantId: "01GQ7Y2FHD0C8C5H8F342NEJF5",
        imageUrl: "https://static.wodo.io/asset/avalanche-avax-logo.png",
        type: 0, enabled: true, withdrawEnabled: false, depositEnabled: false, description: "Avax coin on Avalanche blockchain", dailyLimit: 200,
        networks: [AVALANCHE_X_NETWORK, AVALANCHE_C_NETWORK, AVALANCHE_TEST_NETWORK]
    },
    BNB: CryptoAsset = {
        id: 6, name: "Binance Coin", symbol: "BNB", tenantId: "01GQ7Y2FHD0C8C5H8F342NEJF5",
        imageUrl: "https://static.wodo.io/asset/bnb-bnb-logo.png", type: 0, enabled: true, withdrawEnabled: false, depositEnabled: true, description: "Binance coin on Binance Smart blockchain", dailyLimit: 200,
        networks: [BINANCE_SMART_CHAIN_NETWORK]
    },
    SOL: CryptoAsset = {
        id: 7, name: "Solano", symbol: "SOL", tenantId: "01GQ7Y2FHD0C8C5H8F342NEJF5",
        imageUrl: "https://static.wodo.io/asset/solana-sol-logo.png", type: 0, enabled: false, withdrawEnabled: false, depositEnabled: false, description: "Solana blockchain", dailyLimit: 200,
        networks: [SOLANA_NETWORK]
    },
    USDT: CryptoAsset = {
        id: 8, name: "Tether", symbol: "USDT", tenantId: "01GQ7Y2FHD0C8C5H8F342NEJF5",
        imageUrl: "https://static.wodo.io/asset/tether-usdt-logo-128.png", type: 0, enabled: false, withdrawEnabled: false, depositEnabled: false, description: "tether blockchain", dailyLimit: 200,
        networks: [TETHER_NETWORK]
    },
    JGT_TOKEN: CryptoAsset = {
        id: 9, name: "Joy Gaming Token", type: 1, symbol: "JGT", tenantId: "01GQ7Y2FHD0C8C5H8F342NEJF5",
        imageUrl: "https://static.wodo.io/asset/wodo-joy-token.png",
        enabled: true, withdrawEnabled: false, depositEnabled: true, description: "Joy Gaming Token ", dailyLimit: 500,
        networks: [BINANCE_SMART_CHAIN_NETWORK]
    },
    CHZ: CryptoAsset = {
        id: 10, name: "Chiliz", type: 0, symbol: "CHZ", tenantId: "01GQ7Y2FHD0C8C5H8F342NEJF5",
        imageUrl: "https://static.wodo.io/asset/chiliz-chz-logo-64.png",
        enabled: true, withdrawEnabled: false, depositEnabled: false, description: "CHZ chiliz coin", dailyLimit: 500,
        networks: [CHILIZ_NETWORK]
    },
    SUPER: CryptoAsset = {
        id: 11, name: "SuperVerse TOKEN", symbol: "SUPER", tenantId: "01GQ7Y2FHD0C8C5H8F342NEJF5",
        imageUrl: "https://static.wodo.io/asset/superverse-token-logo-64.png",
        type: 1, enabled: true, withdrawEnabled: false, depositEnabled: true, description: "Superverse token", dailyLimit: 200,
        networks: [ETHEREUM_NETWORK]
    },
    SKL: CryptoAsset = {
        id: 12, name: "Skale", type: 0, symbol: "SKL", tenantId: "01GQ7Y2FHD0C8C5H8F342NEJF5",
        imageUrl: "https://static.wodo.io/asset/skale-skl-logo-64.png",
        enabled: true, withdrawEnabled: false, depositEnabled: false, description: "Skale skl coin", dailyLimit: 500,
        networks: [SKALE_NETWORK]
    };


export const ALL_ASSETS_NETWORKS = [
    BINANCE_SMART_CHAIN_NETWORK, NANO_NETWORK,
    BANANO_NETWORK, ETHEREUM_NETWORK,
    AVALANCHE_X_NETWORK, AVALANCHE_C_NETWORK, AVALANCHE_TEST_NETWORK,
    SOLANA_NETWORK, TETHER_NETWORK, CHILIZ_NETWORK, SKALE_NETWORK];
export const ALL_ASSETS = [WODO_TOKEN, NANO, BANANO, ETHEREUM, AVALANCHE, BNB, JGT_TOKEN,CHZ, SOL, SUPER, SKL];


export function findAssetBySymbol(symbol: string): CryptoAsset {
    let found: CryptoAsset | undefined = ALL_ASSETS.find(obj => {
        return obj.symbol == symbol.toUpperCase();
    });

    if (found) {
        return found;
    } else {
        throw Error(`Unknown asset type: ${symbol}`);
    }
}

export function findAssetById(id: number): CryptoAsset {
    let found: CryptoAsset | undefined = ALL_ASSETS.find(obj => {
        return obj.id == id;
    });

    if (found) {
        return found;
    } else {
        throw Error("Unknown asset id:" + id);
    }
}
