import { NotificationChannelType, NotificationType } from "./enums";

export class NotificationsDto {
    userIds: string[];
    type: string;
    data: NotificationsDataDto;
    idempotencyId: string;
}

export class NotificationsDataDto {
    title: string;
    message: string;
}

export class sendNotificationDto {
    userIds: string[];
    type: NotificationType;
    channelTypes: NotificationChannelType[];
    data: sendNotificationDataDto | any;
    idempotencyId: string;
}

export class sendNotificationDataDto {
    email: string;
    subject: string;
    greetings: string;
    message: string;
    currency: string;
    amount: string;
    txAddress: string;
    txTime: string;
    txId: string;
    code: string;
}
