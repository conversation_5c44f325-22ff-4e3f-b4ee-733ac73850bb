import { ApiProperty } from '@nestjs/swagger';
export class NotificationSendDto {
    constructor(type:string,data:any,channelTypes:string[],userIds:string[],idempotencyId:string){
        this.type = type;
        this.data = data;
        this.channelTypes = channelTypes;
        this.userIds = userIds;
        this.idempotencyId = idempotencyId;
    }

    @ApiProperty({description:"Notification type as one of TX_WITHDRAW_FINISHED, TX_DEPOSIT_FINISHED, AC_CREATED, NOTF_TYPE_DAILY_EARN_LIMIT_REACHED", })
    type: string;
    data: any;
    @ApiProperty({description:"Notification channel types as array of socket, email, sms, webhook", })
    channelTypes: string[];
    userIds: string[];
    idempotencyId: string;
}
