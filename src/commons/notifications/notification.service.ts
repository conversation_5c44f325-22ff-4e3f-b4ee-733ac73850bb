import { Injectable, Logger } from "@nestjs/common";
import { NotificationsDto, sendNotificationDto } from "./notifications-dto";
import { NotificationsProps } from "./notifications.entity";
import { AxiosRequestConfig } from "axios";
import { Role } from "../../commons/auth/role.enum";
import { ConfigService } from "@nestjs/config";
import { WPAxios } from "../../commons/axios-client";
import { NotificationSendDto } from "./notification-send.dto";
import { NotificationChannelType, NotificationType } from "./enums";

@Injectable()
export class NotificationService {
    private readonly logger = new Logger(NotificationService.name);

    constructor(private readonly confService: ConfigService) {}

    async create(notificationSendBody: NotificationSendDto): Promise<void> {
        const notificationApiUrl: string = process.env.NOTIFICATION_API_URL || "";
        //const notificationApiAuthHeader: string = await this.getConfig(NOTIFICATION_API_AUTH_HEADER_PARAM_NAME);
        const notification: sendNotificationDto = {
            userIds: notificationSendBody.userIds,
            type: NotificationType.GENERIC,
            channelTypes: [NotificationChannelType.EMAIL, NotificationChannelType.SOCKET],
            data: notificationSendBody.data,
            idempotencyId: notificationSendBody.idempotencyId,
        };

        console.log("@@@@@@@", notification);
        // we don't save any notification record in database for now
        // let notificationSend = await this.notifyRepo.create(notification);
        // return notificationSend;

        // if (!notificationApiUrl.includes("localhost")) {
        const config: AxiosRequestConfig = {
            headers: {
                //Auth: notificationApiAuthHeader,
                Accept: "application/json",
                "Content-Type": "application/json; charset=utf-8",
                "Access-Control-Allow-Credentials": true,
                "X-Requested-With": "XMLHttpRequest",
                user_groups: `${Role.service_admin}`,
            },
        };

        this.logger.debug(`posting http request for ${notification}`);

        await WPAxios.post<NotificationsDto>(`${notificationApiUrl}/notifications`, notificationSendBody, config);
        // }
    }
}
