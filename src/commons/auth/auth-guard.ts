import { Injectable, CanActivate, ExecutionContext } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { ROLES_KEY, Roles } from './roles.decarator';
import { Role } from './role.enum';
import { ClsService } from 'nestjs-cls';
import { AuthUser } from './auth-user';
import { AuthKeys } from './auth-constnats';

/**
 * see 
 * https://docs.nestjs.com/custom-decorators
 * https://docs.nestjs.com/guards
 */
@Injectable()
export class AuthGuard implements CanActivate {

    constructor(private reflector: Reflector, private cls:ClsService) { }

    canActivate(context: ExecutionContext): boolean {
        if ("true" == (process.env.IS_LOCAL_DEV || "false")) {
            return true;
        }

        const requiredRoles: Role[] = this.reflector.getAllAndOverride<Role[]>(ROLES_KEY, [
            context.getHandler(),
            context.getClass(),
          ]);

        if (!requiredRoles) {
            return false;
        }

        if(requiredRoles.includes(Role.full_access)){
            return true;
        }

        const request = context.switchToHttp().getRequest();
        const groups: string = request.headers[AuthKeys.user_groups] as string;

        if (!groups) {
            return false;
        }
        
        const authorized:boolean = this.matchAllRoles(requiredRoles, groups);

        let authUser = this.cls.get<AuthUser>(AuthKeys.auth_user);
        
        if(authUser)
            authUser.authorized = authorized;

        return authorized;
    }

    /*
    * Checks whether at least one of the given groups matches with the required roles.
    * e.g.; Groups: t_admin and Required roles: t_admin, user. The method returns true.
    */
    matchRoles(requiredRoles: Role[], groups: string) {
        const groupArray: string[] = groups.split(",");
        const roleMatch = requiredRoles.find((element) => {
            return groupArray.includes(element.toString());
        });

        return roleMatch ? true : false;
    }

    /*
    * Checks whether all of the given groups matches with the required roles.
    * e.g.; Groups: t_admin, user and Required roles: t_admin, user 
    * Even if one of the groups are not provided in the required roles, the method returns false
    */
    matchAllRoles(requiredRoles: Role[], groups: string) {
        const groupArray: string[] = groups.split(",");
        const roleMatch = requiredRoles.filter((element) => {
            return groupArray.includes(element.toString());
        });
        
        return roleMatch?.length == requiredRoles.length;
    }
}