import { ExecutionContext, createParamDecorator,  } from '@nestjs/common';
import { Reflector } from '@nestjs/core';

/**
 * see 
 * https://docs.nestjs.com/custom-decorators
 * https://docs.nestjs.com/guards
 */
//export const Roles = Reflector.createDecorator<string[]>();

import { SetMetadata } from '@nestjs/common';
import { Role } from './role.enum';

export const ROLES_KEY = 'roles';
export const Roles = (...roles: Role[]) => SetMetadata(ROLES_KEY, roles);