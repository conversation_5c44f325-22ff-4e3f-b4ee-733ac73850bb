import { ClsMiddleware } from 'nestjs-cls';
import { AuthUser } from './auth-user';
import { AuthKeys } from './auth-constnats';
import { Ulid } from '../id/ulid/uild';

/**
 * AuthMiddlewareFactory to create a new instance of ClsMiddleware.
 * This middleware is used to resolve user information such as the  user_id and user_groups via the HTTP headers in the cls context.
 * The resolved data is set in the cls context and can be accessed in the application.
 * 
 * idGenerator generates a correlation id as Ulid if "correlation_id" is not present in the HTTP headers.
 * 
 * See main.ts for the usage of this middleware.
 */
export class AuthMiddlewareFactory {

    public static newClsMiddlewareInstance() {
        return new ClsMiddleware(
            {
                generateId: true,
                idGenerator: (req: Request) => {
                    return req.headers[AuthKeys.correlation_id] ?? Ulid.getId();
                },
                setup: (cls, req: Request, res: Response) => {
                    cls.set(AuthKeys.user_id, req.headers[AuthKeys.user_id]);
                    cls.set(AuthKeys.auth_user, new AuthUser(req.headers[AuthKeys.user_id], req.headers[AuthKeys.user_groups]?.split(",")))
                }
            }
        );
    }
}
