import { Role } from "./role.enum";

/**
 * The class to carry out authenticated and authorized user data accross the services.
 */
export class UserContext {
    userId: string | undefined;
    groups: string[];
    isKycCompleted: boolean;
    kycEnabled: boolean;
    email?: string | undefined;
    username: string | undefined;

    constructor(userId: string, groups: string[], isKycCompleted: boolean, kycEnabled: boolean, email?: string) {
        this.userId = userId === "undefined" ? undefined : userId;
        this.groups = groups;
        this.isKycCompleted = isKycCompleted;
        this.kycEnabled = kycEnabled;
        this.email = email === "undefined" ? undefined : email;
    }

    isUserInGroup = (group: string): boolean => {
        if(!group) {
            return false;
        }

        return this.groups && this.groups.includes(group);
    }

    isSuperAdmin = () => {
        return this.isUserInGroup(Role.admin);
    }
}
