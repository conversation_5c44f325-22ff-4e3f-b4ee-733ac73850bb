import { ExecutionContext, createParamDecorator,  } from '@nestjs/common';
import { Reflector } from '@nestjs/core';

/**
 * see 
 * https://docs.nestjs.com/custom-decorators
 * https://docs.nestjs.com/guards
 */
//export const Roles = Reflector.createDecorator<string[]>();

import { SetMetadata } from '@nestjs/common';
import { Role } from './role.enum';
import { IdempotentAware } from './idempotent.aware';

export const IDEMPOTENCY_KEY = 'idempotency';
export const Idempotent = (enabled:boolean =true ) => SetMetadata(IDEMPOTENCY_KEY, new IdempotentAware(enabled));