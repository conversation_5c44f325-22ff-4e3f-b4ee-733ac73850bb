import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import axios from 'axios';
import { WPError } from '../error/wp.error';
import { WP_ERROR_INPUT_VALIDATION } from '../error/error.codes';

export interface RecaptchaVerificationResponse {
    success: boolean;
    challenge_ts?: string;
    hostname?: string;
    score?: number;
    action?: string;
    'error-codes'?: string[];
}

@Injectable()
export class RecaptchaService {
    private readonly logger = new Logger(RecaptchaService.name);
    private readonly recaptchaSecretKey: string;
    private readonly recaptchaVerifyUrl = 'https://www.google.com/recaptcha/api/siteverify';
    private readonly isRecaptchaDisabled: boolean;

    constructor(private readonly configService: ConfigService) {
        this.recaptchaSecretKey = this.configService.get<string>('RECAPTCHA_SECRET_KEY') || '';
        this.isRecaptchaDisabled = this.configService.get<string>('DISABLE_RECAPTCHA') === 'true';
        
        if (!this.recaptchaSecretKey && !this.isRecaptchaDisabled) {
            this.logger.warn('RECAPTCHA_SECRET_KEY is not configured. reCAPTCHA verification will fail.');
        }
    }

    /**
     * Verifies a reCAPTCHA token with Google's verification service
     * @param token The reCAPTCHA token to verify
     * @param remoteIp Optional remote IP address for additional verification
     * @returns Promise<boolean> indicating if the verification was successful
     */
    async verifyRecaptcha(token: string, remoteIp?: string): Promise<boolean> {
        // Skip verification if reCAPTCHA is disabled
        if (this.isRecaptchaDisabled) {
            this.logger.debug('reCAPTCHA verification is disabled, skipping verification');
            return true;
        }

        if (!token) {
            this.logger.error('reCAPTCHA token is missing');
            throw new WPError(
                WP_ERROR_INPUT_VALIDATION,
                'reCAPTCHA token is required',
                undefined,
                '400'
            );
        }

        if (!this.recaptchaSecretKey) {
            this.logger.error('reCAPTCHA secret key is not configured');
            throw new WPError(
                WP_ERROR_INPUT_VALIDATION,
                'reCAPTCHA verification is not properly configured',
                undefined,
                '500'
            );
        }

        try {
            const params = new URLSearchParams({
                secret: this.recaptchaSecretKey,
                response: token,
            });

            if (remoteIp) {
                params.append('remoteip', remoteIp);
            }

            this.logger.debug('Verifying reCAPTCHA token with Google');
            
            const response = await axios.post<RecaptchaVerificationResponse>(
                this.recaptchaVerifyUrl,
                params,
                {
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    timeout: 10000, // 10 second timeout
                }
            );

            const verificationResult = response.data;
            
            this.logger.debug('reCAPTCHA verification response:', {
                success: verificationResult.success,
                score: verificationResult.score,
                action: verificationResult.action,
                errorCodes: verificationResult['error-codes']
            });

            if (!verificationResult.success) {
                const errorCodes = verificationResult['error-codes'] || [];
                this.logger.warn('reCAPTCHA verification failed', { errorCodes });
                
                // Provide more specific error messages based on error codes
                let errorMessage = 'reCAPTCHA verification failed';
                if (errorCodes.includes('timeout-or-duplicate')) {
                    errorMessage = 'reCAPTCHA token has expired or been used already';
                } else if (errorCodes.includes('invalid-input-response')) {
                    errorMessage = 'Invalid reCAPTCHA token';
                } else if (errorCodes.includes('invalid-input-secret')) {
                    errorMessage = 'reCAPTCHA configuration error';
                }

                throw new WPError(
                    WP_ERROR_INPUT_VALIDATION,
                    errorMessage,
                    { errorCodes } as any,
                    '400'
                );
            }

            // For reCAPTCHA v3, check the score (optional)
            if (verificationResult.score !== undefined) {
                const minScore = this.configService.get<number>('RECAPTCHA_MIN_SCORE') || 0.5;
                if (verificationResult.score < minScore) {
                    this.logger.warn(`reCAPTCHA score ${verificationResult.score} is below minimum threshold ${minScore}`);
                    throw new WPError(
                        WP_ERROR_INPUT_VALIDATION,
                        'reCAPTCHA verification failed: suspicious activity detected',
                        { score: verificationResult.score, minScore } as any,
                        '400'
                    );
                }
            }

            this.logger.debug('reCAPTCHA verification successful');
            return true;

        } catch (error) {
            if (error instanceof WPError) {
                throw error;
            }

            this.logger.error('Error during reCAPTCHA verification:', error);
            
            if (axios.isAxiosError(error)) {
                if (error.code === 'ECONNABORTED') {
                    throw new WPError(
                        WP_ERROR_INPUT_VALIDATION,
                        'reCAPTCHA verification timeout',
                        error,
                        '408'
                    );
                }
                
                throw new WPError(
                    WP_ERROR_INPUT_VALIDATION,
                    'Failed to verify reCAPTCHA due to network error',
                    error,
                    '503'
                );
            }

            throw new WPError(
                WP_ERROR_INPUT_VALIDATION,
                'reCAPTCHA verification failed due to unexpected error',
                error,
                '500'
            );
        }
    }

    /**
     * Validates that a reCAPTCHA token is present and has the correct format
     * @param token The reCAPTCHA token to validate
     * @returns boolean indicating if the token format is valid
     */
    isValidTokenFormat(token: string): boolean {
        if (!token || typeof token !== 'string') {
            return false;
        }

        // Basic format validation - reCAPTCHA tokens are typically long alphanumeric strings
        const tokenRegex = /^[A-Za-z0-9_-]+$/;
        return tokenRegex.test(token) && token.length > 20;
    }
} 