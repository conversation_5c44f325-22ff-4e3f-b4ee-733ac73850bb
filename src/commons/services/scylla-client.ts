import { Injectable, Logger, On<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, OnModuleInit } from "@nestjs/common";
import * as scylla from "cassandra-driver";
import { SCYLLA_DATA_CENTER_PARAM_NAME, SCYLLA_KEY_SPACE_PARAM_NAME, SCYLLA_NODE_IPS_PARAM_NAME } from "../../utils/confs";
import { ConfigService } from "@nestjs/config";

/**
 * Scylla Client Rules:
 * 
 * - Only use one Client instance per keyspace or use a single Client and explicitly specify the keyspace in your queries 
 * and reuse it in across your modules in the application lifetime.
 * - If you execute a statement more than once, use a prepared statement.
 * - In some situations you can reduce the number of network roundtrips and also have atomic operations by using batches.
*/
@Injectable()
export class ScyllaClient implements OnModuleInit, OnModuleDestroy{

    private readonly logger = new Logger(ScyllaClient.name);

    public scyllaCluster: scylla.Client;

    constructor(private configService: ConfigService) {
        this.logger.debug(`instantiated ${ScyllaClient.name}`);
    }

    async onModuleInit() {
        this.logger.debug(`configuring scyllaDB client...`);
        let scyllaNodeIp: string = this.configService.get<string>(SCYLLA_NODE_IPS_PARAM_NAME) || '';
        let scyllaKeySpace: string = this.configService.get<string>(SCYLLA_KEY_SPACE_PARAM_NAME) || '';
        let scyllaDC: string = this.configService.get<string>(SCYLLA_DATA_CENTER_PARAM_NAME) || '';

        let requestTracker = new scylla.tracker.RequestLogger({logNormalRequests:true });

        this.scyllaCluster = new scylla.Client({
            contactPoints: [scyllaNodeIp],
            localDataCenter: scyllaDC,
            keyspace: scyllaKeySpace,
            /*requestTracker:requestTracker,
            monitorReporting:{enabled:true},
            queryOptions:{logged:true, traceQuery:true},*/
        });
        
        await this.scyllaCluster.connect()
        this.logger.debug(`connected to scyllaDB:${scyllaNodeIp} keyspace:${scyllaKeySpace}.`);
    }

    async onModuleDestroy() {
        this.logger.debug(`shutting down scyllaDB client...`);
        if(this.scyllaCluster) {
            await this.scyllaCluster.shutdown()
        }
    }

    getClient():scylla.Client {
        return this.scyllaCluster;
    }
}