import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

/**
 * Service for providing standardized transaction configurations
 * This centralizes transaction settings to ensure consistency across services
 */
@Injectable()
export class TransactionConfigService {
  constructor(private readonly configService: ConfigService) {}
  
  /**
   * Get standard transaction configuration for most operations
   * @returns Transaction configuration with REQUIRED propagation and standard timeout
   */
  getStandardTransactionConfig() {
    return {
      propagation: 'REQUIRED'
    };
  }
  
  /**
   * Get transaction configuration for short operations
   * @returns Transaction configuration with REQUIRED propagation and short timeout
   */
  getShortTransactionConfig() {
    return {
      propagation: 'REQUIRED'
    };
  }
  
  /**
   * Get transaction configuration for long-running operations
   * @returns Transaction configuration with REQUIRED propagation and extended timeout
   */
  getLongTransactionConfig() {
    return {
      propagation: 'REQUIRED'
    };
  }
  
  /**
   * Get transaction configuration for operations that need a new transaction
   * @returns Transaction configuration with REQUIRES_NEW propagation
   */
  getRequiresNewTransactionConfig() {
    return {
      propagation: 'REQUIRES_NEW'
    };
  }
  
  /**
   * Get transaction configuration for operations that should not be part of a transaction
   * @returns Transaction configuration with NOT_SUPPORTED propagation
   */
  getNotSupportedTransactionConfig() {
    return {
      propagation: 'NOT_SUPPORTED'
    };
  }
  
  /**
   * Get transaction configuration for operations that can run with or without a transaction
   * @returns Transaction configuration with SUPPORTS propagation
   */
  getSupportsTransactionConfig() {
    return {
      propagation: 'SUPPORTS'
    };
  }
}