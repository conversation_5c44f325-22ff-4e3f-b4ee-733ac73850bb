import { Test, TestingModule } from '@nestjs/testing';
import { ConfigService } from '@nestjs/config';
import axios from 'axios';
import { RecaptchaService } from './recaptcha.service';
import { WPError } from '../error/wp.error';

// Mock axios
jest.mock('axios');
const mockedAxios = axios as jest.Mocked<typeof axios>;

describe('RecaptchaService', () => {
    let service: RecaptchaService;
    let configService: jest.Mocked<ConfigService>;

    const createTestingModule = async (configMock: any) => {
        const module: TestingModule = await Test.createTestingModule({
            providers: [
                RecaptchaService,
                {
                    provide: ConfigService,
                    useValue: {
                        get: jest.fn().mockImplementation(configMock),
                    },
                },
            ],
        }).compile();

        return module;
    };

    afterEach(() => {
        jest.clearAllMocks();
    });

    describe('verifyRecaptcha', () => {
        it('should return true when reCAPTCHA is disabled', async () => {
            const module = await createTestingModule((key: string) => {
                if (key === 'DISABLE_RECAPTCHA') return 'true';
                return undefined;
            });

            service = module.get<RecaptchaService>(RecaptchaService);
            const result = await service.verifyRecaptcha('any-token');
            expect(result).toBe(true);
        });

        it('should throw error when token is missing', async () => {
            const module = await createTestingModule((key: string) => {
                switch (key) {
                    case 'RECAPTCHA_SECRET_KEY':
                        return 'test-secret-key';
                    case 'DISABLE_RECAPTCHA':
                        return 'false';
                    default:
                        return undefined;
                }
            });

            service = module.get<RecaptchaService>(RecaptchaService);
            await expect(service.verifyRecaptcha('')).rejects.toThrow(WPError);
            await expect(service.verifyRecaptcha(null as any)).rejects.toThrow(WPError);
        });

        it('should throw error when secret key is not configured', async () => {
            const module = await createTestingModule((key: string) => {
                if (key === 'RECAPTCHA_SECRET_KEY') return '';
                if (key === 'DISABLE_RECAPTCHA') return 'false';
                return undefined;
            });

            service = module.get<RecaptchaService>(RecaptchaService);
            await expect(service.verifyRecaptcha('test-token')).rejects.toThrow(WPError);
        });

        it('should verify reCAPTCHA successfully', async () => {
            const module = await createTestingModule((key: string) => {
                switch (key) {
                    case 'RECAPTCHA_SECRET_KEY':
                        return 'test-secret-key';
                    case 'DISABLE_RECAPTCHA':
                        return 'false';
                    case 'RECAPTCHA_MIN_SCORE':
                        return 0.5;
                    default:
                        return undefined;
                }
            });

            const mockResponse = {
                data: {
                    success: true,
                    challenge_ts: '2023-01-01T00:00:00Z',
                    hostname: 'example.com',
                },
            };

            mockedAxios.post.mockResolvedValue(mockResponse);

            service = module.get<RecaptchaService>(RecaptchaService);
            const result = await service.verifyRecaptcha('valid-token', '***********');
            expect(result).toBe(true);
            expect(mockedAxios.post).toHaveBeenCalledWith(
                'https://www.google.com/recaptcha/api/siteverify',
                expect.any(URLSearchParams),
                expect.objectContaining({
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    timeout: 10000,
                })
            );
        });

        it('should handle reCAPTCHA verification failure', async () => {
            const module = await createTestingModule((key: string) => {
                switch (key) {
                    case 'RECAPTCHA_SECRET_KEY':
                        return 'test-secret-key';
                    case 'DISABLE_RECAPTCHA':
                        return 'false';
                    default:
                        return undefined;
                }
            });

            const mockResponse = {
                data: {
                    success: false,
                    'error-codes': ['invalid-input-response'],
                },
            };

            mockedAxios.post.mockResolvedValue(mockResponse);

            service = module.get<RecaptchaService>(RecaptchaService);
            await expect(service.verifyRecaptcha('invalid-token')).rejects.toThrow(WPError);
        });

        it('should handle reCAPTCHA v3 score validation', async () => {
            const module = await createTestingModule((key: string) => {
                switch (key) {
                    case 'RECAPTCHA_SECRET_KEY':
                        return 'test-secret-key';
                    case 'DISABLE_RECAPTCHA':
                        return 'false';
                    case 'RECAPTCHA_MIN_SCORE':
                        return 0.5;
                    default:
                        return undefined;
                }
            });

            const mockResponse = {
                data: {
                    success: true,
                    score: 0.3, // Below threshold
                },
            };

            mockedAxios.post.mockResolvedValue(mockResponse);

            service = module.get<RecaptchaService>(RecaptchaService);
            await expect(service.verifyRecaptcha('low-score-token')).rejects.toThrow(WPError);
        });

        it('should pass reCAPTCHA v3 score validation when score is above threshold', async () => {
            const module = await createTestingModule((key: string) => {
                switch (key) {
                    case 'RECAPTCHA_SECRET_KEY':
                        return 'test-secret-key';
                    case 'DISABLE_RECAPTCHA':
                        return 'false';
                    case 'RECAPTCHA_MIN_SCORE':
                        return 0.5;
                    default:
                        return undefined;
                }
            });

            const mockResponse = {
                data: {
                    success: true,
                    score: 0.8, // Above threshold
                },
            };

            mockedAxios.post.mockResolvedValue(mockResponse);

            service = module.get<RecaptchaService>(RecaptchaService);
            const result = await service.verifyRecaptcha('high-score-token');
            expect(result).toBe(true);
        });

        it('should handle network timeout', async () => {
            const module = await createTestingModule((key: string) => {
                switch (key) {
                    case 'RECAPTCHA_SECRET_KEY':
                        return 'test-secret-key';
                    case 'DISABLE_RECAPTCHA':
                        return 'false';
                    default:
                        return undefined;
                }
            });

            const timeoutError = new Error('timeout');
            timeoutError.name = 'AxiosError';
            (timeoutError as any).code = 'ECONNABORTED';
            (timeoutError as any).isAxiosError = true;

            mockedAxios.post.mockRejectedValue(timeoutError);
            mockedAxios.isAxiosError.mockReturnValue(true);

            service = module.get<RecaptchaService>(RecaptchaService);
            await expect(service.verifyRecaptcha('test-token')).rejects.toThrow(WPError);
        });

        it('should handle network errors', async () => {
            const module = await createTestingModule((key: string) => {
                switch (key) {
                    case 'RECAPTCHA_SECRET_KEY':
                        return 'test-secret-key';
                    case 'DISABLE_RECAPTCHA':
                        return 'false';
                    default:
                        return undefined;
                }
            });

            const networkError = new Error('Network Error');
            networkError.name = 'AxiosError';
            (networkError as any).isAxiosError = true;

            mockedAxios.post.mockRejectedValue(networkError);
            mockedAxios.isAxiosError.mockReturnValue(true);

            service = module.get<RecaptchaService>(RecaptchaService);
            await expect(service.verifyRecaptcha('test-token')).rejects.toThrow(WPError);
        });
    });

    describe('isValidTokenFormat', () => {
        beforeEach(async () => {
            const module = await createTestingModule((key: string) => {
                switch (key) {
                    case 'RECAPTCHA_SECRET_KEY':
                        return 'test-secret-key';
                    case 'DISABLE_RECAPTCHA':
                        return 'false';
                    default:
                        return undefined;
                }
            });
            service = module.get<RecaptchaService>(RecaptchaService);
        });

        it('should return true for valid token format', () => {
            const validToken = 'abcdefghijklmnopqrstuvwxyz1234567890';
            expect(service.isValidTokenFormat(validToken)).toBe(true);
        });

        it('should return false for invalid token format', () => {
            expect(service.isValidTokenFormat('')).toBe(false);
            expect(service.isValidTokenFormat('short')).toBe(false);
            expect(service.isValidTokenFormat('invalid@token')).toBe(false);
            expect(service.isValidTokenFormat(null as any)).toBe(false);
            expect(service.isValidTokenFormat(undefined as any)).toBe(false);
        });

        it('should return true for tokens with valid characters including hyphens and underscores', () => {
            const validToken = 'abc-def_ghi123456789012345';
            expect(service.isValidTokenFormat(validToken)).toBe(true);
        });
    });
}); 