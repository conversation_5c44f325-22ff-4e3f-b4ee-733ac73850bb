import { Injectable } from "@nestjs/common";
import { ConfigService } from "@nestjs/config";
import { WP_ERROR_CONFIG_NOT_FOUND } from "../error/error.codes";
import { WPError } from "../error/wp.error";

/**
 * BaseService class to provide common functionalities to other services.
 */
@Injectable()
export class BaseService {

    constructor(private readonly configSrv: ConfigService) {

    }

    /**
     * Get the configuration value from the environment variables.
     * @param param 
     * @param defaultValue 
     * @returns T
     */
    async getConfig<T>(param: string, defaultValue?:T): Promise<T> {
        let value:T | undefined;
        if(defaultValue != undefined) {
            value = this.configSrv.get(param, defaultValue);
        }
        else {
            value = this.configSrv.get(param);
            if(value == undefined || value == null || value == "") {
                throw new WPError(WP_ERROR_CONFIG_NOT_FOUND,`parameter:${param}`,undefined,WP_ERROR_CONFIG_NOT_FOUND.headerStatusCode+"");
            }
        }
        return value!;
    }
    
}