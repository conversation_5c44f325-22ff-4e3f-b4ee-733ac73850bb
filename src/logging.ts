import * as winston from "winston";
import * as util from "util";
import { LEVEL, SPLAT, MESSAGE } from "triple-beam";
import { WinstonModule } from "nest-winston";
import "winston-daily-rotate-file";
import * as dotenv from "dotenv";
import { ClsServiceManager } from "nestjs-cls";

export interface LoggerOptions {
    isProduction: boolean;
    logLevel: string;
    logStream: string;
    logFileStreamConf: {
        maxSize?: string | number;
        maxFiles?: string | number;
        logFilePath: string;
        filename: string;
    };
}

/** Turns a given timestamp or current Date to an ISO date string */
function getDateStr(timestamp?: number): string {
    return timestamp ? new Date(timestamp).toJSON() : new Date().toJSON();
}

/** Format function for logging in development */
const devLogFormat = winston.format.printf(({ level, message, label, context, timestamp, ...rest }) => {
    // The type signature in winston is wrong
    const { [LEVEL]: _lvl, [SPLAT]: _splt, [MESSAGE]: _msg, ...restNoSymbols } = rest as Record<string | symbol, any>;
    return Object.keys(restNoSymbols).length === 0
        ? `${getDateStr(typeof timestamp === 'number' ? timestamp : undefined)} ${ClsServiceManager.getClsService()?.getId() ?? ""} [${context ? context : label}] ${level}: ${message}`
        : `${getDateStr(typeof timestamp === 'number' ? timestamp : undefined)} ${ClsServiceManager.getClsService()?.getId() ?? ""} [${context ? context : label}] ${level}: ${message} ${util.inspect(restNoSymbols, false, 4, true)}`;
});

/** Create a winston logger from given options */
export function createLogger(logConf: LoggerOptions): winston.Logger {
    return winston.createLogger({
        level: logConf.logLevel,
        format: logConf.isProduction
            ? winston.format.json()
            : winston.format.combine(
                winston.format.uncolorize(),
                winston.format.splat(),
                winston.format.errors({ stack: true }),
                //winston.format.simple(),
                devLogFormat,
            ),
        transports: [
            logConf.logStream === "file"
                ? new winston.transports.DailyRotateFile({
                    dirname: logConf.logFileStreamConf.logFilePath,
                    datePattern: "YYYY-MM-DD-HH",
                    filename: `${logConf.logFileStreamConf.filename}-%DATE%.log`,
                    zippedArchive: true,
                    maxSize: logConf.logFileStreamConf.maxSize,
                    maxFiles: logConf.logFileStreamConf.maxFiles,
                    format: winston.format.uncolorize(),
                })
                : new winston.transports.Console(),
        ],
    });
}
// make sure that process.ENV loads .env file before nestjs
dotenv.config();
const logStream = process.env.LOG_STREAM ?? "console";
const logLevel = process.env.LOG_LEVEL ?? "info";
const isProd = (process.env.APP_ENV === "prod" || process.env.APP_ENV === "production");
const logFileName = "eazy-wallet.log";
const logDir = process.env.LOG_STREAM_FILE_DIR ?? `${__dirname}/logs`;
const logFileMaxSize = process.env.LOG_STREAM_FILE_MAX_SIZE ?? "10m";
const logFileMax = process.env.LOG_STREAM_FILE_MAX ?? undefined;

const logConf: LoggerOptions = {
    isProduction: isProd,
    logLevel: logLevel,
    logStream: logStream,
    logFileStreamConf: {
        maxFiles: logFileMax,
        maxSize: logFileMaxSize,
        logFilePath: `${logDir}`,
        filename: logFileName,
    },
};
console.log(`log conf: ${JSON.stringify(logConf)}`);

export const winstonLogger = createLogger(logConf);
export const winstonLoggerService = WinstonModule.createLogger(winstonLogger);
