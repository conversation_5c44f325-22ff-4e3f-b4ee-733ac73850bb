// Load environment variables first
require('dotenv').config();

require('newrelic');
import { SwaggerConfigLoader } from "./utils/swagger/swagget-config-loader";
SwaggerConfigLoader.initConfigs();
import { NestFactory } from "@nestjs/core";
import { AppModule } from "./app.module";
import { SwaggerModule, DocumentBuilder, SwaggerCustomOptions } from "@nestjs/swagger";
import { BadRequestException, INestApplication, NestApplicationOptions, ValidationError, ValidationPipe } from "@nestjs/common";
import { winstonLoggerService } from "./logging";
import { NODE_LOCAL_PORT_PARAM_NAME } from "./utils/confs";
import { ObservabilityInterceptor } from "./commons/o11y/observability.interceptor";
import { ServiceLevelMetricsSender } from "./commons/o11y/service.metric";
import { HttpExceptionFilter } from "./commons/error/wp.exception.filter";
import { AuthMiddlewareFactory } from "./commons/auth/auth-middleware.factory";
import { ConfigService } from "@nestjs/config";
import { SwaggerFactory } from "./utils/swagger/swagger.factory";
import { initSequelizeCLS } from "sequelize-transactional-decorator";


initSequelizeCLS();

export let apiGWApp: INestApplication;

/**
 * Bootstrap the application. 
 * It creates the Nest application and sets up:
 *  1. Loggger 
 *  2. CORS
 *  3. The global REST API prefix, 
 *  4. The global HttpExceptionFilter for handling all exceptions and retıning well formated JSON error payload properly, 
 *  5. The global ValidationPipe for validating all incoming REST requests,
 *  6. The global ObservabilityInterceptor for adding custom attributes to the request and transactions.
 *  7. The Swagger documentation for the API.
 */
async function bootstrap() {
    // read configs from the .env first
    SwaggerFactory.initConfigs();
    const appOptions: NestApplicationOptions = {
        cors: {
            origin: "*",
            allowedHeaders: "*",
            methods: "*",
            optionsSuccessStatus: 200,
            credentials: true,
            maxAge: 0
        },
        autoFlushLogs: true,
        snapshot: true,
        abortOnError: true,
        logger: winstonLoggerService,
    };
    apiGWApp = await NestFactory.create(AppModule, appOptions);

    const isDev = !process.env.NODE_ENV || process.env.NODE_ENV === "development";
    apiGWApp.enableShutdownHooks();
    apiGWApp.useGlobalFilters(new HttpExceptionFilter());
    apiGWApp.use(AuthMiddlewareFactory.newClsMiddlewareInstance().use);

    apiGWApp.useGlobalPipes(
        new ValidationPipe({
            exceptionFactory: (validationErrors: ValidationError[] = []) => {
                return new BadRequestException(validationErrors);
            },
            transform: true,
            transformOptions: {
                enableImplicitConversion: true,
                enableCircularCheck: true,
            },
            // Add this:
            //whitelist: true,
            //forbidNonWhitelisted: true,
        }),
    );

    apiGWApp.useGlobalInterceptors(new ObservabilityInterceptor(new ServiceLevelMetricsSender()));
    //apiGWApp.setGlobalPrefix("test");

    const swaggerGlobalconfigs = new DocumentBuilder()
        .setTitle("Easy Wallet")
        .setDescription("handles all external REST API calls.")
        .addBearerAuth()
        .build();

    const swaggerGlobalOptions = {
        customCss: `
              .topbar-wrapper img {content:url(\'https://s3.eu-central-1.amazonaws.com/static.eazywallet.io/general/eazywallet-logo.png\'); width:250px; height:auto;}
              .swagger-ui .topbar { background-color: white; }
              `,
    };

    const document = SwaggerModule.createDocument(apiGWApp, swaggerGlobalconfigs);
    SwaggerModule.setup("docs", apiGWApp, document, swaggerGlobalOptions);

    await SwaggerFactory.buidSwaggers(apiGWApp);

    const confService: ConfigService = await apiGWApp.get(ConfigService);
    const port: string | undefined = confService.get<string>(NODE_LOCAL_PORT_PARAM_NAME);
    if(!port) {
        throw new Error(`Could not find the configuration parameter: ${NODE_LOCAL_PORT_PARAM_NAME}`);
    }
    try {
        await apiGWApp.listen(Number.parseInt(port));

    } catch (error) {
        throw error;
    }
    winstonLoggerService.log(`Eazy Wallet successfully started on port:${port}`, 'NestApplication');

}



(async () => {
    try {
        await Promise.resolve(bootstrap());
    } catch (error) {
        if (winstonLoggerService) {
            winstonLoggerService.error("Could not start the eazy wallet app.", error);
            console.error("Could not start the eazy wallet app.", error);
        } else {
            console.error("Could not start the eazy wallet app.", error);
        }
        throw error;
    }
})();

