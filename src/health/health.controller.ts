/* eslint-disable @typescript-eslint/no-unused-vars */
import { Controller, Get } from "@nestjs/common";
import { HealthCheckService, HttpHealthIndicator, HealthCheck, MicroserviceHealthIndicator, HealthCheckResult } from "@nestjs/terminus";
import { Roles } from "../commons/auth/roles.decarator";
import { Role } from "../commons/auth/role.enum";

/*
* This api is not exposed to the public therefore no need to apply role based access
*/
@Controller("actuator/health")
export class HealthController {
    constructor(
        private health: HealthCheckService,
        private http: HttpHealthIndicator,
        private microserviceHealthIndicator: MicroserviceHealthIndicator,
    ) {}

    @Get("liveness")
    @HealthCheck()
    @Roles(Role.full_access)
    liveness() {
        return { status: "UP" };
    }

    @Get("readiness")
    @HealthCheck()
    @Roles(Role.admin)
    readiness() {
        return { 
            status: "ok",
         } as HealthCheckResult;
        
        /*this.health.check([
        ]);*/
    }
}
