import { MiddlewareConsumer, Module, RequestMethod } from '@nestjs/common';
import { HttpModule } from '@nestjs/axios';
import { HealthModule } from './health/health.module';
import { ClsModule } from 'nestjs-cls';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { APP_GUARD, RouterModule, Routes, RouteTree } from '@nestjs/core';
import { AuthGuard } from './commons/auth/auth-guard';
import { MongooseModule } from '@nestjs/mongoose';
import { RedisModule } from './redis/redis.module';
import { LoggerMiddleware } from './commons/wp.rest.logger';
import { SequelizeModule } from '@nestjs/sequelize';
import { SequelizeTransactionalModule } from "sequelize-transactional-decorator";
import { UserModule } from './iam/user/user.module';
import { AppController } from './app.controller';

export const APP_NAME = 'EW';

/**
 *  Main module of the application.
 *  It imports all the other modules and sets up :
 *   1. AuthGuard as the global guards. see commons/auth/auth-guard.ts
 *   2. RouterModule to define the routes of the application. It adds the v1 prefix to the routes. 
 *      When it is combimed with setGlobalPrefix("apigw/api") in the main.ts, the final route will be apigw/api/v1/...
 */
@Module({
  imports: [
    ClsModule.forRoot({ global: true }),
    ConfigModule.forRoot({ isGlobal: true }),
    HttpModule,
    HealthModule,
    UserModule,
    RedisModule,
    /*MongooseModule.forRootAsync({
      useFactory: async (configService: ConfigService) => {
        const isLocal = configService.get('MONGODB_ENV') === 'local';
        const host = configService.get('MONGODB_HOST');
        const port = configService.get('MONGODB_PORT');
        const database = configService.get('MONGODB_DATABASE');
        const maxPoolSize = parseInt(configService.get('MONGODB_MAX_POOL_SIZE', '10'), 10);
        const retryWrites = configService.get('MONGODB_RETRY_WRITES') === 'true';
        // Sets the write concern as majority
        const w = configService.get('MONGODB_W');

        let uri: string;
        let options: any = {
          useNewUrlParser: true,
          useUnifiedTopology: true,
          maxPoolSize,
          retryWrites,
          w,
        };

        if (isLocal) {
          uri = `mongodb://${host}:${port}/${database}`;
        } else {
          const user = configService.get('MONGODB_USER');
          const pass = configService.get('MONGODB_PASSWORD');
          const ssl = false;
          uri = `mongodb://${user}:${pass}@${host}:${port}/${database}`;
          options.ssl = ssl;
        }

        return {
          uri,
          ...options,
          authSource: 'admin',
        };
      },
      inject: [ConfigService],
    }),*/
    SequelizeModule.forRootAsync({
      useFactory: async () => {
        const isNew = process.env.MYSQLDB_RECREATE_DB_SCHEMA && process.env.MYSQLDB_RECREATE_DB_SCHEMA.toLowerCase() === "true";
        return {
          dialect: "mysql",
          host: String(process.env.MYSQLDB_HOST),
          port: Number(process.env.MYSQLDB_LOCAL_PORT),
          username: String(process.env.MYSQLDB_WODO_USER),
          password: String(process.env.MYSQLDB_WODO_PASSWORD),
          database: String(process.env.MYSQLDB_WODO_DATABASE),
          autoLoadModels: true,
          synchronize: !!isNew,
          //logging: console.log,
          benchmark: true,
          logQueryParameters: true,
          sync: { force: !!isNew },
          pool: {
            max: parseInt(process.env.MYSQLDB_CONNECTION_POOL_MAX ?? "10"),
            min: parseInt(process.env.MYSQLDB_CONNECTION_POOL_MIN ?? "1"),
            acquire: parseInt(process.env.MYSQLDB_CONNECTION_POOL_ACQUIRE ?? "30000"),
            idle: parseInt(process.env.MYSQLDB_CONNECTION_POOL_IDLE ?? "10000"),
          },
        };
      },
    }),
    SequelizeTransactionalModule.register(),
    RouterModule.register([

    ] as Routes),
  ],
  controllers: [AppController],
  providers: [
    {
      provide: APP_GUARD,
      useClass: AuthGuard,
    }
  ],
})
export class AppModule {
  configure(consumer: MiddlewareConsumer) {
    consumer.apply(LoggerMiddleware).forRoutes({ path: "*", method: RequestMethod.ALL });
  }
}
