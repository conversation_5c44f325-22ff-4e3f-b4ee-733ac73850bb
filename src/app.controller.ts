import { Controller, Get } from "@nestjs/common";
import { ApiOperation, ApiResponse, ApiTags } from "@nestjs/swagger";
import { Roles } from "./commons/auth/roles.decarator";
import { Role } from "./commons/auth/role.enum";

@ApiTags('')
@Controller()
export class AppController {

    @Get()
    @ApiOperation({ summary: "Get the hello world message" })
    @ApiResponse({ status: 200, description: "Returns the hello world message" })
    @ApiResponse({ status: 403, description: "Forbidden." })
    @ApiResponse({ status: 404, description: "Not Found." })
    @ApiResponse({ status: 500, description: "Internal Server Error." })
    @ApiResponse({ status: 503, description: "Service Unavailable." })
    @Roles(Role.full_access)
    getHello(): string {
        return "Hello World from Eazy Wallet!";
    }
}