export interface ConfPair {
    paramName: string,
    defaultValue: string,
    isGlobal: boolean,
}
export const DEFAULT_PAGE_RECORD_COUNT = "15";

export const IDEMPOTENT_API_RECORD_PAGE_PARAM_NAME = "IDEMPOTENT_API_RECORD_PAGE";
export const IDEMPOTENT_API_RECORD_PAGE_DAFULT_VALUE = "15";
export const IDEMPOTENT_API_RECORD_PAGE_CONF: ConfPair = {
    paramName: IDEMPOTENT_API_RECORD_PAGE_PARAM_NAME,
    defaultValue: IDEMPOTENT_API_RECORD_PAGE_DAFULT_VALUE,
    isGlobal: true,
};

export const IDEMPOTENT_API_RECORD_REPOSITORY_PARAM_NAME = "IDEMPOTENT_API_RECORD_REPOSITORY";
export const IDEMPOTENT_API_RECORD_REPOSITORY_DAFULT_VALUE = "mongo";
export const IDEMPOTENT_API_RECORD_REPOSITORY_MONGO = "mongo";
export const IDEMPOTENT_API_RECORD_REPOSITORY_SCYLLA = "scylla";
export const IDEMPOTENT_API_RECORD_REPOSITORY_REDIS = "redis";
export const IDEMPOTENT_API_RECORD_REPOSITORY_CONF: ConfPair = {
    paramName: IDEMPOTENT_API_RECORD_REPOSITORY_PARAM_NAME,
    defaultValue: IDEMPOTENT_API_RECORD_REPOSITORY_DAFULT_VALUE,
    isGlobal: true,
};


export const IDEMPOTENCY_CACHE_EXPIRE_TIME_PARAM_NAME = "IDEMPOTENCY_CACHE_EXPIRE_TIME";
export const IDEMPOTENCY_CACHE_EXPIRE_TIME_DAFULT_VALUE = (new String(1000 * 60 * 60 * 24)).toString(); // 24 hours
export const IDEMPOTENCY_CACHE_EXPIRE_TIME_CONF: ConfPair = {
    paramName:IDEMPOTENCY_CACHE_EXPIRE_TIME_PARAM_NAME,
    defaultValue:IDEMPOTENCY_CACHE_EXPIRE_TIME_DAFULT_VALUE,
    isGlobal: true,
};

export const IDEMPOTENCY_CACHE_KEY_PREFIX_PARAM_NAME = "IDEMPOTENCY_CACHE_KEY_PREFIX";
export const IDEMPOTENCY_CACHE_KEY_PREFIX_DAFULT_VALUE = "ipk-"
export const IDEMPOTENCY_CACHE_KEY_PREFIX_CONF: ConfPair = {
    paramName:IDEMPOTENCY_CACHE_KEY_PREFIX_PARAM_NAME,
    defaultValue:IDEMPOTENCY_CACHE_KEY_PREFIX_DAFULT_VALUE,
    isGlobal: true,
};



export const NODE_LOCAL_PORT_PARAM_NAME = "NODE_LOCAL_PORT";
export const NODE_LOCAL_PORT_DAFULT_VALUE = "3017";
export const NODE_LOCAL_PORT_CONF: ConfPair = {
    paramName: NODE_LOCAL_PORT_PARAM_NAME,
    defaultValue: NODE_LOCAL_PORT_DAFULT_VALUE,
    isGlobal: true,
};

export const AWS_COGNITO_CLIENT_ID_PARAM_NAME = "AWS_COGNITO_CLIENT_ID";
export const AWS_COGNITO_CLIENT_ID_DEFAULT_VALUE = undefined;
export const AWS_COGNITO_CLIENT_ID_CONF: ConfPair = {
    paramName: AWS_COGNITO_CLIENT_ID_PARAM_NAME,
    defaultValue: AWS_COGNITO_CLIENT_ID_DEFAULT_VALUE!,
    isGlobal: true,
};

export const AWS_COGNITO_CLIENT_SECRET_PARAM_NAME = "AWS_COGNITO_CLIENT_SECRET";
export const AWS_COGNITO_CLIENT_SECRET_DEFAULT_VALUE = undefined;
export const AWS_COGNITO_CLIENT_SECRET_CONF: ConfPair = {
    paramName: AWS_COGNITO_CLIENT_SECRET_PARAM_NAME,
    defaultValue: AWS_COGNITO_CLIENT_SECRET_DEFAULT_VALUE!,
    isGlobal: true,
};

export const AWS_COGNITO_USER_POOL_ID_PARAM_NAME = "AWS_COGNITO_USER_POOL_ID";
export const AWS_COGNITO_USER_POOL_ID_DEFAULT_VALUE = undefined;
export const AWS_COGNITO_USER_POOL_ID_CONF: ConfPair = {
    paramName: AWS_COGNITO_USER_POOL_ID_PARAM_NAME,
    defaultValue: AWS_COGNITO_USER_POOL_ID_DEFAULT_VALUE!,
    isGlobal: true,
};


export const AWS_APIGW_AUTH_LAMBDA_NAME_PARAM_NAME = "AWS_APIGW_AUTH_LAMBDA_NAME";
export const AWS_APIGW_AUTH_LAMBDA_NAME_DAFULT_VALUE = undefined;
export const AWS_APIGW_AUTH_LAMBDA_NAME_CONF: ConfPair = {
    paramName: AWS_APIGW_AUTH_LAMBDA_NAME_PARAM_NAME,
    defaultValue: AWS_APIGW_AUTH_LAMBDA_NAME_DAFULT_VALUE!,
    isGlobal: true,
};

export const AWS_APIGW_AUTH_LAMBDA_AUTHORIZER_URI_PARAM_NAME = "AWS_APIGW_AUTH_LAMBDA_AUTHORIZER_URI";
export const AWS_APIGW_AUTH_LAMBDA_AUTHORIZER_URI_DAFULT_VALUE = undefined;
export const AWS_APIGW_AUTH_LAMBDA_AUTHORIZER_URI_CONF: ConfPair = {
    paramName: AWS_APIGW_AUTH_LAMBDA_AUTHORIZER_URI_PARAM_NAME,
    defaultValue: AWS_APIGW_AUTH_LAMBDA_AUTHORIZER_URI_DAFULT_VALUE!,
    isGlobal: true,
};

export const AWS_APIGW_AUTH_LAMBDA_AUTHORIZER_TTL_SECONDS_PARAM_NAME = "AWS_APIGW_AUTH_LAMBDA_AUTHORIZER_TTL_SECONDS";
export const AWS_APIGW_AUTH_LAMBDA_AUTHORIZER_TTL_SECONDS_DAFULT_VALUE = "300";
export const AWS_APIGW_AUTH_LAMBDA_AUTHORIZER_TTL_SECONDS_CONF: ConfPair = {
    paramName: AWS_APIGW_AUTH_LAMBDA_AUTHORIZER_TTL_SECONDS_PARAM_NAME,
    defaultValue: AWS_APIGW_AUTH_LAMBDA_AUTHORIZER_TTL_SECONDS_DAFULT_VALUE,
    isGlobal: true,
};


export const WALLET_SERVICE_URL_PARAM_NAME = "WALLET_SERVICE_URL";
export const WALLET_SERVICE_URL_DAFULT_VALUE = "";
export const WALLET_SERVICE_URL_CONF: ConfPair = {
    paramName: WALLET_SERVICE_URL_PARAM_NAME,
    defaultValue: WALLET_SERVICE_URL_DAFULT_VALUE,
    isGlobal: true,
};


export const AUDIT_LOG_PER_PAGE_PARAM_NAME = "AUDIT_LOG_PER_PAGE";
export const AUDIT_LOG_PER_PAGE_DAFULT_VALUE = "15";
export const AUDIT_LOG_PER_PAGE_CONF: ConfPair = {
    paramName: AUDIT_LOG_PER_PAGE_PARAM_NAME,
    defaultValue: AUDIT_LOG_PER_PAGE_DAFULT_VALUE,
    isGlobal: true,
};

export const AUDIT_LOGGER_REPOSITORY_PARAM_NAME = "AUDIT_LOGGER_REPOSITORY";
export const AUDIT_LOGGER_REPOSITORY_DAFULT_VALUE = "mongo";
export const AUDIT_LOGGER_REPOSITORY_MONGO = "mongo";
export const AUDIT_LOGGER_REPOSITORY_SCYLLA = "scylla";
export const AUDIT_LOGGER_REPOSITORY_CONF: ConfPair = {
    paramName: AUDIT_LOGGER_REPOSITORY_PARAM_NAME,
    defaultValue: AUDIT_LOGGER_REPOSITORY_DAFULT_VALUE,
    isGlobal: true,
};


export const DISTRIBUTED_TRACE_PER_PAGE_PARAM_NAME = "DISTRIBUTED_TRACE_PER_PAGE";
export const DISTRIBUTED_TRACE_PER_PAGE_DAFULT_VALUE = "50";
export const DISTRIBUTED_TRACE_PER_PAGE_CONF: ConfPair = {
    paramName: DISTRIBUTED_TRACE_PER_PAGE_PARAM_NAME,
    defaultValue: DISTRIBUTED_TRACE_PER_PAGE_DAFULT_VALUE,
    isGlobal: true,
};

export const DISTRIBUTED_TRACE_SUMMARY_PER_PAGE_PARAM_NAME = "DISTRIBUTED_TRACE_SUMMARY_PER_PAGE";
export const DISTRIBUTED_TRACE_SUMMARY_PER_PAGE_DAFULT_VALUE = "50";
export const DISTRIBUTED_TRACE_SUMMARY_SUMMARY_PER_PAGE_CONF: ConfPair = {
    paramName: DISTRIBUTED_TRACE_PER_PAGE_PARAM_NAME,
    defaultValue: DISTRIBUTED_TRACE_SUMMARY_PER_PAGE_DAFULT_VALUE,
    isGlobal: true,
};

export const ENTITY_PER_PAGE_PARAM_NAME = "ENTITY_PER_PAGE";
export const ENTITY_PER_PAGE_DAFULT_VALUE = "150";
export const ENTITY_PER_PAGE_CONF: ConfPair = {
    paramName: ENTITY_PER_PAGE_PARAM_NAME,
    defaultValue: ENTITY_PER_PAGE_DAFULT_VALUE,
    isGlobal: true,
};

// SCYLLA DB

export const SCYLLA_NODE_IPS_PARAM_NAME = "SCYLLA_NODE_IPS";
export const SCYLLA_NODE_IPS_DAFULT_VALUE = "localhost";
export const SCYLLA_NODE_IPS_CONF: ConfPair = {
    paramName: SCYLLA_NODE_IPS_PARAM_NAME,
    defaultValue: SCYLLA_NODE_IPS_DAFULT_VALUE,
    isGlobal: true,
};

export const SCYLLA_DATA_CENTER_PARAM_NAME = "SCYLLA_DATA_CENTER";
export const SCYLLA_DATA_CENTER_DAFULT_VALUE = "DC1";
export const SCYLLA_DATA_CENTER_CONF: ConfPair = {
    paramName: SCYLLA_DATA_CENTER_PARAM_NAME,
    defaultValue: SCYLLA_DATA_CENTER_DAFULT_VALUE,
    isGlobal: true,
};

export const SCYLLA_USERNAME_PARAM_NAME = "SCYLLA_USERNAME";
export const SCYLLA_USERNAME_DAFULT_VALUE = "";
export const SCYLLA_USERNAME_CONF: ConfPair = {
    paramName: SCYLLA_USERNAME_PARAM_NAME,
    defaultValue: SCYLLA_USERNAME_DAFULT_VALUE,
    isGlobal: true,
};

export const SCYLLA_PASSWORD_PARAM_NAME = "SCYLLA_PASSWORD";
export const SCYLLA_PASSWORD_DAFULT_VALUE = "";
export const SCYLLA_PASSWORD_CONF: ConfPair = {
    paramName: SCYLLA_PASSWORD_PARAM_NAME,
    defaultValue: SCYLLA_PASSWORD_DAFULT_VALUE,
    isGlobal: true,
};

export const SCYLLA_KEY_SPACE_PARAM_NAME = "SCYLLA_KEY_SPACE";
export const SCYLLA_KEY_SPACE_DAFULT_VALUE = "";
export const SCYLLA_KEY_SPACE_CONF: ConfPair = {
    paramName: SCYLLA_KEY_SPACE_PARAM_NAME,
    defaultValue: SCYLLA_KEY_SPACE_DAFULT_VALUE,
    isGlobal: true,
};


export const REDIS_HOST_PARAM_NAME = "REDIS_HOST";
export const REDIS_HOST_DEFAULT_VALUE = "localhost";
export const REDIS_HOST_CONF: ConfPair = {
    paramName: REDIS_HOST_PARAM_NAME,
    defaultValue: REDIS_HOST_DEFAULT_VALUE,
    isGlobal: true,
};

export const REDIS_PORT_PARAM_NAME = "REDIS_PORT";
export const REDIS_PORT_DEFAULT_VALUE = "6379";
export const REDIS_PORT_CONF: ConfPair = {
    paramName: REDIS_PORT_PARAM_NAME,
    defaultValue: REDIS_PORT_DEFAULT_VALUE,
    isGlobal: true,
};

export const REDIS_USERNAME_PARAM_NAME = "REDIS_USERNAME";
export const REDIS_USERNAME_DEFAULT_VALUE = undefined;
export const REDIS_USERNAME_CONF: ConfPair = {
    paramName: REDIS_USERNAME_PARAM_NAME,
    defaultValue: REDIS_USERNAME_DEFAULT_VALUE!,
    isGlobal: true,
};

export const REDIS_PASSWORD_PARAM_NAME = "REDIS_PASSWORD";
export const REDIS_PASSWORD_DEFAULT_VALUE = undefined;
export const REDIS_PASSWORD_CONF: ConfPair = {
    paramName: REDIS_PASSWORD_PARAM_NAME,
    defaultValue: REDIS_PASSWORD_DEFAULT_VALUE!,
    isGlobal: true,
};


export const REDIS_CLUSTER_ENABLED_PARAM_NAME = "REDIS_CLUSTER_ENABLED";
export const REDIS_CLUSTER_ENABLED_DEFAULT_VALUE = "false";
export const REDIS_CLUSTER_ENABLED_CONF: ConfPair = {
    paramName: REDIS_CLUSTER_ENABLED_PARAM_NAME,
    defaultValue: REDIS_CLUSTER_ENABLED_DEFAULT_VALUE,
    isGlobal: true,
};


/**
 * How long the client will wait before killing a socket due to inactivity during initial connection.
 */
export const REDIS_CONNECT_TIME_OUT_PARAM_NAME = "REDIS_CONNECT_TIME_OUT";
export const REDIS_CONNECT_TIME_OUT_DEFAULT_VALUE = "10000";
export const REDIS_CONNECT_TIME_OUT_CONF: ConfPair = {
    paramName: REDIS_CONNECT_TIME_OUT_PARAM_NAME,
    defaultValue: REDIS_CONNECT_TIME_OUT_DEFAULT_VALUE,
    isGlobal: true,
};

/**
 * If a command does not return a reply within a set number of milliseconds, a "Command timed out" error will be thrown.
 */
export const REDIS_COMMAND_TIME_OUT_PARAM_NAME = "REDIS_COMMAND_TIME_OUT";
export const REDIS_COMMAND_TIME_OUT_DEFAULT_VALUE = "10000";
export const REDIS_COMMAND_TIME_OUT_CONF: ConfPair = {
    paramName: REDIS_COMMAND_TIME_OUT_PARAM_NAME,
    defaultValue: REDIS_COMMAND_TIME_OUT_DEFAULT_VALUE,
    isGlobal: true,
};


export const REDIS_NAT_MAP_PARAM_NAME = "REDIS_NAT_MAP";
export const REDIS_NAT_MAP_DEFAULT_VALUE = undefined;
export const REDIS_NAT_MAP_CONF: ConfPair = {
    paramName: REDIS_NAT_MAP_PARAM_NAME,
    defaultValue: REDIS_NAT_MAP_DEFAULT_VALUE!,
    isGlobal: true,
};


export const NEW_RELIC_API_BASE_URL_PARAM_NAME = "NEW_RELIC_API_BASE_URL";
export const NEW_RELIC_API_BASE_URL_DEFAULT_VALUE = "";
export const NEW_RELIC_API_BASE_URL_CONF: ConfPair = {
    paramName: NEW_RELIC_API_BASE_URL_PARAM_NAME,
    defaultValue: NEW_RELIC_API_BASE_URL_DEFAULT_VALUE,
    isGlobal: false,
};

export const NEW_RELIC_GRAPHQL_API_URL_PARAM_NAME = "NEW_RELIC_GRAPHQL_API_URL";
export const NEW_RELIC_GRAPHQL_API_URL_DEFAULT_VALUE = "";
export const NEW_RELIC_GRAPHQL_API_URL_CONF: ConfPair = {
    paramName: NEW_RELIC_GRAPHQL_API_URL_PARAM_NAME,
    defaultValue: NEW_RELIC_GRAPHQL_API_URL_DEFAULT_VALUE,
    isGlobal: false,
};



export const NEW_RELIC_API_KEY_PARAM_NAME = "NEW_RELIC_API_KEY";
export const NEW_RELIC_API_KEY_DEFAULT_VALUE = "";
export const NEW_RELIC_API_KEY_CONF: ConfPair = {
    paramName: NEW_RELIC_API_KEY_PARAM_NAME,
    defaultValue: NEW_RELIC_API_KEY_DEFAULT_VALUE,
    isGlobal: false,
};

export const NEW_RELIC_API_APPLICATION_ID_PARAM_NAME = "NEW_RELIC_API_APPLICATION_ID";
export const NEW_RELIC_API_APPLICATION_ID_DEFAULT_VALUE = "";
export const NEW_RELIC_API_APPLICATION_ID_CONF: ConfPair = {
    paramName: NEW_RELIC_API_APPLICATION_ID_PARAM_NAME,
    defaultValue: NEW_RELIC_API_APPLICATION_ID_DEFAULT_VALUE,
    isGlobal: false,
};

export const NEW_RELIC_ACCOUNT_ID_PARAM_NAME = "NEW_RELIC_ACCOUNT_ID";
export const NEW_RELIC_ACCOUNT_ID_DEFAULT_VALUE = "";
export const NEW_RELIC_ACCOUNT_ID_CONF: ConfPair = {
    paramName: NEW_RELIC_ACCOUNT_ID_PARAM_NAME,
    defaultValue: NEW_RELIC_ACCOUNT_ID_DEFAULT_VALUE,
    isGlobal: false,
};
export const AWS_ACCESS_KEY_ID_PARAM_NAME = "AWS_ACCESS_KEY_ID";
export const AWS_ACCESS_KEY_ID_DAFULT_VALUE = "";
export const AWS_ACCESS_KEY_ID_CONF: ConfPair = {
    paramName: AWS_ACCESS_KEY_ID_PARAM_NAME,
    defaultValue: AWS_ACCESS_KEY_ID_DAFULT_VALUE,
    isGlobal: false,
};

export const AWS_SECRET_ACCESS_KEY_PARAM_NAME = "AWS_SECRET_ACCESS_KEY";
export const AWS_SECRET_ACCESS_KEY_DAFULT_VALUE = "";
export const AWS_SECRET_ACCESS_KEY_CONF: ConfPair = {
    paramName: AWS_SECRET_ACCESS_KEY_PARAM_NAME,
    defaultValue: AWS_SECRET_ACCESS_KEY_DAFULT_VALUE,
    isGlobal: false,
};

export const AWS_USER_PROFILE_BUCKET_NAME_PARAM_NAME = "AWS_USER_PROFILE_BUCKET_NAME";
export const AWS_USER_PROFILE_BUCKET_NAME_DAFULT_VALUE = "";
export const AWS_USER_PROFILE_BUCKET_NAME_CONF: ConfPair = {
    paramName: AWS_USER_PROFILE_BUCKET_NAME_PARAM_NAME,
    defaultValue: AWS_USER_PROFILE_BUCKET_NAME_DAFULT_VALUE,
    isGlobal: false,
};
export const AWS_REGION_PARAM_NAME = "AWS_REGION";
export const AWS_REGION_DAFULT_VALUE = "eu-central-1";
export const AWS_REGION_CONF: ConfPair = {
    paramName: AWS_REGION_PARAM_NAME,
    defaultValue: AWS_REGION_DAFULT_VALUE,
    isGlobal: false,
};
export const SHUFTIPRO_API_URL_PARAM_NAME = "SHUFTIPRO_API_URL";
export const SHUFTIPRO_API_URL_DEFAULT_VALUE = "";
export const SHUFTIPRO_API_URL_CONF: ConfPair = {
    paramName: SHUFTIPRO_API_URL_PARAM_NAME,
    defaultValue: SHUFTIPRO_API_URL_DEFAULT_VALUE,
    isGlobal: false,
};
export const WALLET_SERVICE_API_URL_PARAM_NAME = "WALLET_SERVICE_API_URL";
export const WALLET_SERVICE_API_URL_DEFAULT_VALUE = "";
export const WALLET_SERVICE_API_URL_CONF: ConfPair = {
    paramName: WALLET_SERVICE_API_URL_PARAM_NAME,
    defaultValue: WALLET_SERVICE_API_URL_DEFAULT_VALUE,
    isGlobal: false,
};
export const WALLET_MANAGER_API_URL_PARAM_NAME = "WALLET_MANAGER_API_URL";
export const WALLET_MANAGER_API_URL_DEFAULT_VALUE = "";
export const WALLET_MANAGER_API_URL_CONF: ConfPair = {
    paramName: WALLET_MANAGER_API_URL_PARAM_NAME,
    defaultValue: WALLET_MANAGER_API_URL_DEFAULT_VALUE,
    isGlobal: false,
};
export const AWS_USER_PROFILE_BUCKET_FOLDER_PARAM_NAME = "AWS_USER_PROFILE_BUCKET_FOLDER";
export const AWS_USER_PROFILE_BUCKET_FOLDER_DAFULT_VALUE = "";
export const AWS_USER_PROFILE_BUCKET_FOLDER_CONF: ConfPair = {
    paramName: AWS_USER_PROFILE_BUCKET_FOLDER_PARAM_NAME,
    defaultValue: AWS_USER_PROFILE_BUCKET_FOLDER_DAFULT_VALUE,
    isGlobal: false,
};
export const AWS_CONTACT_FORM_BUCKET_FOLDER_PARAM_NAME = "AWS_CONTACT_FORM_BUCKET_FOLDER";
export const AWS_CONTACT_FORM_BUCKET_FOLDER_DAFULT_VALUE = "";
export const AWS_CONTACT_FORM_BUCKET_FOLDER_CONF: ConfPair = {
    paramName: AWS_CONTACT_FORM_BUCKET_FOLDER_PARAM_NAME,
    defaultValue: AWS_CONTACT_FORM_BUCKET_FOLDER_DAFULT_VALUE,
    isGlobal: false,
};

export const USER_PER_PAGE_PARAM_NAME = `USER_PER_PAGE`;
export const USER_PAGE_RECORD_COUNT: string = DEFAULT_PAGE_RECORD_COUNT;
export const USER_PER_PAGE_CONF: ConfPair = {
    paramName: USER_PER_PAGE_PARAM_NAME,
    defaultValue: USER_PAGE_RECORD_COUNT,
    isGlobal: false,
};

export const ALL_CONF_PAIRS: ConfPair[] = [
    NODE_LOCAL_PORT_CONF,
    IDEMPOTENT_API_RECORD_PAGE_CONF,
    IDEMPOTENT_API_RECORD_REPOSITORY_CONF,
    IDEMPOTENCY_CACHE_EXPIRE_TIME_CONF,
    IDEMPOTENCY_CACHE_KEY_PREFIX_CONF,
    WALLET_SERVICE_URL_CONF,
    AUDIT_LOG_PER_PAGE_CONF,
    AUDIT_LOGGER_REPOSITORY_CONF,
    SCYLLA_NODE_IPS_CONF,
    SCYLLA_DATA_CENTER_CONF,
    SCYLLA_USERNAME_CONF,
    SCYLLA_PASSWORD_CONF,
    SCYLLA_KEY_SPACE_CONF,
    REDIS_HOST_CONF,
    REDIS_PORT_CONF,
    REDIS_USERNAME_CONF,
    REDIS_PASSWORD_CONF,
    REDIS_CLUSTER_ENABLED_CONF,
    REDIS_CONNECT_TIME_OUT_CONF,
    REDIS_COMMAND_TIME_OUT_CONF,
    REDIS_NAT_MAP_CONF,
    AWS_APIGW_AUTH_LAMBDA_NAME_CONF,
    AWS_APIGW_AUTH_LAMBDA_AUTHORIZER_URI_CONF,
    AWS_APIGW_AUTH_LAMBDA_AUTHORIZER_TTL_SECONDS_CONF,
    NEW_RELIC_API_KEY_CONF,
    NEW_RELIC_API_BASE_URL_CONF,
    NEW_RELIC_GRAPHQL_API_URL_CONF,
    NEW_RELIC_API_APPLICATION_ID_CONF,
    NEW_RELIC_ACCOUNT_ID_CONF,
    DISTRIBUTED_TRACE_PER_PAGE_CONF,
    DISTRIBUTED_TRACE_SUMMARY_SUMMARY_PER_PAGE_CONF,
    ENTITY_PER_PAGE_CONF,
    AWS_COGNITO_CLIENT_ID_CONF,
    AWS_COGNITO_CLIENT_SECRET_CONF,
    AWS_COGNITO_USER_POOL_ID_CONF,
    AWS_ACCESS_KEY_ID_CONF,
    AWS_SECRET_ACCESS_KEY_CONF,
    AWS_USER_PROFILE_BUCKET_NAME_CONF,
    AWS_REGION_CONF,
    SHUFTIPRO_API_URL_CONF,
    WALLET_SERVICE_API_URL_CONF,
    WALLET_MANAGER_API_URL_CONF,
    AWS_USER_PROFILE_BUCKET_FOLDER_CONF,
    AWS_CONTACT_FORM_BUCKET_FOLDER_CONF,
    AWS_USER_PROFILE_BUCKET_NAME_CONF,
    USER_PER_PAGE_CONF,
];