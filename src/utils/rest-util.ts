import { UserContext } from "../commons/auth/user-context";
import { AuthKeys } from "../commons/auth/auth-constnats";
import { FilterQueryParams } from "../commons/filter-query-param.dto";
import { WP_USER_ID_NOT_FOUND } from "../commons/error/error.codes";
import { WPError } from "../commons/error/wp.error";

export class RESTUtil {
    /**
     * Resolves required haeadrs in the header object provided by the nestjs and then
     * creates `UserContext` object
     * @param headers
     */
    public static resolveUserContex(headers: any): UserContext {
        const userId = String(headers[AuthKeys.user_id]);
        const user_groups: string[] = headers[AuthKeys.user_groups]?.split(",");
        const isKycCompleted = Boolean(headers["is_kyc_completed"] === "true");
        const kycEnabled = Boolean(headers["kyc_enabled"] === "true");
        const email = String(headers["email"]);

        const uc: UserContext = new UserContext(userId, user_groups, isKycCompleted, kycEnabled, email);
        uc.username = String(headers["username"]);
        return uc;
    }

    public static validateUserContext(userContext: UserContext): void {

        if (!userContext.userId || userContext.userId == "") throw new WPError(WP_USER_ID_NOT_FOUND, undefined, JSON.stringify(userContext));
    }

    public static async paginationRBAC(userContext: UserContext, queryParamsFilterDto: FilterQueryParams, allowAll: boolean = false) {
        queryParamsFilterDto.userId = queryParamsFilterDto.userId ?? (allowAll ? undefined : userContext.userId);
    }
}
