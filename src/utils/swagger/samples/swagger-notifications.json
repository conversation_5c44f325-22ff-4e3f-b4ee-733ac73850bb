{"openapi": "3.0.0", "paths": {"/v1/notifications": {"get": {"description": "Finds notifications by the given filter parameters.", "operationId": "NotificationController_findByFilter", "parameters": [], "responses": {"200": {"description": "Returns the notifications found by the given parameters.", "headers": {"Access-Control-Allow-Origin": {"schema": {"type": "string"}}}, "content": {"application/json": {"schema": {"type": "object", "properties": {"page": {"type": "number"}, "limit": {"type": "number"}, "totalItems": {"type": "number"}, "totalPages": {"type": "number"}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/NotificationDto"}}, "links": {"type": "object", "$ref": "#/components/schemas/PaginationLinkDto"}, "meta": {"type": "object", "$ref": "#/components/schemas/PaginationMetaDto"}}}}}}, "400": {"description": "Bad request. The server cannot or will not process the request due to something that is perceived to be a client error (for example, malformed request syntax, invalid request message framing, or deceptive request routing).", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WPExceptionMessage"}}}}, "403": {"description": "Forbidden.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WPExceptionMessage"}}}}, "500": {"description": "An internal error occurred while finding the notifications by the filter parameters.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WPExceptionMessage"}}}}}, "security": [{"api_key": []}, {"ew-iam-app-lambda-auth-jwt-cognito": []}], "summary": "List notifications", "tags": ["notifications"], "x-amazon-apigateway-integration": {"httpMethod": "GET", "uri": "http://ec2-3-***********.eu-central-1.compute.amazonaws.com:8107/v1/notifications", "responses": {"default": {"statusCode": "200", "responseParameters": {"method.response.header.Access-Control-Allow-Origin": "'*'"}}}, "requestTemplates": {"application/json": "#set($context.requestOverride.header.user_groups = $context.authorizer.user_groups)\n#set($context.requestOverride.header.user_id = $context.authorizer.user_id)\n#set($context.requestOverride.header.tenant_id = $context.authorizer.tenant_id)\n#set($context.requestOverride.header.username = $context.authorizer.username)\n#set($context.requestOverride.header.email = $context.authorizer.email)\n#set($context.requestOverride.header.user_uuid = $context.authorizer.user_uuid)\n#set($context.requestOverride.header.locale = $context.authorizer.locale)\n#set($context.requestOverride.header.zoneinfo = $context.authorizer.zoneinfo)\n#set($context.requestOverride.header.email_verified = $context.authorizer.email_verified)\n\n $input.body"}, "passthroughBehavior": "when_no_templates", "type": "http"}}, "options": {"description": "Options", "operationId": "NotificationController_findByFilterOptions", "parameters": [], "responses": {"200": {"description": "200 response", "headers": {"Access-Control-Allow-Origin": {"schema": {"type": "string"}}, "Access-Control-Allow-Methods": {"schema": {"type": "string"}}, "Access-Control-Allow-Headers": {"schema": {"type": "string"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Empty"}}}}}, "summary": "Options", "tags": ["notifications"], "x-amazon-apigateway-integration": {"responses": {"default": {"statusCode": "200", "responseParameters": {"method.response.header.Access-Control-Allow-Methods": "'DELETE,GET,HEAD,OPTIONS,PATCH,POST,PUT'", "method.response.header.Access-Control-Allow-Headers": "'Content-Type,Authorization,X-Amz-Date,X-Api-Key,X-Amz-Security-Token'", "method.response.header.Access-Control-Allow-Origin": "'*'"}}}, "requestTemplates": {"application/json": "{\"statusCode\": 200}"}, "passthroughBehavior": "when_no_match", "type": "mock"}}}}, "info": {"title": "Eazy Wallet Notifications API", "description": "Notifications API to handle allNotification operations for REST API calls.", "version": "v1", "contact": {}}, "tags": [], "servers": [{"url": "https://api-dev-rest.eazywallet.io"}], "components": {"securitySchemes": {"ew-iam-app-lambda-auth-jwt-cognito": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "Authorization", "in": "header", "description": "Lambda authorizer token", "x-amazon-apigateway-authtype": "custom", "x-amazon-apigateway-authorizer": {"authorizerUri": "arn:aws:apigateway:eu-central-1:lambda:path/2015-03-31/functions/arn:aws:lambda:eu-central-1:158907794445:function:ew-iam-application-JwtAuthorizerFunction-djwriLMpSAra/invocations", "authorizerResultTtlInSeconds": 300, "identitySource": "method.request.header.Authorization", "type": "request"}}, "api_key": {"type": "<PERSON><PERSON><PERSON><PERSON>", "in": "header", "name": "x-api-key", "description": "API key"}}, "schemas": {"PaginationDto": {"type": "object", "properties": {}}, "PaginationLinkDto": {"type": "object", "properties": {}}, "PaginationMetaDto": {"type": "object", "properties": {}}, "NotificationDto": {"type": "object", "properties": {}}, "WPExceptionMessage": {"type": "object", "properties": {}}, "Empty": {"type": "object", "properties": {}}}}}