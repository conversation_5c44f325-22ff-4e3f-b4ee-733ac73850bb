import { DocumentBuilder, OpenAPIObject, SwaggerCustomOptions, SwaggerModule } from "@nestjs/swagger";
import { INestApplication } from "@nestjs/common";
import { ConfigService } from "@nestjs/config";
import { WP_ERROR_CONFIG_NOT_FOUND } from "../../commons/error/error.codes";
import { SwaggerConfig } from "./swagger-config";
import util from 'util';
import { ParameterObject, PathItemObject, PathsObject, ReferenceObject, ResponsesObject, SecurityRequirementObject } from "@nestjs/swagger/dist/interfaces/open-api-spec.interface";
import { OperationObject } from "@nestjs/swagger/dist/interfaces/open-api-spec.interface";
import { SwaggerConfigLoader } from "./swagget-config-loader";
import { WPError } from "../../commons/error/wp.error";


/**
 * SwaggerFactory is responsible for generating and configuring Swagger documentation
 * for various modules in a NestJS application. It integrates with AWS API Gateway
 * and Lambda authorizers to provide secure and well-documented APIs.
 */
export class SwaggerFactory {

    public static awsApiGwintegrationObecyKey: string = "x-amazon-apigateway-integration"


    public static configs: SwaggerConfig | undefined = undefined;

    public static initConfigs() {
        this.configs = SwaggerConfigLoader.configs;
    }

    /**
    * Builds Swagger documentation for multiple modules (Auth, Wallet, Blockchain Node,
    * and Notifications) in the NestJS application.
    * @param apiGWApp - The NestJS application instance.
    */
    public static async buidSwaggers(apiGWApp: INestApplication) {
        await SwaggerFactory.buidlAuthSwagger(apiGWApp);
        await SwaggerFactory.buidlWalletSwagger(apiGWApp);
        await SwaggerFactory.buildNotificationsSwagger(apiGWApp);
        await SwaggerFactory.buildEazyWalletSwagger(apiGWApp);
    }

    /**
     * Retrieves a configuration value from the `ConfigService`. Throws an error if the
     * configuration is not found.
     * @param configParam - The configuration parameter name.
     * @param confService - The NestJS `ConfigService` instance.
     * @returns The configuration value as a string.
     * @throws WPError if the configuration is not found.
     */
    static getConfig(configParam: string, confService: ConfigService): string {
        const value: string | undefined = confService.get(configParam)
        if (!value) {
            throw new WPError(WP_ERROR_CONFIG_NOT_FOUND, `Config not found: ${configParam}`, undefined, WP_ERROR_CONFIG_NOT_FOUND.headerStatusCode + "")
        }
        return value;
    }


    /**
     * Generates Swagger documentation for the `AuthModule`.
     * @param apiGWApp - The NestJS application instance.
     */
    public static async buidlAuthSwagger(apiGWApp: INestApplication) {

        const awsAuthorizerLAmbdaFunationName: string = SwaggerFactory.configs!.securityConfigLambdaAuthorizer.name

        const swaggerGlobalconfigs = new DocumentBuilder()
            .setTitle("Eazy Wallet Auth API")
            .setDescription("Auth API to handle all security operations for REST API calls.")
            .setVersion("v1")
            .addSecurity(awsAuthorizerLAmbdaFunationName, {
                type: 'apiKey',
                name: 'Authorization',
                in: 'header',
                description: 'Lambda authorizer token',
            })
            .addApiKey({
                type: 'apiKey',
                name: 'x-api-key',
                in: 'header',
                description: 'API key',
            })
            .addServer("https://api-dev-rest.eazywallet.io")
            .build();

        const swaggerGlobalOptions: SwaggerCustomOptions = {
            customCss: `
                  .topbar-wrapper img {content:url(\'https://s3.eu-central-1.amazonaws.com/static.eazywallet.io/general/eazywallet-logo.png\'); width:250px; height:auto;}
                  .swagger-ui .topbar { background-color: white; }
                  `,
            //useGlobalPrefix:true,
        };

        const document: OpenAPIObject = SwaggerModule.createDocument(apiGWApp, swaggerGlobalconfigs,
            {
                include: [],
            }
        );
        const awsApiGwIntegrationUri: string | undefined = process.env.AWS_APIGW_INTEGRATION_URI_AUTH;
        if (!awsApiGwIntegrationUri) {
            throw new WPError(WP_ERROR_CONFIG_NOT_FOUND, `Config not found: AWS_APIGW_INTEGRATION_URI_AUTH`, undefined, WP_ERROR_CONFIG_NOT_FOUND.headerStatusCode + "")
        }

        this.addAwsConfigs(document, awsApiGwIntegrationUri);

        SwaggerModule.setup("auth-docs", apiGWApp, document, swaggerGlobalOptions);
    }

    /**
     * Generates Swagger documentation for the `TransactionModule` and `WalletModule`.
     * @param apiGWApp - The NestJS application instance.
     */
    public static async buidlWalletSwagger(apiGWApp: INestApplication) {

        const awsAuthorizerLambdaFunationName: string = SwaggerFactory.configs!.securityConfigLambdaAuthorizer.name

        const swaggerGlobalconfigs = new DocumentBuilder()
            .setTitle("Eazy Wallet Wallet API")
            .setDescription("Wallet API to handle all wallet operations for REST API calls.")
            .setVersion("v1")
            .addSecurity(awsAuthorizerLambdaFunationName, {
                type: 'apiKey',
                name: 'Authorization',
                in: 'header',
                description: 'Lambda authorizer token',
            })
            .addApiKey({
                type: 'apiKey',
                name: 'x-api-key',
                in: 'header',
                description: 'API key',
            })
            .addServer("https://api-dev-rest.eazywallet.io")
            .build();

        const swaggerGlobalOptions: SwaggerCustomOptions = {
            customCss: `
                  .topbar-wrapper img {content:url(\'https://s3.eu-central-1.amazonaws.com/static.eazywallet.io/general/eazywallet-logo.png\'); width:250px; height:auto;}
                  .swagger-ui .topbar { background-color: white; }
                  `,
            //useGlobalPrefix:true,
        };

        const document = SwaggerModule.createDocument(apiGWApp, swaggerGlobalconfigs,
            {
                include: [],
            }
        );

        const awsApiGwIntegrationUri: string | undefined = process.env.AWS_APIGW_INTEGRATION_URI_WALLET;
        if (!awsApiGwIntegrationUri) {
            throw new WPError(WP_ERROR_CONFIG_NOT_FOUND, `Config not found: AWS_APIGW_INTEGRATION_URI_WALLET`, undefined, WP_ERROR_CONFIG_NOT_FOUND.headerStatusCode + "")
        }

        this.addAwsConfigs(document, awsApiGwIntegrationUri);

        /*if (document.components?.securitySchemes?.["ew-iam-app-lambda-auth-jwt-cognito"]) {
            document.components.securitySchemes["ew-iam-app-lambda-auth-jwt-cognito"]['x-amazon-apigateway-authtype'] = 'custom';
            document.components.securitySchemes["ew-iam-app-lambda-auth-jwt-cognito"]['x-amazon-apigateway-authorizer'] = {
                "authorizerUri": authUri,
                "authorizerResultTtlInSeconds": 300,
                "identitySource": "method.request.header.Authorization",
                "type": "request"
            };
        }

        document.components["definitions"] = {
          "Empty" : {
          "type" : "object",
          "title" : "Empty Schema"
          }
      }*/

        SwaggerModule.setup("wallet-docs", apiGWApp, document, swaggerGlobalOptions);

    }


    /**
     * Generates Swagger documentation for the `NotificationModule`.
     * @param apiGWApp - The NestJS application instance.
     */
    public static async buildNotificationsSwagger(apiGWApp: INestApplication) {

        const awsAuthorizerLambdaFunationName: string = SwaggerFactory.configs!.securityConfigLambdaAuthorizer.name

        const swaggerGlobalconfigs = new DocumentBuilder()
            .setTitle("Eazy Wallet Notifications API")
            .setDescription("Notifications API to handle allNotification operations for REST API calls.")
            .setVersion("v1")
            .addSecurity(awsAuthorizerLambdaFunationName, {
                type: 'apiKey',
                name: 'Authorization',
                in: 'header',
                description: 'Lambda authorizer token',
            })
            .addApiKey({
                type: 'apiKey',
                name: 'x-api-key',
                in: 'header',
                description: 'API key',
            })
            .addServer("https://api-dev-rest.eazywallet.io")
            .build();

        const swaggerGlobalOptions: SwaggerCustomOptions = {
            customCss: `
                  .topbar-wrapper img {content:url(\'https://s3.eu-central-1.amazonaws.com/static.eazywallet.io/general/eazywallet-logo.png\'); width:250px; height:auto;}
                  .swagger-ui .topbar { background-color: white; }
                  `,
            //useGlobalPrefix:true,
        };

        const document: OpenAPIObject = SwaggerModule.createDocument(apiGWApp, swaggerGlobalconfigs,
            {
                include: [],
            }
        );

        const awsApiGwIntegrationUri: string | undefined = process.env.AWS_APIGW_INTEGRATION_URI_NOTIFICATIONS;
        if (!awsApiGwIntegrationUri) {
            throw new WPError(WP_ERROR_CONFIG_NOT_FOUND, `Config not found: AWS_APIGW_INTEGRATION_URI_NOTIFICATIONS`, undefined, WP_ERROR_CONFIG_NOT_FOUND.headerStatusCode + "")
        }

        this.addAwsConfigs(document, awsApiGwIntegrationUri);

        SwaggerModule.setup("notifications-docs", apiGWApp, document, swaggerGlobalOptions);
    }

    /**
     * Generates Swagger documentation for the fundamental rest resources in the eqzy wallet module.
     * This swagger does not requrie API KEY authentication
     * @param apiGWApp - The NestJS application instance.
     */
    public static async buildEazyWalletSwagger(apiGWApp: INestApplication) {
        const awsAuthorizerLambdaFunationName: string = SwaggerFactory.configs!.securityConfigLambdaAuthorizer.name

        const swaggerGlobalconfigs = new DocumentBuilder()
            .setTitle("Eazy Wallet UI API")
            .setDescription("API to handle request from UI components.")
            .setVersion("v1")
            .addSecurity(awsAuthorizerLambdaFunationName, {
                type: 'apiKey',
                name: 'Authorization',
                in: 'header',
                description: 'Lambda authorizer token',
            })
            .addServer("https://api-dev-rest.eazywallet.io")
            .build();

        const swaggerGlobalOptions: SwaggerCustomOptions = {
            customCss: `
                  .topbar-wrapper img {content:url(\'https://s3.eu-central-1.amazonaws.com/static.eazywallet.io/general/eazywallet-logo.png\'); width:250px; height:auto;}
                  .swagger-ui .topbar { background-color: white; }
                  `,
            //useGlobalPrefix:true,
        };

        const document: OpenAPIObject = SwaggerModule.createDocument(apiGWApp, swaggerGlobalconfigs,
            {
                include: [],
            }
        );

        const awsApiGwIntegrationUri: string | undefined = process.env.AWS_APIGW_INTEGRATION_URI;
        if (!awsApiGwIntegrationUri) {
            throw new WPError(WP_ERROR_CONFIG_NOT_FOUND, `Config not found: AWS_APIGW_INTEGRATION_URI`, undefined, WP_ERROR_CONFIG_NOT_FOUND.headerStatusCode + "")
        }

        this.addAwsConfigs(document, awsApiGwIntegrationUri, false);

        SwaggerModule.setup("eazy-wallet-docs", apiGWApp, document, swaggerGlobalOptions);
    }

    // --- utiltiy functions ---
    /**
     * Adds AWS API Gateway-specific configurations to the Swagger document.
     * @param document - The OpenAPI document to modify.
     * @param awsApiGwIntegrationUri - The URI for AWS API Gateway integration.
     */
    public static addAwsConfigs(document: OpenAPIObject, awsApiGwIntegrationUri: string, apiKeyEnabled: boolean = true) {

        this.addAwsLambdaSecurityScheme(document);

        if (document.paths) {
            this.processPaths4Aws(document.paths, awsApiGwIntegrationUri, apiKeyEnabled);
        }
    }

    /**
     * Adds AWS Lambda authorizer security schemes to the Swagger document.
     * @param document - The OpenAPI document to modify.
     */
    public static addAwsLambdaSecurityScheme(document: OpenAPIObject) {

        const awsAuthorizerLambdaFunationName: string = SwaggerFactory.configs!.securityConfigLambdaAuthorizer.name
        const authUri: string = SwaggerFactory.configs!.getAWSApigwLambdaAuthurizerUri()

        if (document.components?.securitySchemes?.[awsAuthorizerLambdaFunationName]) {
            document.components.securitySchemes[awsAuthorizerLambdaFunationName][SwaggerFactory.configs!.getAWSAapigwAuthTyeAttr()] = SwaggerFactory.configs!.getAWSApigwAuthTyeValue();
            document.components.securitySchemes[awsAuthorizerLambdaFunationName][SwaggerFactory.configs!.getAWSApigwAuthorizerAttributeName()] = {
                "authorizerUri": authUri,
                "authorizerResultTtlInSeconds": SwaggerFactory.configs!.getAWSApigwLambdaAuthurizerTTL(),
                "identitySource": SwaggerFactory.configs!.getAWSApigwLambdaAuthurizerIdentityResource(),
                "type": SwaggerFactory.configs!.getAWSApigwLambdaAuthurizerType(),
            };
        }
    }

    /**
     * Add AWS API Gateway Integration to the paths and adds an option method for each patch in the swagger document
     * 
     * @param pathsObject PathsObject
     * @param awsApiGwIntegrationUri string 
     */
    public static processPaths4Aws(pathsObject: PathsObject, awsApiGwIntegrationUri: string, apiKeyEnabled: boolean = true) {

        for (const path in pathsObject) {
            const pathItem: PathItemObject = pathsObject[path];


            if(!apiKeyEnabled) {
                const operationObject: OperationObject | undefined = pathItem.get ?? pathItem.post ?? pathItem.delete ?? pathItem.patch ?? pathItem.put ?? pathItem.head ?? pathItem.trace ?? pathItem.options;
                this.removeApiKeySecuityConf(operationObject);
            }

            const intUrl: string = awsApiGwIntegrationUri + path;
            let tags: string[] = [];
            if (pathItem.get) {
                this.addAwsApigwIntegration(pathItem.get, "GET", intUrl);
                tags = pathItem.get.tags!;
            }
            if (pathItem.post) {
                this.addAwsApigwIntegration(pathItem.post, "POST", intUrl);
                tags = pathItem.post.tags!;
            }
            if (pathItem.delete) {
                this.addAwsApigwIntegration(pathItem.delete, "DELETE", intUrl);
                tags = pathItem.delete.tags!;
            }
            if (pathItem.patch) {
                this.addAwsApigwIntegration(pathItem.patch, "PATCH", intUrl);
                tags = pathItem.patch.tags!;
            }
            if (pathItem.put) {
                this.addAwsApigwIntegration(pathItem.put, "PUT", intUrl);
                tags = pathItem.put.tags!;
            }
            if (pathItem.head) {
                this.addAwsApigwIntegration(pathItem.head, "HEAD", intUrl);
                tags = pathItem.head.tags!;
            }
            if (pathItem.trace) {
                this.addAwsApigwIntegration(pathItem.trace, "TRACE", intUrl);
                tags = pathItem.trace.tags!;
            }
            if (!pathItem.options) {
                this.addOptionsPath4Aws(pathItem, tags);
            }

            //console.log("path", path);
            //console.log("pathItem", util.inspect(pathItem, false, 10, true));
        }

    }

    public static removeApiKeySecuityConf(operationObject?: OperationObject) {
        if (operationObject?.security) {
            const secReqObjects: SecurityRequirementObject[] = operationObject.security;
            const updatedSecReqObjects: SecurityRequirementObject[] = secReqObjects.filter((secReqObject) => {
                if (secReqObject[SwaggerConfig.securitySchemesApiKeyName]) {
                    return false;
                }
                return true;
            }
            );
            operationObject.security = updatedSecReqObjects;
        }
    }

    /**
     * Add AWS API Gateway Integration to the given http operation in the swaggger document.
     * 
     * if the operation is a proxy integration, the integration response and request templates are not added, 
     * extra headers are added to the request parameters.
     * 
     * if the operation is not a proxy integration, the integration response and request templates are added.
     * 
     * @param operation OperationObject
     * @param httpMethod string
     * @param integrationUri string
     * @param proxyIntegration boolean
     * 
     * Sample:
     * 
     * "x-amazon-apigateway-integration" : {
          "httpMethod" : "GET",
          "uri" : "http://ec2-3-127-110-168.eu-central-1.compute.amazonaws.com:8107/v1/notifications/{id}",
           "responses" : {
            "default" : {
              "statusCode" : "500"
            },
            "200" : {
              "statusCode" : "200"
            },
            "400" : {
              "statusCode" : "400"
            },
            "403" : {
              "statusCode" : "403"
            },
            "404" : {
              "statusCode" : "404"
            }
          },
          "requestParameters" : {
            "integration.request.querystring.test" : "method.request.querystring.test",
            "integration.request.path.id" : "method.request.path.id"
          },
          "requestTemplates" : {
            "application/json" : "#set($context.requestOverride.header.user_groups = $context.authorizer.user_groups)\n#set($context.requestOverride.header.user_id = $context.authorizer.user_id)\n#set($context.requestOverride.header.username = $context.authorizer.username)\n#set($context.requestOverride.header.email = $context.authorizer.email)\n#set($context.requestOverride.header.user_uuid = $context.authorizer.user_uuid)\n#set($context.requestOverride.header.locale = $context.authorizer.locale)\n#set($context.requestOverride.header.zoneinfo = $context.authorizer.zoneinfo)\n#set($context.requestOverride.header.email_verified = $context.authorizer.email_verified)\n\n $input.body"
          },
          "passthroughBehavior" : "when_no_templates",
          "type" : "http"
        }
     */
    public static addAwsApigwIntegration(operation: OperationObject, httpMethod: string, integrationUri: string, proxyIntegration: boolean = true) {
        if (!operation[this.awsApiGwintegrationObecyKey]) {
            let apigatewayIntegrationObj = {
                httpMethod: httpMethod,
                uri: integrationUri,
                responses: {} as Record<string, any> | undefined,
                requestTemplates: {} as Record<string, string> | undefined,
                requestParameters: {} as Record<string, string> | undefined,
                passthroughBehavior: '',
                type: ''
            }

            if (proxyIntegration) {
                apigatewayIntegrationObj.type = "http_proxy";
                apigatewayIntegrationObj.passthroughBehavior = "when_no_match",
                    delete apigatewayIntegrationObj.requestTemplates;
                delete apigatewayIntegrationObj.responses;
            }
            else {
                apigatewayIntegrationObj.type = "http";
                apigatewayIntegrationObj.passthroughBehavior = "when_no_templates",
                    apigatewayIntegrationObj.requestTemplates = {
                        'application/json': "#set($context.requestOverride.header.user_groups = $context.authorizer.user_groups)\n#set($context.requestOverride.header.user_id = $context.authorizer.user_id)\n#set($context.requestOverride.header.username = $context.authorizer.username)\n#set($context.requestOverride.header.email = $context.authorizer.email)\n#set($context.requestOverride.header.user_uuid = $context.authorizer.user_uuid)\n#set($context.requestOverride.header.locale = $context.authorizer.locale)\n#set($context.requestOverride.header.zoneinfo = $context.authorizer.zoneinfo)\n#set($context.requestOverride.header.email_verified = $context.authorizer.email_verified)\n\n $input.body"
                    };

                const responseObject: ResponsesObject = operation.responses;
                if (responseObject) {
                    let responses: Record<string, any> = {} as Record<string, any>;
                    for (const response in responseObject) {
                        const responseCodeMatch = response == "500" ? "default" : response;
                        responses[responseCodeMatch] = {
                            "statusCode": response
                        }
                    }
                    apigatewayIntegrationObj["responses"] = responses;
                }

            }

            operation[this.awsApiGwintegrationObecyKey] = apigatewayIntegrationObj;

            this.addAwsApigwIntegrationParameters(operation, proxyIntegration);

        }

    }

    /**
     * if proxyIntegration is true, the request header parameters are added to the integration object and no need to add query string parmeters
     * @param operation 
     * @param proxyIntegration 
     */
    public static addAwsApigwIntegrationParameters(operation: OperationObject, proxyIntegration: boolean = true) {

        let requestParameters: Record<string, string> = {} as Record<string, string>;

        if (proxyIntegration) {
            requestParameters = {
                "integration.request.header.user_uuid": "context.authorizer.user_uuid",
                "integration.request.header.email": "context.authorizer.email",
                "integration.request.header.username": "context.authorizer.username",
                "integration.request.header.user_groups": "context.authorizer.user_groups",
                "integration.request.header.user_id": "context.authorizer.user_id",
                "integration.request.header.zoneinfo": "context.authorizer.zoneinfo",
                "integration.request.header.locale": "context.authorizer.locale",
                "integration.request.header.email_verified": "context.authorizer.email_verified"
            };
        }

        const paramObjects: (ParameterObject | ReferenceObject)[] | undefined = operation.parameters;
        if (paramObjects) {
            for (const param in paramObjects) {
                const paramObject: ParameterObject = paramObjects[param] as ParameterObject;
                if (paramObject.in === "path") {
                    requestParameters[`integration.request.path.${paramObject.name}`] = `method.request.path.${paramObject.name}`;
                }
                if (!proxyIntegration) {
                    if (paramObject.in === "query") {
                        requestParameters[`integration.request.querystring.${paramObject.name}`] = `method.request.querystring.${paramObject.name}`;
                    }
                }

            }
        }

        if (Object.keys(requestParameters).length > 0) {
            operation[this.awsApiGwintegrationObecyKey]["requestParameters"] = requestParameters;
        }
    }

    /**
     * Add an options method to the given http operation in the swagger document
     * 
     * @param pathItem PathItemObject
     * @param tags string[] | undefined
     */
    public static addOptionsPath4Aws(pathItem: PathItemObject, tags: string[] | undefined) {

        if (!pathItem.options) {
            pathItem.options = {
                summary: "Options",
                description: "Options",
                operationId: "optionsId",
                tags: tags,
                responses: {
                    "200": {
                        description: "200 response",
                        content: {
                            "application/json": {
                                schema: {
                                    $ref: "#/components/schemas/Empty"
                                }
                            }
                        },
                        headers: {
                            "Access-Control-Allow-Origin": {
                                schema: {
                                    type: "string"
                                }
                            },
                            "Access-Control-Allow-Methods": {
                                schema: {
                                    type: "string"
                                }
                            },
                            "Access-Control-Allow-Headers": {
                                schema: {
                                    type: "string"
                                }
                            }
                        }
                    }
                }
            } as OperationObject

            pathItem.options["x-amazon-apigateway-integration"] = {
                "responses": {
                    "default": {
                        "statusCode": "200",
                        "responseParameters": {
                            "method.response.header.Access-Control-Allow-Methods": "'DELETE,GET,HEAD,OPTIONS,PATCH,POST,PUT'",
                            "method.response.header.Access-Control-Allow-Headers": "'Content-Type,Authorization,X-Amz-Date,X-Api-Key,X-Amz-Security-Token'",
                            "method.response.header.Access-Control-Allow-Origin": "'*'"
                        }
                    }
                },
                "requestTemplates": {
                    "application/json": "{\"statusCode\": 200}"
                },
                "passthroughBehavior": "when_no_match",
                "type": "mock"
            }
        }

    }

}