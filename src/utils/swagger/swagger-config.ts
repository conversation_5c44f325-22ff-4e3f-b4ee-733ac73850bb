import { SwaggerSecurityConfigAwsLambda } from "./swagger-security-config-aws-lambda";

/**
 * A class that represents the security configuration for Swagger
 */
export class SwaggerConfig {

    public static securitySchemesApiKeyName: string = "api_key";

    securityConfigLambdaAuthorizer: SwaggerSecurityConfigAwsLambda;

    getAWSAapigwAuthTyeAttr():string {
        return this.securityConfigLambdaAuthorizer?.options.apigatewayAuthType.name;
    }   

    getAWSApigwAuthTyeValue():string {
        return this.securityConfigLambdaAuthorizer?.options.apigatewayAuthType.value;
    } 

    getAWSApigwAuthorizerAttributeName():string {
        return SwaggerSecurityConfigAwsLambda.awsApigwAuthorizerAttributeName;
    }

    getAWSApigwLambdaAuthurizerUri():string {
        return  this.securityConfigLambdaAuthorizer?.options.awsLambdaAuthurizer.value.authorizerUri;
    }

    getAWSApigwLambdaAuthurizerTTL():number {
        return  this.securityConfigLambdaAuthorizer?.options.awsLambdaAuthurizer.value.authorizerResultTtlInSeconds;
    }

    getAWSApigwLambdaAuthurizerIdentityResource():string {
        return  this.securityConfigLambdaAuthorizer?.options.awsLambdaAuthurizer.value.identitySource;
    }

    getAWSApigwLambdaAuthurizerType():string {
        return  this.securityConfigLambdaAuthorizer?.options.awsLambdaAuthurizer.value.type;
    }
     
}