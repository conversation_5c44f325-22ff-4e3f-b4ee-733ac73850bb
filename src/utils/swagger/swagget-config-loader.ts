import { WP_ERROR_CONFIG_NOT_FOUND } from "../../commons/error/error.codes";
import { WPError } from "../../commons/error/wp.error";
import { AWS_APIGW_AUTH_LAMBDA_AUTHORIZER_TTL_SECONDS_DAFULT_VALUE, AWS_APIGW_AUTH_LAMBDA_AUTHORIZER_URI_PARAM_NAME, AWS_APIGW_AUTH_LAMBDA_NAME_PARAM_NAME } from "../confs";
import { SwaggerConfig } from "./swagger-config";
import { AwsSecuritySchemeObject, SwaggerSecurityConfigAwsLambda } from "./swagger-security-config-aws-lambda";
import util from 'util';

/**
 * A class that loads the Swagger configuration.
 */
export class SwaggerConfigLoader {

    public static configs: SwaggerConfig | undefined = undefined;

    public static initConfigs() {
    
            const authLambdaName: string | undefined = process.env.AWS_APIGW_AUTH_LAMBDA_NAME ?? '';
            if (!authLambdaName) {
                throw new WPError(WP_ERROR_CONFIG_NOT_FOUND, `Config not found: ${AWS_APIGW_AUTH_LAMBDA_NAME_PARAM_NAME}`, undefined, WP_ERROR_CONFIG_NOT_FOUND.headerStatusCode + "")
            }
            let lambdaCacheTTL: string | undefined = process.env.AWS_APIGW_AUTH_LAMBDA_AUTHORIZER_TTL_SECONDS ?? '10';
            if (!lambdaCacheTTL) {
                lambdaCacheTTL = AWS_APIGW_AUTH_LAMBDA_AUTHORIZER_TTL_SECONDS_DAFULT_VALUE
            }
            const authUri: string | undefined = process.env.AWS_APIGW_AUTH_LAMBDA_AUTHORIZER_URI ?? '';
            if (!authUri) {
                throw new WPError(WP_ERROR_CONFIG_NOT_FOUND, `Config not found: ${AWS_APIGW_AUTH_LAMBDA_AUTHORIZER_URI_PARAM_NAME}`, undefined, WP_ERROR_CONFIG_NOT_FOUND.headerStatusCode + "")
            }
    
    
            if (!SwaggerConfigLoader.configs) {
                SwaggerConfigLoader.configs = new SwaggerConfig();
                const awsSecuritySchemeObject: AwsSecuritySchemeObject = {
                    type: "apiKey",
                    name: "Authorization",
                    in: "header",
                    description: 'Lambda authorizer token',
                    apigatewayAuthType: {
                        name: SwaggerSecurityConfigAwsLambda.awsApigwAuthTypeAttributeName,
                        value: SwaggerSecurityConfigAwsLambda.awsApigwAuthTypeAttributeValue,
                    },
                    awsLambdaAuthurizer: {
                        name: SwaggerSecurityConfigAwsLambda.awsApigwAuthorizerAttributeName, // "x-amazon-apigateway-authorizer"
                        value: {
                            "authorizerUri": authUri,
                            "authorizerResultTtlInSeconds": parseInt(lambdaCacheTTL!),
                            "identitySource": SwaggerSecurityConfigAwsLambda.awsApigwIdentitySource, // "method.request.header.Authorization",
                            "type": SwaggerSecurityConfigAwsLambda.awsApigwAuthLambdaType // "request"
                        }
                    }
                };
    
                const securityConfigLambdaAuthorizer: SwaggerSecurityConfigAwsLambda = new SwaggerSecurityConfigAwsLambda(authLambdaName!, awsSecuritySchemeObject);
                SwaggerConfigLoader.configs.securityConfigLambdaAuthorizer = securityConfigLambdaAuthorizer;
    
                //console.log("SwaggerConfigLoader.configs", util.inspect(SwaggerConfigLoader.configs.securityConfigLambdaAuthorizer));
            }
        }
}