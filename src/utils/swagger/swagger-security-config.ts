import { SecuritySchemeObject } from "@nestjs/swagger/dist/interfaces/open-api-spec.interface";

/**
 * A class that represents the security configuration for Swagger.
 * It contains the name of the security scheme and its options.
 */
export class SwaggerSecurityConfig {
    name:string;
    options: SecuritySchemeObject;
    constructor(name: string, options: SecuritySchemeObject) {
        this.name = name;
        this.options = options;
    }
}