import { SecuritySchemeObject } from "@nestjs/swagger/dist/interfaces/open-api-spec.interface";

/**
 * A class that represents the AWS security schema object configuration for Swagger.
 */
export interface AwsSecuritySchemeObject extends SecuritySchemeObject {

    apigatewayAuthType: {
        name: string
        value: string
    },

    awsLambdaAuthurizer: {
        name: string,
        value: {
        "authorizerUri" : string,
        "authorizerResultTtlInSeconds" : number,
        "identitySource" : string,
        "type" : string
    }
};
}

/**
 * A class that represents the security configuration for AWS Lambda authorizer in Swagger
 */
export class SwaggerSecurityConfigAwsLambda {

    static awsApigwAuthorizerAttributeName:string  = "x-amazon-apigateway-authorizer";
    static awsApigwAuthTypeAttributeName:string  = "x-amazon-apigateway-authtype";
    static awsApigwAuthTypeAttributeValue:string  = "custom";
    static awsApigwIdentitySource:string = "method.request.header.Authorization";
    static awsApigwAuthLambdaType:string = "request";

    name:string;
    options: AwsSecuritySchemeObject;

    constructor(name: string, options: AwsSecuritySchemeObject) {
        this.name = name;
        this.options = options;
    }
}