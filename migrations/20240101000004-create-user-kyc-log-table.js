'use strict';

const { createMigrationHelpers } = require('./utils/migration-helpers');

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    const helpers = createMigrationHelpers(queryInterface, Sequelize);

    // Create user_kyc_log table
    await helpers.safeCreateTable('user_kyc_log', {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true,
        allowNull: false
      },
      user_id: {
        type: Sequelize.STRING,
        allowNull: true,
        references: {
          model: 'users',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'SET NULL'
      },
      data: {
        type: Sequelize.TEXT,
        allowNull: false
      },
      created_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW
      },
      updated_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW
      },
      deleted_at: {
        type: Sequelize.DATE,
        allowNull: true
      }
    });

    // Add indexes
    const indexes = [
      {
        name: 'user_kyc_log_user_id_index',
        fields: ['user_id']
      },
      {
        name: 'user_kyc_log_created_at_index',
        fields: ['created_at']
      },
      {
        name: 'user_kyc_log_deleted_at_index',
        fields: ['deleted_at']
      }
    ];

    await helpers.safeAddIndexes('user_kyc_log', indexes);
    
    console.log('✅ Created user_kyc_log table with indexes and foreign keys');
  },

  async down(queryInterface, Sequelize) {
    const helpers = createMigrationHelpers(queryInterface, Sequelize);
    
    // Remove indexes first
    const indexNames = [
      'user_kyc_log_user_id_index',
      'user_kyc_log_created_at_index',
      'user_kyc_log_deleted_at_index'
    ];

    await helpers.safeRemoveIndexes('user_kyc_log', indexNames);
    
    // Drop table
    await helpers.safeDropTable('user_kyc_log');
    
    console.log('✅ Dropped user_kyc_log table and indexes');
  }
};
