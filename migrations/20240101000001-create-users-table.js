'use strict';

const { createMigrationHelpers } = require('./utils/migration-helpers');

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    const helpers = createMigrationHelpers(queryInterface, Sequelize);

    // Create users table
    await helpers.safeCreateTable('users', {
      id: {
        type: Sequelize.STRING,
        primaryKey: true,
        allowNull: false
      },
      name: {
        type: Sequelize.STRING,
        allowNull: true
      },
      last_name: {
        type: Sequelize.STRING,
        allowNull: true
      },
      uuid: {
        type: Sequelize.STRING,
        allowNull: true
      },
      nickname: {
        type: Sequelize.STRING,
        allowNull: true
      },
      address: {
        type: Sequelize.STRING,
        allowNull: true
      },
      birthdate: {
        type: Sequelize.DATEONLY,
        allowNull: true
      },
      gender: {
        type: Sequelize.INTEGER,
        allowNull: true
      },
      image: {
        type: Sequelize.STRING,
        allowNull: true
      },
      password: {
        type: Sequelize.STRING,
        allowNull: true
      },
      email: {
        type: Sequelize.STRING,
        allowNull: false
      },
      phone_number: {
        type: Sequelize.STRING,
        allowNull: true
      },
      locale: {
        type: Sequelize.STRING,
        allowNull: true
      },
      zone_info: {
        type: Sequelize.STRING,
        allowNull: true
      },
      email_verified: {
        type: Sequelize.BOOLEAN,
        allowNull: true
      },
      phone_verified: {
        type: Sequelize.BOOLEAN,
        allowNull: true
      },
      kyc_enabled: {
        type: Sequelize.BOOLEAN,
        allowNull: true
      },
      kyc_status: {
        type: Sequelize.INTEGER,
        allowNull: true,
        defaultValue: null
      },
      kyc_requested_at: {
        type: Sequelize.DATE,
        allowNull: true
      },
      email_verified_at: {
        type: Sequelize.DATE,
        allowNull: true
      },
      register_seen_at: {
        type: Sequelize.DATE,
        allowNull: true,
        defaultValue: null
      },
      utm_source: {
        type: Sequelize.STRING,
        allowNull: true
      },
      phone_verified_at: {
        type: Sequelize.DATE,
        allowNull: true
      },
      is_admin: {
        type: Sequelize.BOOLEAN,
        allowNull: false,
        defaultValue: false
      },
      cooldown_end_at: {
        type: Sequelize.DATE,
        allowNull: true
      },
      remember_token: {
        type: Sequelize.CHAR(100),
        allowNull: true
      },
      two_factor_secret: {
        type: Sequelize.TEXT,
        allowNull: true
      },
      two_factor_recovery_codes: {
        type: Sequelize.TEXT,
        allowNull: true
      },
      referral_code: {
        type: Sequelize.CHAR(10),
        allowNull: true
      },
      referrer_code: {
        type: Sequelize.CHAR(10),
        allowNull: true
      },
      created_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW
      },
      updated_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW
      },
      deleted_at: {
        type: Sequelize.DATE,
        allowNull: true
      }
    });

    // Add indexes
    const indexes = [
      {
        name: 'users_email_unique',
        unique: true,
        fields: ['email']
      },
      {
        name: 'users_referral_code_unique',
        unique: true,
        fields: ['referral_code']
      },
      {
        name: 'users_uuid_index',
        fields: ['uuid']
      },
      {
        name: 'users_nickname_index',
        fields: ['nickname']
      },
      {
        name: 'users_phone_number_index',
        fields: ['phone_number']
      },
      {
        name: 'users_referrer_code_index',
        fields: ['referrer_code']
      },
      {
        name: 'users_kyc_status_index',
        fields: ['kyc_status']
      },
      {
        name: 'users_created_at_index',
        fields: ['created_at']
      },
      {
        name: 'users_deleted_at_index',
        fields: ['deleted_at']
      }
    ];

    await helpers.safeAddIndexes('users', indexes);
    
    console.log('✅ Created users table with indexes');
  },

  async down(queryInterface, Sequelize) {
    const helpers = createMigrationHelpers(queryInterface, Sequelize);
    
    // Remove indexes first
    const indexNames = [
      'users_email_unique',
      'users_referral_code_unique',
      'users_uuid_index',
      'users_nickname_index',
      'users_phone_number_index',
      'users_referrer_code_index',
      'users_kyc_status_index',
      'users_created_at_index',
      'users_deleted_at_index'
    ];

    await helpers.safeRemoveIndexes('users', indexNames);
    
    // Drop table
    await helpers.safeDropTable('users');
    
    console.log('✅ Dropped users table and indexes');
  }
};
