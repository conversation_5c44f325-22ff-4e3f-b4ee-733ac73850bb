'use strict';

const { createMigrationHelpers } = require('./utils/migration-helpers');

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    const helpers = createMigrationHelpers(queryInterface, Sequelize);

    // Create user_kyc_info table
    await helpers.safeCreateTable('user_kyc_info', {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true,
        allowNull: false
      },
      user_id: {
        type: Sequelize.STRING,
        allowNull: false,
        references: {
          model: 'users',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      id_no: {
        type: Sequelize.STRING,
        allowNull: false
      },
      created_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW
      },
      updated_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW
      },
      deleted_at: {
        type: Sequelize.DATE,
        allowNull: true
      }
    });

    // Add indexes
    const indexes = [
      {
        name: 'user_kyc_info_user_id_index',
        fields: ['user_id']
      },
      {
        name: 'user_kyc_info_id_no_index',
        fields: ['id_no']
      },
      {
        name: 'user_kyc_info_created_at_index',
        fields: ['created_at']
      },
      {
        name: 'user_kyc_info_deleted_at_index',
        fields: ['deleted_at']
      },
      {
        name: 'user_kyc_info_id_no_unique',
        unique: true,
        fields: ['id_no']
      }
    ];

    await helpers.safeAddIndexes('user_kyc_info', indexes);
    
    console.log('✅ Created user_kyc_info table with indexes and foreign keys');
  },

  async down(queryInterface, Sequelize) {
    const helpers = createMigrationHelpers(queryInterface, Sequelize);
    
    // Remove indexes first
    const indexNames = [
      'user_kyc_info_user_id_index',
      'user_kyc_info_id_no_index',
      'user_kyc_info_created_at_index',
      'user_kyc_info_deleted_at_index',
      'user_kyc_info_id_no_unique'
    ];

    await helpers.safeRemoveIndexes('user_kyc_info', indexNames);
    
    // Drop table
    await helpers.safeDropTable('user_kyc_info');
    
    console.log('✅ Dropped user_kyc_info table and indexes');
  }
};
