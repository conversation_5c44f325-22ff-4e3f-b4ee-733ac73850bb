'use strict';

const { createMigrationHelpers } = require('./utils/migration-helpers');

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    const helpers = createMigrationHelpers(queryInterface, Sequelize);

    // Create user_kyc table
    await helpers.safeCreateTable('user_kyc', {
      id: {
        type: Sequelize.STRING,
        primaryKey: true,
        allowNull: false
      },
      user_id: {
        type: Sequelize.STRING,
        allowNull: true,
        references: {
          model: 'users',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'SET NULL'
      },
      reference_id: {
        type: Sequelize.STRING,
        allowNull: true
      },
      client_type: {
        type: Sequelize.ENUM('GAMEHUB', 'DESKTOP', 'WALLET_WEB', 'WALLET_MOBILE'),
        allowNull: true
      },
      email: {
        type: Sequelize.STRING,
        allowNull: true
      },
      country: {
        type: Sequelize.STRING,
        allowNull: true
      },
      first_name: {
        type: Sequelize.STRING,
        allowNull: true
      },
      last_name: {
        type: Sequelize.STRING,
        allowNull: true
      },
      national_id: {
        type: Sequelize.STRING,
        allowNull: true
      },
      date_of_birth: {
        type: Sequelize.STRING,
        allowNull: true
      },
      result: {
        type: Sequelize.INTEGER,
        allowNull: true
      },
      declined_reason: {
        type: Sequelize.STRING,
        allowNull: true
      },
      declined_codes: {
        type: Sequelize.STRING,
        allowNull: true
      },
      created_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW
      },
      updated_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW
      },
      deleted_at: {
        type: Sequelize.DATE,
        allowNull: true
      }
    });

    // Add indexes
    const indexes = [
      {
        name: 'user_kyc_user_id_index',
        fields: ['user_id']
      },
      {
        name: 'user_kyc_reference_id_index',
        fields: ['reference_id']
      },
      {
        name: 'user_kyc_email_index',
        fields: ['email']
      },
      {
        name: 'user_kyc_national_id_index',
        fields: ['national_id']
      },
      {
        name: 'user_kyc_result_index',
        fields: ['result']
      },
      {
        name: 'user_kyc_client_type_index',
        fields: ['client_type']
      },
      {
        name: 'user_kyc_created_at_index',
        fields: ['created_at']
      },
      {
        name: 'user_kyc_deleted_at_index',
        fields: ['deleted_at']
      }
    ];

    await helpers.safeAddIndexes('user_kyc', indexes);
    
    console.log('✅ Created user_kyc table with indexes and foreign keys');
  },

  async down(queryInterface, Sequelize) {
    const helpers = createMigrationHelpers(queryInterface, Sequelize);
    
    // Remove indexes first
    const indexNames = [
      'user_kyc_user_id_index',
      'user_kyc_reference_id_index',
      'user_kyc_email_index',
      'user_kyc_national_id_index',
      'user_kyc_result_index',
      'user_kyc_client_type_index',
      'user_kyc_created_at_index',
      'user_kyc_deleted_at_index'
    ];

    await helpers.safeRemoveIndexes('user_kyc', indexNames);
    
    // Drop table
    await helpers.safeDropTable('user_kyc');
    
    console.log('✅ Dropped user_kyc table and indexes');
  }
};
