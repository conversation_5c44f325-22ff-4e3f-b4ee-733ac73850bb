'use strict';

/**
 * Migration Helper Utilities
 * 
 * Provides idempotent database operation helpers for Sequelize migrations.
 * These utilities ensure migrations can be run multiple times safely.
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 */

class MigrationHelpers {
  constructor(queryInterface, Sequelize) {
    this.queryInterface = queryInterface;
    this.Sequelize = Sequelize;
  }

  /**
   * Check if a table exists in the database
   * @param {string} tableName - Name of the table to check
   * @returns {Promise<boolean>} - True if table exists, false otherwise
   */
  async tableExists(tableName) {
    try {
      const result = await this.queryInterface.sequelize.query(
        `SELECT COUNT(*) as count FROM information_schema.tables 
         WHERE table_schema = DATABASE() 
         AND table_name = ?`,
        { 
          replacements: [tableName],
          type: this.Sequelize.QueryTypes.SELECT 
        }
      );
      return result[0].count > 0;
    } catch (error) {
      console.log(`⚠️  Error checking table ${tableName}:`, error.message);
      return false;
    }
  }

  /**
   * Check if a column exists in a table
   * @param {string} tableName - Name of the table
   * @param {string} columnName - Name of the column to check
   * @returns {Promise<boolean>} - True if column exists, false otherwise
   */
  async columnExists(tableName, columnName) {
    try {
      const result = await this.queryInterface.sequelize.query(
        `SELECT COUNT(*) as count FROM information_schema.columns 
         WHERE table_schema = DATABASE() 
         AND table_name = ? 
         AND column_name = ?`,
        { 
          replacements: [tableName, columnName],
          type: this.Sequelize.QueryTypes.SELECT 
        }
      );
      return result[0].count > 0;
    } catch (error) {
      console.log(`⚠️  Error checking column ${tableName}.${columnName}:`, error.message);
      return false;
    }
  }

  /**
   * Check if an index exists on a table
   * @param {string} tableName - Name of the table
   * @param {string} indexName - Name of the index to check
   * @returns {Promise<boolean>} - True if index exists, false otherwise
   */
  async indexExists(tableName, indexName) {
    try {
      const result = await this.queryInterface.sequelize.query(
        `SELECT COUNT(*) as count FROM information_schema.statistics 
         WHERE table_schema = DATABASE() 
         AND table_name = ? 
         AND index_name = ?`,
        { 
          replacements: [tableName, indexName],
          type: this.Sequelize.QueryTypes.SELECT 
        }
      );
      return result[0].count > 0;
    } catch (error) {
      console.log(`⚠️  Error checking index ${tableName}.${indexName}:`, error.message);
      return false;
    }
  }

  /**
   * Check if a foreign key constraint exists
   * @param {string} tableName - Name of the table
   * @param {string} constraintName - Name of the constraint to check
   * @returns {Promise<boolean>} - True if constraint exists, false otherwise
   */
  async constraintExists(tableName, constraintName) {
    try {
      const result = await this.queryInterface.sequelize.query(
        `SELECT COUNT(*) as count FROM information_schema.table_constraints 
         WHERE table_schema = DATABASE() 
         AND table_name = ? 
         AND constraint_name = ?`,
        { 
          replacements: [tableName, constraintName],
          type: this.Sequelize.QueryTypes.SELECT 
        }
      );
      return result[0].count > 0;
    } catch (error) {
      console.log(`⚠️  Error checking constraint ${tableName}.${constraintName}:`, error.message);
      return false;
    }
  }

  /**
   * Safely create a table (idempotent)
   * @param {string} tableName - Name of the table to create
   * @param {Object} attributes - Table attributes/columns definition
   * @param {Object} options - Additional options for table creation
   * @returns {Promise<boolean>} - True if table was created, false if already existed
   */
  async safeCreateTable(tableName, attributes, options = {}) {
    try {
      const exists = await this.tableExists(tableName);
      if (exists) {
        console.log(`ℹ️  Table ${tableName} already exists, skipping creation`);
        return false;
      }

      await this.queryInterface.createTable(tableName, attributes, options);
      console.log(`✅ Created table: ${tableName}`);
      return true;
    } catch (error) {
      if (error.message.includes('already exists')) {
        console.log(`ℹ️  Table ${tableName} already exists, skipping creation`);
        return false;
      }
      throw error;
    }
  }

  /**
   * Safely add a column to a table (idempotent)
   * @param {string} tableName - Name of the table
   * @param {string} columnName - Name of the column to add
   * @param {Object} columnDefinition - Column definition
   * @returns {Promise<boolean>} - True if column was added, false if already existed
   */
  async safeAddColumn(tableName, columnName, columnDefinition) {
    try {
      const exists = await this.columnExists(tableName, columnName);
      if (exists) {
        console.log(`ℹ️  Column ${tableName}.${columnName} already exists, skipping`);
        return false;
      }

      await this.queryInterface.addColumn(tableName, columnName, columnDefinition);
      console.log(`✅ Added column: ${tableName}.${columnName}`);
      return true;
    } catch (error) {
      if (error.message.includes('already exists') || error.message.includes('Duplicate column')) {
        console.log(`ℹ️  Column ${tableName}.${columnName} already exists, skipping`);
        return false;
      }
      throw error;
    }
  }

  /**
   * Safely add an index to a table (idempotent)
   * @param {string} tableName - Name of the table
   * @param {Object} indexDefinition - Index definition with fields and name
   * @returns {Promise<boolean>} - True if index was added, false if already existed
   */
  async safeAddIndex(tableName, indexDefinition) {
    try {
      const exists = await this.indexExists(tableName, indexDefinition.name);
      if (exists) {
        console.log(`ℹ️  Index ${indexDefinition.name} already exists, skipping`);
        return false;
      }

      await this.queryInterface.addIndex(tableName, indexDefinition);
      console.log(`✅ Added index: ${indexDefinition.name}`);
      return true;
    } catch (error) {
      if (error.message.includes('already exists') || 
          error.message.includes('Duplicate key') ||
          error.message.includes('duplicate key name')) {
        console.log(`ℹ️  Index ${indexDefinition.name} already exists, skipping`);
        return false;
      }
      console.log(`⚠️  Warning: Could not add index ${indexDefinition.name}:`, error.message);
      return false;
    }
  }

  /**
   * Safely add multiple indexes to a table
   * @param {string} tableName - Name of the table
   * @param {Array<Object>} indexes - Array of index definitions
   * @returns {Promise<number>} - Number of indexes successfully added
   */
  async safeAddIndexes(tableName, indexes) {
    let addedCount = 0;
    for (const index of indexes) {
      const added = await this.safeAddIndex(tableName, index);
      if (added) addedCount++;
    }
    return addedCount;
  }

  /**
   * Safely remove a column from a table (idempotent)
   * @param {string} tableName - Name of the table
   * @param {string} columnName - Name of the column to remove
   * @returns {Promise<boolean>} - True if column was removed, false if didn't exist
   */
  async safeRemoveColumn(tableName, columnName) {
    try {
      const exists = await this.columnExists(tableName, columnName);
      if (!exists) {
        console.log(`ℹ️  Column ${tableName}.${columnName} does not exist, skipping removal`);
        return false;
      }

      await this.queryInterface.removeColumn(tableName, columnName);
      console.log(`✅ Removed column: ${tableName}.${columnName}`);
      return true;
    } catch (error) {
      if (error.message.includes("doesn't exist") || error.message.includes("Unknown column")) {
        console.log(`ℹ️  Column ${tableName}.${columnName} does not exist, skipping removal`);
        return false;
      }
      throw error;
    }
  }

  /**
   * Safely remove an index from a table (idempotent)
   * @param {string} tableName - Name of the table
   * @param {string} indexName - Name of the index to remove
   * @returns {Promise<boolean>} - True if index was removed, false if didn't exist
   */
  async safeRemoveIndex(tableName, indexName) {
    try {
      const exists = await this.indexExists(tableName, indexName);
      if (!exists) {
        console.log(`ℹ️  Index ${indexName} does not exist, skipping removal`);
        return false;
      }

      await this.queryInterface.removeIndex(tableName, indexName);
      console.log(`✅ Removed index: ${indexName}`);
      return true;
    } catch (error) {
      if (error.message.includes("doesn't exist") || 
          error.message.includes("Can't DROP") ||
          error.message.includes("check that column/key exists")) {
        console.log(`ℹ️  Index ${indexName} does not exist, skipping removal`);
        return false;
      }
      throw error;
    }
  }

  /**
   * Safely remove multiple indexes from a table
   * @param {string} tableName - Name of the table
   * @param {Array<string>} indexNames - Array of index names to remove
   * @returns {Promise<number>} - Number of indexes successfully removed
   */
  async safeRemoveIndexes(tableName, indexNames) {
    let removedCount = 0;
    for (const indexName of indexNames) {
      const removed = await this.safeRemoveIndex(tableName, indexName);
      if (removed) removedCount++;
    }
    return removedCount;
  }

  /**
   * Safely drop a table (idempotent)
   * @param {string} tableName - Name of the table to drop
   * @returns {Promise<boolean>} - True if table was dropped, false if didn't exist
   */
  async safeDropTable(tableName) {
    try {
      const exists = await this.tableExists(tableName);
      if (!exists) {
        console.log(`ℹ️  Table ${tableName} does not exist, skipping drop`);
        return false;
      }

      await this.queryInterface.dropTable(tableName);
      console.log(`✅ Dropped table: ${tableName}`);
      return true;
    } catch (error) {
      if (error.message.includes("doesn't exist") || error.message.includes("Unknown table")) {
        console.log(`ℹ️  Table ${tableName} does not exist, skipping drop`);
        return false;
      }
      throw error;
    }
  }

  /**
   * Execute a raw SQL query with error handling
   * @param {string} sql - SQL query to execute
   * @param {Object} options - Query options (replacements, type, etc.)
   * @param {string} description - Description of what the query does (for logging)
   * @returns {Promise<any>} - Query result or null if failed
   */
  async safeQuery(sql, options = {}, description = 'Custom query') {
    try {
      const result = await this.queryInterface.sequelize.query(sql, options);
      console.log(`✅ ${description} completed successfully`);
      return result;
    } catch (error) {
      console.log(`⚠️  Warning: ${description} failed:`, error.message);
      return null;
    }
  }

  /**
   * Change column definition safely
   * @param {string} tableName - Name of the table
   * @param {string} columnName - Name of the column to change
   * @param {Object} columnDefinition - New column definition
   * @returns {Promise<boolean>} - True if column was changed, false if failed
   */
  async safeChangeColumn(tableName, columnName, columnDefinition) {
    try {
      const exists = await this.columnExists(tableName, columnName);
      if (!exists) {
        console.log(`ℹ️  Column ${tableName}.${columnName} does not exist, cannot change`);
        return false;
      }

      await this.queryInterface.changeColumn(tableName, columnName, columnDefinition);
      console.log(`✅ Changed column: ${tableName}.${columnName}`);
      return true;
    } catch (error) {
      console.log(`⚠️  Warning: Could not change column ${tableName}.${columnName}:`, error.message);
      return false;
    }
  }

  /**
   * Get table description (columns info)
   * @param {string} tableName - Name of the table
   * @returns {Promise<Object|null>} - Table description or null if failed
   */
  async getTableDescription(tableName) {
    try {
      return await this.queryInterface.describeTable(tableName);
    } catch (error) {
      console.log(`⚠️  Could not describe table ${tableName}:`, error.message);
      return null;
    }
  }

  /**
   * Get existing indexes for a table
   * @param {string} tableName - Name of the table
   * @returns {Promise<Array>} - Array of index information
   */
  async getTableIndexes(tableName) {
    try {
      return await this.queryInterface.showIndex(tableName);
    } catch (error) {
      console.log(`⚠️  Could not get indexes for table ${tableName}:`, error.message);
      return [];
    }
  }
}

/**
 * Factory function to create MigrationHelpers instance
 * @param {Object} queryInterface - Sequelize QueryInterface
 * @param {Object} Sequelize - Sequelize constructor
 * @returns {MigrationHelpers} - Helper instance
 */
function createMigrationHelpers(queryInterface, Sequelize) {
  return new MigrationHelpers(queryInterface, Sequelize);
}

module.exports = {
  MigrationHelpers,
  createMigrationHelpers
}; 