'use strict';

const { createMigrationHelpers } = require('./utils/migration-helpers');

/**
 * Sample migration showing how to use migration helpers for altering existing tables
 * This migration is commented out but shows best practices for future changes
 * 
 * @type {import('sequelize-cli').Migration}
 */
module.exports = {
  async up(queryInterface, Sequelize) {
    const helpers = createMigrationHelpers(queryInterface, Sequelize);

    // Example: Add a new column safely
    // await helpers.safeAddColumn('users', 'new_column', {
    //   type: Sequelize.STRING,
    //   allowNull: true,
    //   comment: 'Description of the new column'
    // });

    // Example: Add a new index safely
    // await helpers.safeAddIndex('users', {
    //   name: 'users_new_column_index',
    //   fields: ['new_column']
    // });

    // Example: Modify an existing column safely
    // await helpers.safeChangeColumn('users', 'existing_column', {
    //   type: Sequelize.TEXT,
    //   allowNull: false,
    //   defaultValue: 'default_value'
    // });

    // Example: Add multiple indexes at once
    // const newIndexes = [
    //   {
    //     name: 'users_compound_index',
    //     fields: ['column1', 'column2']
    //   },
    //   {
    //     name: 'users_partial_index',
    //     fields: ['column3'],
    //     where: {
    //       deleted_at: null
    //     }
    //   }
    // ];
    // await helpers.safeAddIndexes('users', newIndexes);

    console.log('ℹ️  Sample migration - no changes applied (all commented out)');
  },

  async down(queryInterface, Sequelize) {
    const helpers = createMigrationHelpers(queryInterface, Sequelize);

    // Example: Remove indexes safely
    // await helpers.safeRemoveIndex('users', 'users_new_column_index');
    // await helpers.safeRemoveIndexes('users', ['users_compound_index', 'users_partial_index']);

    // Example: Remove column safely
    // await helpers.safeRemoveColumn('users', 'new_column');

    console.log('ℹ️  Sample migration rollback - no changes applied (all commented out)');
  }
};
