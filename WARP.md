# WARP.md

This file provides guidance to WA<PERSON> (warp.dev) when working with code in this repository.

## Essential Development Commands

### Building and Running
- **Development**: `npm run start:dev` - Starts with hot reload
- **Debug mode**: `npm run start:debug` - Starts with debugging enabled
- **Production build**: `npm run build` - Compiles TypeScript to dist/
- **Production start**: `npm run start:prod` - Runs built application with New Relic

### Testing
- **Run all tests**: `npm test`
- **Watch mode**: `npm run test:watch`
- **Coverage report**: `npm run test:cov`
- **E2E tests**: `npm run test:e2e`
- **Debug tests**: `npm run test:debug`

### Code Quality
- **Lint and fix**: `npm run lint`
- **Format code**: `npm run format`

### Database Operations
- **Run migrations**: `npx sequelize-cli db:migrate`
- **Create migration**: `npx sequelize-cli migration:generate --name <migration-name>`
- **Seed database**: `npx sequelize-cli db:seed:all`

## Architecture Overview

### Core Application Structure
This is a **NestJS-based wallet service** with the following key architectural components:

**Main Application Bootstrap** (`src/main.ts`):
- Sets up global exception filters, validation pipes, and observability interceptors
- Configures Swagger documentation at `/docs`
- Implements CORS, authentication middleware, and request logging
- Integrates New Relic for monitoring

**Application Module** (`src/app.module.ts`):
- Uses Sequelize with MySQL as the primary database
- Implements Redis for caching and session management  
- Includes global AuthGuard for JWT-based authentication
- Modular architecture with feature-based modules

### Key Modules and Domains

**IAM (Identity & Access Management)** (`src/iam/`):
- **AWS Integration**: Cognito user management, S3 file uploads, KMS encryption
- **User Management**: Registration, authentication, KYC verification
- **Entities**: Users, KYC data, audit logs

**Commons** (`src/commons/`):
- **Authentication**: JWT guards, role-based access, user context
- **Observability**: New Relic integration, custom metrics, request tracing
- **Error Handling**: Global exception filters and custom error codes
- **Notifications**: Service for handling user notifications

**Database Layer**:
- **Sequelize ORM** with MySQL for transactional data
- **Migration system** for schema versioning
- **Repository pattern** for data access
- **Transaction decorators** for data consistency

### External Dependencies
- **AWS Services**: Cognito (auth), S3 (storage), KMS (encryption), CloudWatch (metrics)
- **Databases**: MySQL (primary), Redis (cache), optional MongoDB/Scylla
- **Monitoring**: New Relic APM and logging
- **Authentication**: JWT tokens with Cognito integration

## Configuration Management

### Environment Setup
1. Copy `.env-example` to `.env`
2. Configure database credentials for `eazy_wallet` user
3. Set up AWS credentials and Cognito pool details
4. Configure Redis cluster settings
5. Set New Relic license key for monitoring

### Database Setup
Execute the SQL commands from README.md to create the MySQL user and database:
```sql
CREATE USER 'eazy_wallet'@'%' IDENTIFIED WITH mysql_native_password BY '123456';
GRANT ALL PRIVILEGES ON *.* to 'eazy_wallet'@'%';
CREATE DATABASE eazy_wallet_db;
```

### Key Configuration Files
- `.env` - Environment variables for all services
- `config/sequelize.js` - Database connection configuration
- `src/main.ts` - Application bootstrap and middleware setup
- `nest-cli.json` - NestJS CLI configuration with Swagger plugins

## Development Patterns

### Module Structure
Each feature module follows NestJS conventions:
- **Controllers**: Handle HTTP requests and responses
- **Services**: Business logic implementation  
- **Entities**: Database models using Sequelize decorators
- **DTOs**: Data transfer objects with validation
- **Repositories**: Data access layer abstraction

### Authentication & Authorization
- Global AuthGuard validates JWT tokens on all routes
- Role-based access control using decorators
- User context available via CLS (Continuation Local Storage)
- Idempotency keys for API safety

### Error Handling
- Global HttpExceptionFilter formats all errors consistently
- Custom error codes defined in `commons/error/error.codes.ts`
- Validation pipes automatically validate DTOs
- New Relic error tracking integration

### Testing Strategy
- Unit tests for services and utilities
- E2E tests with dedicated test environment
- Mock external dependencies (AWS, databases)
- Coverage reporting with Jest
