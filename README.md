<p align="center">
    <a href="https://ew.io/" target="blank"><img src="https://github.com/ew-platform/ew-branding/blob/main/src/img/branding/ew_logo.png" width="320" alt="Ew Platform" /></a>
</p>

<div align="center">
<h2>Eazy Wallet </h2>
</div>

<div align="center">
  <h4>
    <a href="https://ew.io/">
      Website
    </a>
    <span> | </span>
    <a href="#">
      Product Docs
    </a>
    <span> | </span>
    <a href="#">
      Architecture Docs
    </a>
    <span> | </span>
    <!-- <a href="#"> -->
    <!--   CLI -->
    <!-- </a> -->
    <!-- <span> | </span> -->
    <a href="#/CONTRIBUTING.md">
      Contributing
    </a>
    <span> | </span>
    <a href="https://twitter.com/ewio">
      Twitter
    </a>
    <span> | </span>
    <a href="https://t.me/ewio">
      Telegram
    </a>
    <span> | </span>
    <a href="https://discord.gg/fbyns8Egpb">
      Discourd
    </a>
    <span> | </span>
    <a href="https://ewio.medium.com/">
      Medium
    </a>
    <span> | </span>
    <a href="https://www.reddit.com/r/ewio">
      Reddit
    </a>
  </h4>
</div>

<h3> Table of Contents </h3>

- [1. Introduction](#1-introduction)
- [Deployment Specification](#deployment-specification)
  - [Databsae Schema and Seeding](#databsae-schema-and-seeding)



# 1. Introduction


# Deployment Specification
## Databsae Schema and Seeding

**Databsae Schema**

Run the following sql scripts as root user to create the wallet-service mysql user and db schema on your mysql instance:

```SQL
CREATE USER eazy_wallet'@'%' IDENTIFIED WITH mysql_native_password BY '123456';
GRANT ALL PRIVILEGES ON *.* to eazy_wallet'@'%';
flush privileges;
CREATE DATABASE eazy_wallet_db;
commit;

-- in my sqlworjbenck 
CREATE USER 'eazy_wallet'@'%' IDENTIFIED WITH mysql_native_password BY '123456';
GRANT ALL PRIVILEGES ON *.* to 'eazy_wallet'@'%';
flush privileges;
CREATE DATABASE eazy_wallet_db;
commit;
```

Adjust the following configuration properly in your `.env` file:

```
MYSQLDB_HOST=localhost
MYSQLDB_ROOT_USER=root
MYSQLDB_ROOT_PASSWORD=password
MYSQLDB_WODO_USER=eazy_wallet
MYSQLDB_WODO_PASSWORD=123456
MYSQLDB_WODO_DATABASE=eazy_wallet_db
MYSQLDB_LOCAL_PORT=3336
MYSQLDB_DOCKER_PORT=3306
MYSQLDB_CONNECTION_POOL_MAX=5
MYSQLDB_CONNECTION_POOL_MIN=0
MYSQLDB_CONNECTION_POOL_ACQUIRE=30000
MYSQLDB_CONNECTION_POOL_IDLE=10000
```