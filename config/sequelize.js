require('dotenv').config();

module.exports = {
  development: {
    username: process.env.MYSQLDB_WODO_USER,
    password: process.env.MYSQLDB_WODO_PASSWORD,
    database: process.env.MYSQLDB_WODO_DATABASE,
    host: process.env.MYSQLDB_HOST,
    port: process.env.MYSQLDB_LOCAL_PORT || 3306,
    dialect: 'mysql',
    dialectOptions: {
      bigNumberStrings: true
    },
    pool: {
      max: parseInt(process.env.MYSQLDB_CONNECTION_POOL_MAX || '5'),
      min: parseInt(process.env.MYSQLDB_CONNECTION_POOL_MIN || '0'),
      acquire: parseInt(process.env.MYSQLDB_CONNECTION_POOL_ACQUIRE || '30000'),
      idle: parseInt(process.env.MYSQLDB_CONNECTION_POOL_IDLE || '10000')
    }
  },
  test: {
    username: process.env.MYSQLDB_WODO_USER,
    password: process.env.MYSQLDB_WODO_PASSWORD,
    database: process.env.MYSQLDB_WODO_DATABASE,
    host: process.env.MYSQLDB_HOST,
    port: process.env.MYSQLDB_LOCAL_PORT || 3306,
    dialect: 'mysql',
    dialectOptions: {
      bigNumberStrings: true
    }
  },
  production: {
    username: process.env.MYSQLDB_WODO_USER,
    password: process.env.MYSQLDB_WODO_PASSWORD,
    database: process.env.MYSQLDB_WODO_DATABASE,
    host: process.env.MYSQLDB_HOST,
    port: process.env.MYSQLDB_LOCAL_PORT || 3306,
    dialect: 'mysql',
    dialectOptions: {
      bigNumberStrings: true
    },
    logging: false,
    pool: {
      max: parseInt(process.env.MYSQLDB_CONNECTION_POOL_MAX || '5'),
      min: parseInt(process.env.MYSQLDB_CONNECTION_POOL_MIN || '0'),
      acquire: parseInt(process.env.MYSQLDB_CONNECTION_POOL_ACQUIRE || '30000'),
      idle: parseInt(process.env.MYSQLDB_CONNECTION_POOL_IDLE || '10000')
    }
  }
}; 