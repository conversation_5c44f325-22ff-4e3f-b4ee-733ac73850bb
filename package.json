{"name": "@eazy-wallet/eazy-wallet", "version": "1.0.0", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"prebuild": "<PERSON><PERSON><PERSON> dist", "build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "NEW_RELIC_CONFIG_FILENAME=dist/commons/o11y/newrelic.js nest start -e 'node -r newrelic -r source-map-support/register'", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch -e 'node -r newrelic -r source-map-support/register'", "start:prod": "NEW_RELIC_CONFIG_FILENAME=dist/commons/o11y/newrelic.js node -r newrelic -r source-map-support/register dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "generate:graphql": "graphql-codegen"}, "dependencies": {"@aws-sdk/client-cloudwatch": "^3.873.0", "@aws-sdk/client-cognito-identity-provider": "^3.873.0", "@aws-sdk/client-kms": "^3.873.0", "@aws-sdk/client-s3": "^3.873.0", "@aws-sdk/client-secrets-manager": "^3.873.0", "@aws-sdk/smithy-client": "^3.374.0", "@nestjs/axios": "^4.0.0", "@nestjs/common": "^11.0.10", "@nestjs/config": "^4.0.0", "@nestjs/core": "^11.0.10", "@nestjs/event-emitter": "^3.0.0", "@nestjs/microservices": "^11.0.10", "@nestjs/mongoose": "^11.0.1", "@nestjs/platform-express": "^11.0.1", "@nestjs/schedule": "^6.0.0", "@nestjs/sequelize": "11.0.0", "@nestjs/swagger": "^11.0.4", "@nestjs/terminus": "^11.0.0", "@urql/core": "^5.1.1", "aws-jwt-verify": "^5.1.0", "axios": "^1.7.7", "axios-retry": "^4.5.0", "cassandra-driver": "^4.7.2", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "cron-parser": "^5.3.0", "dotenv": "^16.4.5", "graphql": "^16.10.0", "http-status-codes": "^2.3.0", "ioredis": "^5.4.1", "jsonwebtoken": "^9.0.2", "jwk-to-pem": "^2.0.7", "jwt-decode": "^4.0.0", "mongoose": "^8.8.1", "mysql2": "^3.7.0", "nanoid": "^3.3.4", "nest-winston": "^1.10.2", "nestjs-cls": "^5.4.0", "newrelic": "^12.16.1", "reflect-metadata": "^0.2.1", "rimraf": "5.0.5", "rxjs": "^7.8.1", "sequelize": "6.37.7", "sequelize-cli": "^6.6.3", "sequelize-transactional-decorator": "^1.0.1", "sequelize-typescript": "^2.1.6", "source-map-support": "^0.5.20", "ulidx": "^2.4.1", "undici": "^6.6.0", "unique-names-generator": "^4.7.1", "unique-username-generator": "^1.5.1", "winston": "3.17.0", "winston-daily-rotate-file": "^5.0.0"}, "devDependencies": {"@nestjs/cli": "^11.0.4", "@nestjs/schematics": "^11.0.1", "@nestjs/testing": "^11.0.10", "@types/express": "^4.17.13", "@types/jest": "29.5.1", "@types/jsonwebtoken": "^9.0.10", "@types/newrelic": "^9.14.8", "@types/node": "^20.17.28", "@types/supertest": "^2.0.11", "@typescript-eslint/eslint-plugin": "^5.0.0", "@typescript-eslint/parser": "^5.0.0", "eslint": "^8.0.1", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0", "jest": "29.5.0", "jest-mock-extended": "^4.0.0-beta1", "mongodb-memory-server": "^10.1.2", "prettier": "^2.3.2", "redis-memory-server": "^0.11.0", "supertest": "^6.1.3", "ts-jest": "29.1.0", "ts-loader": "^9.2.3", "ts-mockito": "^2.6.1", "ts-node": "^10.0.0", "tsconfig-paths": "4.2.0", "typescript": "^5.0.0"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}