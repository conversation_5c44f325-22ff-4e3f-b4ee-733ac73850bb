-- Active: 1725185159688@@127.0.0.1@3306@eazy_wallet_db
-- Complete setup script for Eazy Wallet Database
-- Run this script as MySQL root user to set up everything at once
-- 
-- Usage:
--   mysql -u root -p < db/mysql/00-setup-complete.sql
-- 
-- Environment Variables Referenced:
--   MYSQLDB_WODO_USER=eazy_wallet
--   MYSQLDB_WODO_PASSWORD=123456
--   MYSQLDB_WODO_DATABASE=eazy_wallet_db

-- =====================================================
-- 1. CREATE DATABASE
-- =====================================================

-- Create the database if it doesn't exist
CREATE DATABASE IF NOT EXISTS `eazy_wallet_db` 
CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci;

-- =====================================================
-- 2. CREATE USER AND GRANT PERMISSIONS
-- =====================================================

-- Create the user if it doesn't exist (for remote and local connections)
CREATE USER IF NOT EXISTS 'eazy_wallet'@'%' IDENTIFIED BY '123456';
CREATE USER IF NOT EXISTS 'eazy_wallet'@'localhost' IDENTIFIED BY '123456';

-- Grant all privileges on the eazy_wallet_db database
GRANT ALL PRIVILEGES ON `eazy_wallet_db`.* TO 'eazy_wallet'@'%';
GRANT ALL PRIVILEGES ON `eazy_wallet_db`.* TO 'eazy_wallet'@'localhost';

-- Grant specific privileges for database operations
GRANT SELECT, INSERT, UPDATE, DELETE, CREATE, DROP, INDEX, ALTER, 
      CREATE TEMPORARY TABLES, LOCK TABLES, EXECUTE, CREATE VIEW, SHOW VIEW, 
      CREATE ROUTINE, ALTER ROUTINE, EVENT, TRIGGER 
ON `eazy_wallet_db`.* TO 'eazy_wallet'@'%';

GRANT SELECT, INSERT, UPDATE, DELETE, CREATE, DROP, INDEX, ALTER, 
      CREATE TEMPORARY TABLES, LOCK TABLES, EXECUTE, CREATE VIEW, SHOW VIEW, 
      CREATE ROUTINE, ALTER ROUTINE, EVENT, TRIGGER 
ON `eazy_wallet_db`.* TO 'eazy_wallet'@'localhost';

-- Refresh privileges
FLUSH PRIVILEGES;

-- =====================================================
-- 3. VERIFICATION
-- =====================================================

-- Select the database to verify it exists
USE `eazy_wallet_db`;

-- Show database info
SELECT 
    'Setup completed successfully!' AS status,
    DATABASE() AS current_database,
    @@character_set_database AS charset,
    @@collation_database AS collation;

-- Show user privileges
SHOW GRANTS FOR 'eazy_wallet'@'localhost';
SHOW GRANTS FOR 'eazy_wallet'@'%';

-- Show final confirmation
SELECT 
    'eazy_wallet_db' AS database_name,
    'eazy_wallet' AS username,
    'Ready for Sequelize migrations!' AS next_steps;
