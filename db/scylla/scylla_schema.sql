CREATE TABLE IF NOT EXISTS audit_logs (
    id text,
    method int,
    ip_address text,
    path text,
    query_parameters text,
    headers text,
    request_data text,
    result text,
    status_code text,
    error_code text,
    duration int,
    created_at timestamp,
    tenant_id   text,
    user_id text,
    PRIMARY KEY (tenant_id ,user_id, id)
 );

 CREATE TABLE IF NOT EXISTS idempotent_api_records (
    id text,
    method int,
    ip_address text,
    path text,
    query_parameters text,
    headers text,
    request_data text,
    result text,
    status_code text,
    created_at timestamp,
    tenant_id   text,
    user_id text,
    PRIMARY KEY (tenant_id , id, path, method)
 );