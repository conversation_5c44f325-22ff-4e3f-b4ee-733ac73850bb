#### APP Configurations ####
 
# prod or development. logs are formatted in json for prod
APP_ENV=dev
# error, warn, info, debug, silly . Default value is info
LOG_LEVEL=info
# console or file. Default value is console
LOG_STREAM=console
# if not provided, the default is ${service_home}/dist/src/logs
LOG_STREAM_FILE_DIR=/tmp/eazy-wallet/logs
# if not provided the defauls is 10mb. Maximum size of the file after which it will rotate. This can be a number of bytes, or units of kb, mb, and gb. If using the units, add 'k', 'm', or 'g' as the suffix. The units need to directly follow the number.
LOG_STREAM_FILE_MAX_SIZE=10m
#Maximum number of logs to keep. If not set, no logs will be removed. This can be a number of files or number of days. If using days, add 'd' as the suffix.
LOG_STREAM_FILE_MAX=14d

WALLET_SERVICE_URL=http://localhost:3002/wallet/api/v1
IAM_SERVICE_URL=http://localhost:3007/identity/api/v1

AUDIT_LOGGER_REPOSITORY=mongo # Use "scylla" or "mongo"
AUDIT_LOG_PER_PAGE=50

APPLICATION_PER_PAGE=50
APPLICATION_LOG_PER_PAGE=50
ENTITY_PER_PAGE=50
DISTRIBUTED_TRACE_PER_PAGE=50
DISTRIBUTED_TRACE_SUMMARY_PER_PAGE=50

IDEMPOTENT_API_RECORD_REPOSITORY=mongo # Use "scylla" or "mongo"
IDEMPOTENCY_CACHE_EXPIRE_TIME=86400000 # 24 hours
IDEMPOTENCY_CACHE_KEY_PREFIX=ipk
IDEMPOTENT_API_RECORD_PAGE=50

NODE_LOCAL_PORT=3033

AWS_APIGW_AUTH_LAMBDA_NAME=ew-iam-app-lambda-auth-jwt-cognito
AWS_APIGW_AUTH_LAMBDA_AUTHORIZER_URI=arn:aws:apigateway:eu-central-1:lambda:path/2015-03-31/functions/arn:aws:lambda:eu-central-1:158907794445:function:ew-iam-application-JwtAuthorizerFunction-djwriLMpSAra/invocations
AWS_APIGW_AUTH_LAMBDA_AUTHORIZER_TTL_SECONDS=300
AWS_APIGW_INTEGRATION_URI=http://ec2-3-127-110-168.eu-central-1.compute.amazonaws.com:8107
AWS_APIGW_INTEGRATION_URI_NOTIFICATIONS=http://ec2-3-127-110-168.eu-central-1.compute.amazonaws.com:8107
AWS_APIGW_INTEGRATION_URI_WALLET=http://ec2-3-127-110-168.eu-central-1.compute.amazonaws.com:8107
AWS_APIGW_INTEGRATION_URI_TRANSACTIONS=http://ec2-3-127-110-168.eu-central-1.compute.amazonaws.com:8107
AWS_APIGW_INTEGRATION_URI_AUTH=http://ec2-3-127-110-168.eu-central-1.compute.amazonaws.com:8107
AWS_APIGW_INTEGRATION_URI_BLOCKCHAIN_NODES=http://ec2-3-127-110-168.eu-central-1.compute.amazonaws.com:8107

#MySql DB
MYSQLDB_HOST=localhost
MYSQLDB_ROOT_USER=root
MYSQLDB_ROOT_PASSWORD=password
MYSQLDB_WODO_USER=eazy_wallet
MYSQLDB_WODO_PASSWORD=123456
MYSQLDB_WODO_DATABASE=eazy_wallet_db
MYSQLDB_LOCAL_PORT=3306
MYSQLDB_DOCKER_PORT=3306
MYSQLDB_RECREATE_DB_SCHEMA=true
DB_SEED=false
MYSQLDB_CONNECTION_POOL_MAX=5
MYSQLDB_CONNECTION_POOL_MIN=0
MYSQLDB_CONNECTION_POOL_ACQUIRE=30000
MYSQLDB_CONNECTION_POOL_IDLE=10000

#Mongo DB
# local, development or prod.
MONGODB_ENV=local
MONGODB_USER=user
MONGODB_PASSWORD=pass
MONGODB_HOST=localhost
MONGODB_PORT=27017
MONGODB_DATABASE=campaign_results
# the maximum number of sockets the MongoDB driver will keep open for this connection
MONGODB_POOL_SIZE=10 
MONGODB_SSL=true
MONGODB_RETRY_WRITES=true
# Sets the write concern
MONGODB_W=majority

### Scylla DB

SCYLLA_NODE_IPS=localhost
SCYLLA_DATA_CENTER=datacenter1
SCYLLA_USERNAME=
SCYLLA_PASSWORD=
SCYLLA_KEY_SPACE=ew_keyspace

### Redis
REDIS_HOST=127.0.0.1
REDIS_PORT=6378
REDIS_USERNAME=
REDIS_PASSWORD=
REDIS_CLUSTER_ENABLED=true
REDIS_CONNECT_TIME_OUT=10000
REDIS_COMMAND_TIME_OUT=10000
# only dev purpose when the redis cluster is deployed in docker
REDIS_NAT_MAP=**********:6379:127.0.0.1:6379,**********:6379:127.0.0.1:6380,**********:6379:127.0.0.1:6381,**********:6379:127.0.0.1:6382,**********:6379:127.0.0.1:6383,**********:6379:127.0.0.1:6384



NEW_RELIC_APP_NAME=eazy-wallet-dev
NEW_RELIC_LICENSE_KEY=
NEW_RELIC_LOG_LEVEL=info
NEW_RELIC_LOG=newrelic_agent.log
NEW_RELIC_CODE_LEVEL_METRICS_ENABLED=false
NEW_RELIC_LABELS=Team: Platform;Environment:Staging;Application:API GW Service
NEW_RELIC_AGENT_ENABLED=false
NEW_RELIC_DISTRIBUTED_TRACING_ENABLED=false

NEW_RELIC_API_KEY=xxxx
NEW_RELIC_API_BASE_URL=https://api.eu.newrelic.com/v2
NEW_RELIC_GRAPHQL_API_URL=https://api.eu.newrelic.com/graphql
NEW_RELIC_API_APPLICATION_ID=*********
NEW_RELIC_ACCOUNT_ID=4267066

AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_COGNITO_USER_POOL_ID=
AWS_COGNITO_CLIENT_ID=
AWS_COGNITO_CLIENT_SECRET=

COGNITO_CALLBACK_DOMAIN=
COGNITO_HOSTED_UI_URL=
AWS_REGION=
COGNITO_SCOPE=email+openid+profile
COGNITO_APPLE_SCOPE=email openid
COGNITO_GOOGLE_SCOPE=email openid profile
GOOGLE_CLIENT_ID=
