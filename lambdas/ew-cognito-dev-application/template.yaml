# This AWS SAM template has been generated from your function's configuration. If
# your function has one or more triggers, note that the AWS resources associated
# with these triggers aren't fully specified in this template and include
# placeholder values. Open this template in AWS Infrastructure Composer or your
# favorite IDE and modify it to specify a serverless application with other AWS
# resources.
AWSTemplateFormatVersion: '2010-09-09'
Transform: AWS::Serverless-2016-10-31
Description: An AWS Serverless Application Model template describing your function.
Resources:
  ewcognitodevapplicationCognitoPostConfirmation:
    Type: AWS::Serverless::Function
    Properties:
      CodeUri: ./cognito-post-confirmation
      Description: ''
      MemorySize: 512
      Timeout: 30
      Handler: app.lambdaHandler
      Runtime: nodejs20.x
      Architectures:
        - x86_64
      EphemeralStorage:
        Size: 512
      Environment:
        Variables:
          KEYSPACE_HOST: cassandra.eu-central-1.amazonaws.com
          SKIP_CAPTCHA_PER_CLIENTS: 6e695okue55dpm1vk69a4tn21j
          KEYSPACE_USER: ew-user-at-158907794445
          SKIP_EMAIL_CHECK: 'true'
          SKIP_IPCHECK_PER_CLIENTS: 6e695okue55dpm1vk69a4tn21j
          IAM_SERVICE_ENDPOINT: https://equal-sparrow-eminent.ngrok-free.app
          KEYSPACE_PASS: TVQxt5tgOBJR2B3eBL0uxXbv9dGbs98p7O4jFH1oztKl7dmJOWiZQuQlb7s=
          IAM_SERVICE_DEFAULT_AVATAR: https://static.wodo.io/general/wodo-icon.png
          DISABLE_CAPTCHA_CHECK: 'true'
          IAM_SERVICE_ENDPOINT_PORT: '443'
          RECAPTCHA_SERVER_KEY: 6Lf98Y0fAAAAAGFMdpTR1sIm9uGMhUuC2oPEfF-s
      EventInvokeConfig:
        MaximumEventAgeInSeconds: 21600
        MaximumRetryAttempts: 2
      PackageType: Zip
      Policies:
        - Statement:
            - Sid: VisualEditor0
              Effect: Allow
              Action:
                - ses:SendEmail
                - ses:SendTemplatedEmail
                - ses:SendBulkTemplatedEmail
                - ses:SendBounce
              Resource: '*'
            - Effect: Allow
              Action:
                - xray:PutTraceSegments
                - xray:PutTelemetryRecords
              Resource:
                - '*'
            - Effect: Allow
              Action:
                - cognito-identity:*
                - cognito-idp:*
                - cognito-sync:*
                - iam:ListRoles
                - iam:ListOpenIdConnectProviders
                - iam:GetRole
                - iam:ListSAMLProviders
                - iam:GetSAMLProvider
                - kinesis:ListStreams
                - lambda:GetPolicy
                - lambda:ListFunctions
                - sns:GetSMSSandboxAccountStatus
                - sns:ListPlatformApplications
                - ses:ListIdentities
                - ses:GetIdentityVerificationAttributes
                - mobiletargeting:GetApps
                - acm:ListCertificates
                - sms-voice:DescribeAccountAttributes
              Resource: '*'
            - Effect: Allow
              Action:
                - iam:CreateServiceLinkedRole
              Resource: '*'
              Condition:
                StringEquals:
                  iam:AWSServiceName:
                    - cognito-idp.amazonaws.com
                    - email.cognito-idp.amazonaws.com
            - Effect: Allow
              Action:
                - iam:DeleteServiceLinkedRole
                - iam:GetServiceLinkedRoleDeletionStatus
              Resource:
                - >-
                  arn:aws:iam::*:role/aws-service-role/cognito-idp.amazonaws.com/AWSServiceRoleForAmazonCognitoIdp*
                - >-
                  arn:aws:iam::*:role/aws-service-role/email.cognito-idp.amazonaws.com/AWSServiceRoleForAmazonCognitoIdpEmail*
            - Sid: AWSLambdaVPCAccessExecutionPermissions
              Effect: Allow
              Action:
                - logs:CreateLogGroup
                - logs:CreateLogStream
                - logs:PutLogEvents
                - ec2:CreateNetworkInterface
                - ec2:DescribeNetworkInterfaces
                - ec2:DescribeSubnets
                - ec2:DeleteNetworkInterface
                - ec2:AssignPrivateIpAddresses
                - ec2:UnassignPrivateIpAddresses
              Resource: '*'
            - Effect: Allow
              Action:
                - ec2:DescribeRouteTables
                - ec2:CreateRoute
                - ec2:DeleteRoute
                - ec2:ReplaceRoute
              Resource:
                - '*'
            - Effect: Allow
              Action:
                - ec2:DescribeNetworkInterfaces
                - ec2:CreateNetworkInterface
                - ec2:DeleteNetworkInterface
                - ec2:CreateNetworkInterfacePermission
                - ec2:DeleteNetworkInterfacePermission
                - ec2:DescribeNetworkInterfacePermissions
                - ec2:ModifyNetworkInterfaceAttribute
                - ec2:DescribeNetworkInterfaceAttribute
                - ec2:DescribeAvailabilityZones
                - ec2:DescribeRegions
                - ec2:DescribeVpcs
                - ec2:DescribeSubnets
              Resource:
                - '*'
            - Effect: Allow
              Action:
                - ec2:AssignPrivateIpAddresses
                - ec2:UnassignPrivateIpAddresses
              Resource:
                - '*'
            - Effect: Allow
              Action:
                - ec2:AssignIpv6Addresses
                - ec2:UnassignIpv6Addresses
              Resource:
                - '*'
            - Effect: Allow
              Action:
                - logs:CreateLogGroup
                - logs:CreateLogStream
                - logs:PutLogEvents
              Resource: '*'
            - Effect: Allow
              Action:
                - execute-api:Invoke
                - execute-api:ManageConnections
              Resource: arn:aws:execute-api:*:*:*
      SnapStart:
        ApplyOn: None
      Tags:
      VpcConfig:
        SecurityGroupIds:
          - sg-0cc5ccd70ee988d5d
        SubnetIds:
          - subnet-053bea342b7348869
          - subnet-02577e50e30f7f89d
          - subnet-097fc884b1ececc19
        Ipv6AllowedForDualStack: false
      RuntimeManagementConfig:
        UpdateRuntimeOn: Auto
    Metadata: # Manage esbuild properties
      BuildMethod: esbuild
      BuildProperties:
        Minify: true
        Target: "es2020"
        Platform: "node"
        # Sourcemap: true # Enabling source maps will create the required NODE_OPTIONS environment variables on your lambda function during sam build
        EntryPoints: 
        - app.ts
