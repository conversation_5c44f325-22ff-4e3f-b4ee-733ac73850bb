AWSTemplateFormatVersion: '2010-09-09'
Transform: AWS::Serverless-2016-10-31

Description: 'Eazy Wallet - Local User Pool with Google and Apple Identity Providers'

Parameters:
  Environment:
    Type: String
    Default: local
    Description: Environment name
  
  GoogleClientSecret:
    Type: String
    Description: Google OAuth Client Secret
    NoEcho: true
    Default: "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"
  
  ApplePrivateKey:
    Type: String
    Description: Apple Sign-in Private Key (p8 file content)
    NoEcho: true
*************************************************************************************************************************************************************************************************************************************************************************************

Resources:
  # User Pool
  LocalUserPool:
    Type: AWS::Cognito::UserPool
    Properties:
      UserPoolName: ew-users-pool-local
      DeletionProtection: INACTIVE  # Changed from ACTIVE for local development
      
      # Password Policy (copied from current pool)
      Policies:
        PasswordPolicy:
          MinimumLength: 8
          RequireUppercase: true
          RequireLowercase: true
          RequireNumbers: true
          RequireSymbols: true
          TemporaryPasswordValidityDays: 7
      
      # Schema - All attributes set to NOT required for easier Google/Apple integration
      Schema:
        - Name: email
          AttributeDataType: String
          Required: false  # Changed from required to avoid Google OAuth issues
          Mutable: true
          StringAttributeConstraints:
            MinLength: 0
            MaxLength: 2048
        
        - Name: given_name
          AttributeDataType: String
          Required: false
          Mutable: true
          StringAttributeConstraints:
            MinLength: 0
            MaxLength: 2048
        
        - Name: family_name
          AttributeDataType: String
          Required: false
          Mutable: true
          StringAttributeConstraints:
            MinLength: 0
            MaxLength: 2048
        
        - Name: name
          AttributeDataType: String
          Required: false
          Mutable: true
          StringAttributeConstraints:
            MinLength: 0
            MaxLength: 2048
        
        - Name: phone_number
          AttributeDataType: String
          Required: false
          Mutable: true
          StringAttributeConstraints:
            MinLength: 0
            MaxLength: 2048
        
        # Custom attributes from your current pool
        - Name: mew
          AttributeDataType: String
          Mutable: true
          StringAttributeConstraints: {}
        
        - Name: nw
          AttributeDataType: String
          Mutable: true
          StringAttributeConstraints: {}
        
        - Name: phone_verified_at
          AttributeDataType: String
          Mutable: true
          StringAttributeConstraints: {}
        
        - Name: email_verified_at
          AttributeDataType: String
          Mutable: true
          StringAttributeConstraints: {}
        
        - Name: user_id
          AttributeDataType: String
          Mutable: true
          StringAttributeConstraints: {}
        
        - Name: referral_code
          AttributeDataType: String
          Mutable: true
          StringAttributeConstraints: {}
        
        - Name: phone_num
          AttributeDataType: String
          Mutable: true
          StringAttributeConstraints: {}
      
      # Auto verification and username attributes
      AutoVerifiedAttributes:
        - email
      
      UsernameAttributes:
        - email
      
      UsernameConfiguration:
        CaseSensitive: false
      
      # Verification settings
      VerificationMessageTemplate:
        DefaultEmailOption: CONFIRM_WITH_CODE
      
      UserAttributeUpdateSettings:
        AttributesRequireVerificationBeforeUpdate: []
      
      # MFA Configuration
      MfaConfiguration: "OFF"
      
      # Email Configuration
      EmailConfiguration:
        EmailSendingAccount: COGNITO_DEFAULT
      
      # Admin create user settings
      AdminCreateUserConfig:
        AllowAdminCreateUserOnly: false
      
      # Account Recovery
      AccountRecoverySetting:
        RecoveryMechanisms:
          - Priority: 1
            Name: verified_email
          - Priority: 2
            Name: verified_phone_number
      
      # Lambda triggers (reference your existing Lambda function)
      LambdaConfig:
        PreSignUp: !Sub "arn:aws:lambda:${AWS::Region}:${AWS::AccountId}:function:ew-cognito-dev-applicatio-ewcognitodevapplicationC-yzc6ybBP4iFr"
        PostConfirmation: !Sub "arn:aws:lambda:${AWS::Region}:${AWS::AccountId}:function:ew-cognito-dev-applicatio-ewcognitodevapplicationC-yzc6ybBP4iFr"

  # Google Identity Provider
  GoogleIdentityProvider:
    Type: AWS::Cognito::UserPoolIdentityProvider
    Properties:
      UserPoolId: !Ref LocalUserPool
      ProviderName: Google
      ProviderType: Google
      
      ProviderDetails:
        client_id: "************-4dsmuso58thpi0fccqlee1e9snm7258c.apps.googleusercontent.com"
        client_secret: !Ref GoogleClientSecret
        authorize_scopes: "profile email openid"
        authorize_url: "https://accounts.google.com/o/oauth2/v2/auth"
        token_url: "https://www.googleapis.com/oauth2/v4/token"
        attributes_url: "https://people.googleapis.com/v1/people/me?personFields="
        attributes_url_add_attributes: "true"
        oidc_issuer: "https://accounts.google.com"
        token_request_method: "POST"
      
      AttributeMapping:
        email: email
        email_verified: email_verified
        family_name: family_name
        name: given_name
        phone_number: expires_in  # Note: This mapping from your current config
        username: sub

  # Apple Identity Provider  
  AppleIdentityProvider:
    Type: AWS::Cognito::UserPoolIdentityProvider
    Properties:
      UserPoolId: !Ref LocalUserPool
      ProviderName: SignInWithApple
      ProviderType: SignInWithApple
      
      ProviderDetails:
        client_id: "com.ezwallet"
        team_id: "AH277897AV"
        key_id: "TXR687JR5C"
        private_key: !Ref ApplePrivateKey
        authorize_scopes: "email name"
        authorize_url: "https://appleid.apple.com/auth/authorize"
        token_url: "https://appleid.apple.com/auth/token"
        attributes_url_add_attributes: "false"
        oidc_issuer: "https://appleid.apple.com"
        token_request_method: "POST"
      
      AttributeMapping:
        email: email
        email_verified: email_verified
        name: name
        phone_number: name  # Note: This unusual mapping from your current config
        username: sub

  # User Pool Domain
  UserPoolDomain:
    Type: AWS::Cognito::UserPoolDomain
    Properties:
      Domain: !Sub "ew-local-${AWS::AccountId}"
      UserPoolId: !Ref LocalUserPool

  # App Client
  UserPoolAppClient:
    Type: AWS::Cognito::UserPoolClient
    Properties:
      UserPoolId: !Ref LocalUserPool
      ClientName: !Sub "eazy-wallet-local-${Environment}"
      GenerateSecret: true  # Keeping secret generation as in your current config
      
      # Token validity (copied from your current config)
      RefreshTokenValidity: 5
      AccessTokenValidity: 60
      IdTokenValidity: 60
      TokenValidityUnits:
        AccessToken: minutes
        IdToken: minutes
        RefreshToken: days
      
      # Identity Providers
      SupportedIdentityProviders:
        - COGNITO
        - Google
        - SignInWithApple
      
      # OAuth Settings
      CallbackURLs:
        - "https://equal-sparrow-eminent.ngrok-free.app/users/landing"
        - "http://localhost:3000/users/landing"  # Added for local development
      
      LogoutURLs:
        - "https://equal-sparrow-eminent.ngrok-free.app/logout"
        - "http://localhost:3000/logout"  # Added for local development
      
      AllowedOAuthFlows:
        - code
        - implicit  # Added implicit for easier testing
      
      AllowedOAuthScopes:
        - email
        - openid
        - profile
      
      AllowedOAuthFlowsUserPoolClient: true
      
      # Security settings
      PreventUserExistenceErrors: ENABLED
      EnableTokenRevocation: true
      EnablePropagateAdditionalUserContextData: false
      AuthSessionValidity: 3
      
      # Attribute permissions - All readable/writable for easier development
      ReadAttributes:
        - email
        - email_verified
        - given_name
        - family_name
        - name
        - phone_number
        - phone_number_verified
        - custom:mew
        - custom:nw
        - custom:phone_verified_at
        - custom:email_verified_at
        - custom:user_id
        - custom:referral_code
      
      WriteAttributes:
        - email
        - given_name
        - family_name
        - name
        - phone_number
        - custom:mew
        - custom:nw
        - custom:phone_verified_at
        - custom:email_verified_at
        - custom:user_id
        - custom:referral_code
    
    DependsOn:
      - GoogleIdentityProvider
      - AppleIdentityProvider

Outputs:
  UserPoolId:
    Description: "Local User Pool ID"
    Value: !Ref LocalUserPool
    Export:
      Name: !Sub "${AWS::StackName}-UserPoolId"
  
  UserPoolClientId:
    Description: "Local User Pool App Client ID"
    Value: !Ref UserPoolAppClient
    Export:
      Name: !Sub "${AWS::StackName}-UserPoolClientId"
  
  UserPoolClientSecret:
    Description: "Local User Pool App Client Secret"
    Value: !GetAtt UserPoolAppClient.ClientSecret
    Export:
      Name: !Sub "${AWS::StackName}-UserPoolClientSecret"
  
  HostedUIURL:
    Description: "Cognito Hosted UI URL"
    Value: !Sub "https://${UserPoolDomain}.auth.${AWS::Region}.amazoncognito.com"
    Export:
      Name: !Sub "${AWS::StackName}-HostedUIURL"
  
  GoogleSignInURL:
    Description: "Direct Google Sign-in URL"
    Value: !Sub "https://${UserPoolDomain}.auth.${AWS::Region}.amazoncognito.com/oauth2/authorize?client_id=${UserPoolAppClient}&response_type=code&scope=email%20openid%20profile&redirect_uri=https://equal-sparrow-eminent.ngrok-free.app/users/landing&identity_provider=Google"
  
  AppleSignInURL:
    Description: "Direct Apple Sign-in URL"  
    Value: !Sub "https://${UserPoolDomain}.auth.${AWS::Region}.amazoncognito.com/oauth2/authorize?client_id=${UserPoolAppClient}&response_type=code&scope=email%20openid&redirect_uri=https://equal-sparrow-eminent.ngrok-free.app/users/landing&identity_provider=SignInWithApple"
  
  # Environment variables for your application
  EnvironmentVariables:
    Description: "Environment variables to use in your application"
    Value: !Sub |
      COGNITO_USER_POOL_ID=${LocalUserPool}
      COGNITO_CLIENT_ID=${UserPoolAppClient}
      COGNITO_CLIENT_SECRET=${UserPoolAppClient.ClientSecret}
      COGNITO_HOSTED_UI_URL=https://${UserPoolDomain}.auth.${AWS::Region}.amazoncognito.com
      COGNITO_CALLBACK_DOMAIN=https://equal-sparrow-eminent.ngrok-free.app
      COGNITO_SCOPE=email openid profile
      COGNITO_APPLE_SCOPE=email openid
      COGNITO_GOOGLE_SCOPE=email openid profile
