#!/bin/bash

# Deploy Local User Pool Script
# This script deploys the new local user pool with Google and Apple identity providers

set -e

STACK_NAME="ew-cognito-local-pool"
REGION="eu-central-1"
TEMPLATE_FILE="user-pool-local-template.yaml"
PARAMETERS_FILE="local-parameters.json"

echo "🚀 Deploying Local User Pool..."
echo "Stack Name: $STACK_NAME"
echo "Region: $REGION"
echo "Template: $TEMPLATE_FILE"

# Validate template
echo "📝 Validating CloudFormation template..."
aws cloudformation validate-template \
  --template-body file://$TEMPLATE_FILE \
  --region $REGION

# Deploy stack
echo "☁️ Deploying CloudFormation stack..."
aws cloudformation deploy \
  --template-file $TEMPLATE_FILE \
  --stack-name $STACK_NAME \
  --parameter-overrides file://$PARAMETERS_FILE \
  --capabilities CAPABILITY_IAM \
  --region $REGION \
  --no-fail-on-empty-changeset

echo "✅ Deployment completed!"

# Get outputs
echo "📋 Stack Outputs:"
aws cloudformation describe-stacks \
  --stack-name $STACK_NAME \
  --region $REGION \
  --query 'Stacks[0].Outputs[*].{Key:OutputKey,Value:OutputValue}' \
  --output table

echo ""
echo "🔗 Important URLs:"
echo "Google Sign-in Test: https://equal-sparrow-eminent.ngrok-free.app/users/test-google-signin"
echo "Apple Sign-in Test: https://equal-sparrow-eminent.ngrok-free.app/users/test-apple-signin"

echo ""
echo "📝 Next Steps:"
echo "1. Update your .env file with the new User Pool ID and Client ID"
echo "2. Update your application configuration to use the new pool"
echo "3. Test Google and Apple sign-in flows"
echo "4. Migrate users from old pool if needed"
