{"name": "cognito-define-challenge", "version": "1.0.0", "description": "", "main": "dist/exec.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "build": "tsc", "deploy": "npm run package && aws lambda update-function-code --function-name cognito-define-challenge --zip-file fileb://function.zip", "package": "npm run build && zip -r function.zip dist node_modules package.json"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@aws-sdk/client-cognito-identity-provider": "^3.679.0"}, "devDependencies": {"@types/aws-lambda": "^8.10.145", "typescript": "^5.6.2"}}