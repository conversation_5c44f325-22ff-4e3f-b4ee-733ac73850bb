import { AdminCreateUserCommand, AdminSetUserPasswordCommand, CognitoIdentityProviderClient } from "@aws-sdk/client-cognito-identity-provider";

const cognitoClient = new CognitoIdentityProviderClient({ region: process.env.REGION });

async function createUser(userPoolId: string, username: string) {
  const adminCreateUserCommand = new AdminCreateUserCommand({
    UserPoolId: userPoolId,
    Username: username,
    MessageAction: "SUPPRESS",
    UserAttributes: [
      { Name: "email", Value: username },
      { Name: "email_verified", Value: "true" }
    ],
  });

  const { User } = await cognitoClient.send(adminCreateUserCommand);
  return User;
}


async function setUserPassword(userPoolId: string, username: string) {
  const command = new AdminSetUserPasswordCommand({
    UserPoolId: userPoolId,
    Username: username,
    Password: 'tempPass123!', // Temporary password, will be changed later
    Permanent: true
  });
  
  await cognitoClient.send(command);
}

export const handler = async (event: any) => {
  console.log('Define auth challenge event:', JSON.stringify(event, null, 2));

  event.response = event.response || {};
  
  // If user not found, create them
  if (event.request.userNotFound) {
    try {
      // Create the user with basic attributes
      // await createUser(event.userPoolId, event.userName);
      // await setUserPassword(event.userPoolId, event.userName);
      
      // Proceed with the challenge
      event.response.challengeName = 'CUSTOM_CHALLENGE';
      event.response.issueTokens = false;
      event.response.failAuthentication = false;
    } catch (error) {
      console.error('Error creating user:', error);
      event.response.issueTokens = false;
      event.response.failAuthentication = true;
    }
  } else {
    // Existing user flow
    const session = event.request.session || [];
    
    if (session.length === 0) {
      event.response.challengeName = 'CUSTOM_CHALLENGE';
      event.response.issueTokens = false;
      event.response.failAuthentication = false;
    } else {
      const lastChallenge = session[session.length - 1];
      if (lastChallenge.challengeResult === true) {
        event.response.issueTokens = true;
        event.response.failAuthentication = false;
      } else {
        event.response.issueTokens = false;
        event.response.failAuthentication = true;
      }
    }
  }

  return event;
};