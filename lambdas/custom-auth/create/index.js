// CREATE CHALLENGE LAMBDA

exports.handler = async (event) => {
  console.log('Create auth challenge event:', JSON.stringify(event, null, 2));

  const username = event.request.userAttributes.email || event.userName;
  console.log('Creating challenge for user:', username);

  event.response = event.response || {};

  const session = event.request.session || [];

  // If this is the first challenge or the previous challenge was successful
  if (session.length === 0 || session[session.length - 1].challengeResult === true) {
    // Proceed to create a new custom challenge
    const identities = JSON.parse(event.request.userAttributes.identities || '[]');
    const googleIdentity = identities.find(id => id.providerName === 'Google');

    if (!googleIdentity) {
      console.error('Google identity not found for user:', username);
      // TODO: handle this scenario

    }

    event.response.publicChallengeParameters = {
      challengeType: 'GOOGLE_TOKEN_VALIDATION',
      email: username,
    };

    event.response.privateChallengeParameters = {
      expectedGoogleId: googleIdentity ? googleIdentity.userId : null,
      email: username,
    };

    console.log('GOOD?');

  } else {
    console.error('Previous challenge failed!!!!!');
    // If previous challenge failed, fail authentication
    event.response.publicChallengeParameters = {};
    event.response.privateChallengeParameters = {};
  }

  console.log('response:', JSON.stringify(event.response, null, 2));

  return event;
};
