// VERIFY CHALLENGE LAMBDA
import { Routine } from "./routine";
import { AttributeType } from "@aws-sdk/client-cognito-identity-provider";
import { ulid } from "ulidx";

interface GoogleUserInfo {
    sub: string;
    email: string;
}

let rout: Routine;

exports.handler = async (event: any) => {
    let tenant_conf: any;
    let wodo_id = ulid();

    try {
        //check env variables for JSON'd useridpools
        tenant_conf = JSON.parse(`${process.env.IAM_SERVICE_TENANT_CONF}`);
    } catch (err) {
        //INFORM FOR BAD JSON
        console.log("JSON P ERROR");
        console.log(err);
    }

    console.log("Verify auth challenge event:", JSON.stringify(event, null, 2));
    rout = new Routine(process.env);

    let pl = event.userPoolId,
        tenantId = rout._check_for_tenant(pl),
        ep_url = `${process.env.IAM_SERVICE_ENDPOINT}:${process.env.IAM_SERVICE_ENDPOINT_PORT}`;

    // Always ensure event.response is initialized
    event.response = event.response || {};

    const challengeAnswer = event.request.challengeAnswer; // The Google ID token

    try {
        // const identities = JSON.parse(event.request.userAttributes.identities || "[]");

        //check this for other providers
        const isGoogle = "expectedGoogleId" in event.request.privateChallengeParameters;
        if (isGoogle) {
            // const googleUserInfo = await rout.verifyGoogleToken(challengeAnswer);
            const googleUserInfo = JSON.parse(challengeAnswer);
            console.log("Verified Google user info:", googleUserInfo);
            if (!googleUserInfo.email) {
                event.response.answerCorrect = false;
                return event;
            }
            /*             const googleIdentity = identities.find((id: any) => id.providerName === "Google");
            if (!googleIdentity) {
                console.error("No Google identity found for user:", event.userName);
                event.response.answerCorrect = false;
                return event;
            }
 */
            // console.log("Token validation result:", {
            //     // expectedGoogleId: googleIdentity.userId,
            //     receivedGoogleId: googleUserInfo.sub,
            //     expectedEmail: event.request.userAttributes.email,
            //     receivedEmail: googleUserInfo.email,
            //     // isValid,
            // });

            const user = await rout.findUserByEmail(googleUserInfo.email, event.userPoolId);
            if (user) {
                //console.log("USER FOUND " + email);
                // await rout.linkSocialAccount({
                //     userPoolId: event.userPoolId,
                //     cognitoUsername: user.Username,
                //     providerName: "Google",
                //     providerUserId: googleUserInfo.sub,
                // });

                // return the event to continue the signup process
                event.response.answerCorrect = true;
                return event;
            } else {
                const newUser = await rout.createUser({
                    userPoolId: event.userPoolId,
                    email: googleUserInfo.email,
                });

                if (!newUser) {
                    throw new Error("Failed to create user");
                }

                if (newUser.Username) {
                    await rout.linkSocialAccount({
                        userPoolId: event.userPoolId,
                        cognitoUsername: newUser.Username,
                        providerName: "Google",
                        providerUserId: googleUserInfo.sub,
                    });
    
                    await rout.addUserToGroup(event.userPoolId, newUser.Username, "wodo_user");
                } else {
                    throw new Error("Username is undefined");
                }

                //console.log("NEW USER CREATED", newUser);

                // // 2. change the password, to change status from FORCE_CHANGE_PASSWORD to CONFIRMED
                await rout.setUserPassword({
                    userPoolId: event.userPoolId,
                    email: googleUserInfo.email,
                });

                // console.log('PASSWORD SET');

                // event.request.userAttributes.newUserName = newUser.Username;

                // // 3. merge the social and the native accounts
                const nickname = rout._generateNickname(googleUserInfo.email);
                let event1 = JSON.parse(JSON.stringify(event));

                const referralCode = rout.generateReferralCode();

                const userAttrArray: Array<AttributeType> = [
                    {
                        Name: "custom:tenant_id" /* required */,
                        Value: tenantId,
                    },
                    {
                        Name: "custom:user_id" /* required */,
                        Value: wodo_id,
                    },
                    {
                        Name: "custom:nw" /* required */,
                        Value: "1",
                    },
                    {
                        Name: "picture" /* required */,
                        Value: process.env.IAM_SERVICE_DEFAULT_AVATAR,
                    },
                    {
                        Name: "nickname" /* required */,
                        Value: nickname,
                    },
                    {
                        Name: "email_verified",
                        Value: "true",
                    },
                    {
                        Name: "custom:referral_code" /* required */,
                        Value: referralCode,
                    },
                ];
                const referrerCodeAttr: AttributeType | undefined = rout.resolveReferrerCode(event);
                if (referrerCodeAttr) {
                    userAttrArray.push(referrerCodeAttr);
                }

                event1.userName = newUser.Username;
                event1.request.userAttributes.email = googleUserInfo.email;
                await rout.updateCognitoAttributes(event1, userAttrArray, wodo_id, tenantId);
                // console.log("UPDATED ACCOUNT");

                // linking users with some account breaks the lambda cycle. post confirmation does not work
                try {
                    await rout.send_welcome_email(googleUserInfo.email, event1.callerContext.clientId);
                    console.log('mail sent');
                    const result = await rout.postUserRegistrationAWS(event1, wodo_id, tenantId, referralCode, process.env.IAM_SERVICE_DEFAULT_AVATAR, nickname, ep_url);
                    console.log('post user registration aws');
                    console.log("result is: ???", result);
                } catch (error) {
                    console.log("Error is: ???", error);
                    throw error;
                }

                // await rout.writeEmail(googleUserInfo.email, event.userPoolId).catch(err => {
                //     console.log("WRITE EMAIL PROBLEM");
                // });
                // await rout.addUserToGroup(event.userPoolId, newUser.userName, "wodo_user");
                // await rout.shutdownCassandra();
                // event1.response.answerCorrect = true;
                // return event1;
            }
            event.response.answerCorrect = true;
        }
    } catch (error) {
        console.error("Token verification failed:", error);
        //  event.response.answerCorrect must be set in case of error
        event.response.answerCorrect = false;
    }

    console.log('returning event', event);

    return event;
};
