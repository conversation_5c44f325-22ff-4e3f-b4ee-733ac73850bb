const { SESClient, SendTemplatedEmailCommand } = require("@aws-sdk/client-ses"); // CommonJS import
const axios = require("axios");
import { ulid } from "ulidx";
import {
    CognitoIdentityProviderClient,
    AttributeType,
    ListUsersCommand,
    AdminUpdateUserAttributesCommand,
    AdminUpdateUserAttributesCommandInput,
    AdminUpdateUserAttributesCommandOutput,
    AdminLinkProviderForUserCommand,
    AdminCreateUserCommand,
    AdminAddUserToGroupCommand,
    AdminSetUserPasswordCommand,
    UserType,
} from "@aws-sdk/client-cognito-identity-provider";
const cassandra = require("cassandra-driver");
const fs = require("fs");
import { generateFromEmail, generateUsername } from "unique-username-generator";
import path from "path";
import { timeLog } from "console";
const https = require("https");

let cognitoIDPC: any;

let auth;

let sslOptions;

const Max_retry_attempts = 10;

let cassClient: any;
export interface GoogleUserInfo {
    sub: string;
    email: string;
}

export class Routine {
    public region: string = "";

    public password_change_sent = false;
    public welcome_sent = false;
    public cass_inited = false;
    public tenant_conf: any;
    public env: any;
    public constructor(env: any) {
        this.env = env;

        cognitoIDPC = new CognitoIdentityProviderClient({
            region: this.env.REGION,
        });

        try {
            //check env variables for JSON'd useridpools
            this.tenant_conf = JSON.parse(`${this.env.IAM_SERVICE_TENANT_CONF}`);
        } catch (err) {
            //INFORM FOR BAD JSON
            console.log("JSON P ERROR");
            console.log(err);
        }
        this.region = env.REGION;
    }

    async connectToCassandra() {
        if (this.cass_inited) return;

        const auth = new cassandra.auth.PlainTextAuthProvider(this.env.KEYSPACE_USER, this.env.KEYSPACE_PASS);
        const certFilePath = path.join(__dirname, "sf-class2-root.crt");
        const sslOptions = {
            ca: [fs.readFileSync(certFilePath, "utf-8")],
            host: this.env.KEYSPACE_HOST,
            rejectUnauthorized: true,
        };

        cassClient = new cassandra.Client({
            contactPoints: [this.env.KEYSPACE_HOST],
            localDataCenter: this.env.REGION,
            authProvider: auth,
            sslOptions: sslOptions,
            consistency: cassandra.types.consistencies.localQuorum,
            logEmitter: function log(level: any, className:
                 any, message: any, furtherInfo: any) {
                // console.log("Cassandra client log:", level, className, message, furtherInfo);
            },
            protocolOptions: { port: 9142 },
        });

        this.cass_inited = true;
    }


    async shutdownCassandra() {
        await cassClient.shutdown();
    }

    async checkIpExits(ip: string, pool_id: string) {
        await this.connectToCassandra();
        const query = "SELECT * FROM wodo_reg.ip WHERE ip = ? AND pool_id=?";
        const result = await cassClient.execute(query, [ip, pool_id], { prepare: true });
        return result.rows && result.rows.length > 0;
    }

    async writeIp(ip:string, pool_id: string) {
        await this.connectToCassandra();
        const query = "INSERT INTO wodo_reg.ip (ip, pool_id, date) VALUES (?, ?, ?)";
        const result = await cassClient.execute(query, [ip, pool_id, Date.now()], { prepare: true, consistency: cassandra.types.consistencies.localQuorum });
        return result;
    }

    async writeLog(log:string, event:any) {
        const id = ulid();
        const query = "INSERT INTO wodo_reg.log (id, log, date) VALUES (?, ?, ?)";
        await this.connectToCassandra();
        const logg = {log: log, event: event};
        const result = await cassClient.execute(query, [ id, JSON.stringify(logg), Date.now()], { prepare: true, consistency: cassandra.types.consistencies.localQuorum });
        return result;
    }


    async addUserToGroup(poolId: string, username: string, groupName: string) {
        let params = {
            UserPoolId: poolId,
            Username: username,
            GroupName: groupName,
        };

        let cmd = new AdminAddUserToGroupCommand(params);
        console.log('SEND1aa');
        const res = await cognitoIDPC.send(cmd);

        return res;
    }

    async setUserPassword({ userPoolId, email }: { userPoolId: string; email: string }) {
        const adminChangePasswordCommand = new AdminSetUserPasswordCommand({
            UserPoolId: userPoolId,
            Username: email, // the email is the username
            Password: this._generatePassword(), // generate a random password
            Permanent: true, // this is needed to set the password as permanent
        });

        console.log('SEND2');
        await cognitoIDPC.send(adminChangePasswordCommand);
    }

    async createUser({ userPoolId, email }: { userPoolId: string; email: string }) {
        const adminCreateUserCommand = new AdminCreateUserCommand({
            UserPoolId: userPoolId,
            MessageAction: "SUPPRESS", // don't send email to the user
            Username: email,
            UserAttributes: [
                {
                    Name: "email",
                    Value: email,
                },
                {
                    Name: "email_verified",
                    Value: "true",
                },
            ],
        });
        console.log('SEND3');

        const { User } = await cognitoIDPC.send(adminCreateUserCommand);
        return User;
    }

    async linkSocialAccount({
        userPoolId,
        cognitoUsername,
        providerName,
        providerUserId,
    }: {
        userPoolId: string;
        cognitoUsername?: string;
        providerName: string;
        providerUserId: string;
    }) {
        const linkProviderForUserCommand = new AdminLinkProviderForUserCommand({
            UserPoolId: userPoolId,
            DestinationUser: {
                ProviderName: "Cognito", // Cognito is the default provider
                ProviderAttributeValue: cognitoUsername, // this is the username of the user
            },
            SourceUser: {
                ProviderName: providerName, // Google or Facebook (first letter capitalized)
                ProviderAttributeName: "Cognito_Subject", // Cognito_Subject is the default attribute name
                ProviderAttributeValue: providerUserId, // this is the value of the provider
            },
        });

        console.log('SEND4');
        await cognitoIDPC.send(linkProviderForUserCommand);
    }

    async findUserByEmail(email: string, userPoolId: string): Promise<UserType | undefined> {
        const listUsersCommand = new ListUsersCommand({
            UserPoolId: userPoolId,
            Filter: `email = "${email}"`,
            Limit: 1,
        });

        console.log('SEND5');
        const { Users } = await cognitoIDPC.send(listUsersCommand);

        return Users?.[0];
    }

    async updateCognitoAttributes(event: any, attrs: AttributeType[], id: string, tenantId: string = "01GQ7Y2FHD0C8C5H8F342NEJF5") {
        const cognitoClient = new CognitoIdentityProviderClient({ region: this.region });

        var userAttributes: AttributeType[] = attrs;

        let input: AdminUpdateUserAttributesCommandInput = {
            UserPoolId: event.userPoolId,
            Username: event.userName, //"6c8fdaf9-e874-4af1-8270-acb203c5100f",
            UserAttributes: userAttributes,
        };
        let command = new AdminUpdateUserAttributesCommand(input);

        try {
            console.log('SEND6');
            let response: AdminUpdateUserAttributesCommandOutput = await cognitoClient.send(command);
            //console.log(`The user attributes successfully updated on the cognito.RequestId:${response.$metadata.requestId}`);
        } catch (error: any) {
            const { requestId, cfId, extendedRequestId } = error.$metadata;
            let errMsg: string = `Could not update cognito user attributes, RequestId:${requestId}`;
            if (error.name === "NotAuthorizedException") {
                errMsg = `NotAuthorizedException. ${errMsg}`;
            } else if (error.name === "ResourceNotFoundException") {
                // client fault.This exception is thrown when the Amazon Cognito service can't find the requested resource.
                errMsg = `Idebtity pool not found. ${errMsg}`;
            } else if (error.name === "UserNotFoundException") {
                // client fault.This exception is thrown when a user isn't found.
                errMsg = `User not found ${errMsg}`;
            } else if (error.name === "InvalidParameterException") {
                // client fault.This exception is thrown when a user isn't found.
                errMsg = `InvalidParameterException. ${errMsg}. ${error.message}`;
            }
            console.error(errMsg, error);
            throw error;
        }
    }
    async writeEmail(email: string, pool_id: string) {
        await this.connectToCassandra();
        const modifiedEmail = email.replace(/\./g, "").toLowerCase();

        // const exists = await checkEmailExists(modifiedEmail);
        // if (exists) {
        //   console.log(`Email ${modifiedEmail} already exists in AWS Keyspaces`);
        //   return;
        // }

        const query = "INSERT INTO wodo_reg.email (email, pool_id, date) VALUES (?, ?, ?)";

        await cassClient.execute(query, [modifiedEmail, pool_id, Date.now()], { prepare: true, consistency: cassandra.types.consistencies.localQuorum  })
        .catch((err: any) => {
            console.log("ERROR WRITING EMAIL : ", err);
        });
        this.cass_inited = false;

        return;

        // console.log(`Email ${modifiedEmail} written to AWS Keyspaces`);
    }


    async postUserRegistrationAWS(event: any, id: String, tenantId: string, referralCode: string, picture: string | undefined, nickname: string, url: string | undefined) {
        return new Promise(async (resolve, reject) => {
            let userReg = {
                id: id,
                uuid: event.userName,
                password: "password",
                picture: picture,
                nickname: nickname,
                tenantId: tenantId,
                email: event.request.userAttributes.email,
                locale: event.request.userAttributes.locale??'',
                zoneinfo: event.request.userAttributes.zoneinfo??'',
                referralCode: referralCode,
                referrerCode: this.fixRefCodeLenght(event.request.userAttributes["custom:referrer_code"] ?? ""),
            };

            // Reduce timeout to ensure Lambda doesn't timeout first
            const axiosTimeout = 3000; // 3 seconds
            const maxRetries = 2;  // Reduce retries
            let retryCount = 0;

            while (retryCount < maxRetries) {
                try {
                    console.log('trying to post user registration :', retryCount);
                    const response = await axios.post(`${url}/identity/api/v1/users/register`, userReg, {
                        headers: {
                            user_groups: `service_admin`,
                        },
                        timeout: axiosTimeout,
                    });
                    console.log("response is: ???", response);
                    if(response.status === 200 || response.status === 201) {
                        console.log('post user registration success');
                        return resolve(response);
                    }
                    else{   
                        console.log('post user registration failed', response);
                        retryCount++;
                    }
                } catch (err) {
                    retryCount++;
                    if (retryCount === maxRetries) {
                        return reject(new Error(err as string));
                    }
                    // Minimal delay between retries
                    await new Promise(r => setTimeout(r, 100));
                }
            }
        });
    }

    async send_welcome_email(email: string, clientId: string) {
        if (this.welcome_sent) {
            //if aws fails to connect to our server it retries and sends multiple emails
            //console.log("ALREADY SENT PASS WELCOME");
            return;
        }
        let template = "welcome_wodo";
        switch (clientId) {
            case "1bf0gjemn7bifa02bimmftmnoc":
            case "6k4lusvul4nhtatuhndl3ut0em":
            case "5mssegecpdh030eh9jr0e75tt2":
                template = "welcome_wodo_wallet";
                break;
            default:
                break;
        }

        //console.log("WELCOMEMAIL " + template);

        const params = {
            // SendTemplatedEmailRequest
            Source: "<EMAIL>", // required
            Destination: {
                ToAddresses: [email],
            },
            ReplyToAddresses: ["<EMAIL>"],
            ReturnPath: "<EMAIL>",
            Template: template, // required
            TemplateData: '{ "WWWW":"QQQ" }' /* required */,
        };
        this.welcome_sent = true;
        await this._send_email(params);
    }

    async _send_email(params: any) {
        //console.log("SENDING EMAIL");
        //console.log(params);

        const client = new SESClient({ region: "eu-central-1" });

        const command = new SendTemplatedEmailCommand(params);
        console.log('SEND7');
        const response = await client.send(command);
        //console.log(response);
        //   var sendPromise = new AWS.SES({ apiVersion: "2010-12-01" })
        //   .sendTemplatedEmail(params)
        //   .promise();

        // // Handle promise's fulfilled/rejected states
        //   await sendPromise
        //   .then(function (data) {
        //     console.log(data);
        //   })
        //   .catch(function (err) {
        //     console.error(err, err.stack);
        //   });
    }
    _check_for_tenant(userPoolId: string) {
        for (const ten in this.tenant_conf) {
            if (this.tenant_conf[ten].pools.indexOf(userPoolId) != -1) {
                return this.tenant_conf[ten].tenantId;
            }
            // console.log(tenant);
        }

        return false;
    }
    _generatePassword = () => {
        return `${Math.random() // Generate random number, eg: 0.123456
            .toString(36) // Convert  to base-36 : "0.4fzyo82mvyr"
            .slice(-8)}42_-.A`; // Cut off last 8 characters : "yo82mvyr" and add a number because the cognito password policy requires a number
    };

    _generateNickname = (email: string) => {
        return generateFromEmail(email, 6).substring(0, 20);
    };

    generateReferralCode = () => {
        const randomStr = ulid(); // generates random string having length more than 10 chars
        const referralCode = randomStr.slice(randomStr.length - 10, randomStr.length);
        return referralCode;
    };

    fixLength(str: string, lenght: number) {
        if (str && str.length > lenght) {
            str = str.slice(0, lenght);
        }
        return str;
    }

    fixRefCodeLenght(str: string) {
        return this.fixLength(str, 10);
    }

    resolveReferrerCode(event: any): AttributeType | undefined {
        const referrerCode = this.fixRefCodeLenght(event.request.userAttributes["custom:referrer_code"]);
        if (referrerCode && referrerCode.length > 0) {
            return {
                Name: "custom:referrer_code" /* optional */,
                Value: referrerCode,
            } as AttributeType;
        }
        return undefined;
    }

    async verifyGoogleToken(token: string): Promise<GoogleUserInfo> {
        return new Promise((resolve, reject) => {
            https
                .get(`https://oauth2.googleapis.com/tokeninfo?id_token=${token}`, (resp: any) => {
                    let data = "";
                    resp.on("data", (chunk: Buffer) => (data += chunk));
                    resp.on("end", () => {
                        if (resp.statusCode === 200) {
                            resolve(JSON.parse(data));
                        } else {
                            reject(new Error(`Token verification failed with status ${resp.statusCode}: ${data}`));
                        }
                    });
                })
                .on("error", (err: Error) => {
                    reject(err);
                });
        });
    }
    
}
