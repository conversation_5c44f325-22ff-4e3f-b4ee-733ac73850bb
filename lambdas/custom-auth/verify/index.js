// VERIFY CHALLENGE LAMBDA

const https = require('https');

async function verifyGoogleToken(token) {
  return new Promise((resolve, reject) => {
    https.get(
      `https://oauth2.googleapis.com/tokeninfo?id_token=${token}`,
      (resp) => {
        let data = '';
        resp.on('data', (chunk) => (data += chunk));
        resp.on('end', () => {
          if (resp.statusCode === 200) {
            resolve(JSON.parse(data));
          } else {
            reject(new Error(`Token verification failed with status ${resp.statusCode}: ${data}`));
          }
        });
      }
    ).on('error', (err) => {
      reject(err);
    });
  });
}

exports.handler = async (event) => {
  console.log('Verify auth challenge event:', JSON.stringify(event, null, 2));

  // Always ensure event.response is initialized
  event.response = event.response || {};

  const challengeAnswer = event.request.challengeAnswer; // The Google ID token

  try {
    const googleUserInfo = await verifyGoogleToken(challengeAnswer);
    console.log('Verified Google user info:', googleUserInfo);

    const identities = JSON.parse(event.request.userAttributes.identities || '[]');
    const googleIdentity = identities.find(id => id.providerName === 'Google');

    if (!googleIdentity) {
      console.error('No Google identity found for user:', event.userName);
      event.response.answerCorrect = false;
      return event;
    }

    const isValid =
      googleUserInfo.sub === googleIdentity.userId &&
      googleUserInfo.email === event.request.userAttributes.email;

    console.log('Token validation result:', {
      expectedGoogleId: googleIdentity.userId,
      receivedGoogleId: googleUserInfo.sub,
      expectedEmail: event.request.userAttributes.email,
      receivedEmail: googleUserInfo.email,
      isValid
    });

    event.response.answerCorrect = isValid;
  } catch (error) {
    console.error('Token verification failed:', error);
    //  event.response.answerCorrect must be set in case of error
    event.response.answerCorrect = false;
  }

  return event;
};
