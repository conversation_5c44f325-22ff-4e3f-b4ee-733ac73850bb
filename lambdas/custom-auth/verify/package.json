{"name": "custom-auth-verify", "version": "1.0.0", "description": "", "main": "dist/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "build": "tsc", "deploy": "npm i && npm run package && aws lambda update-function-code --function-name cognito-verify-challenge --zip-file fileb://function.zip", "deploy-prod": "npm i && npm run package && aws lambda update-function-code --profile wodo-prod --function-name cognito-verify-challenge --zip-file fileb://function.zip", "package": "npm run build && zip -r function.zip dist node_modules package.json"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@aws-sdk/client-s3": "^3.667.0", "@aws-sdk/s3-request-presigner": "^3.667.0", "aws-lambda": "^1.0.7", "aws-sdk": "^2.1691.0", "jsonwebtoken": "^9.0.2", "@aws-sdk/client-cognito-identity-provider": "^3.490.0", "@aws-sdk/client-lambda": "^3.516.0", "@aws-sdk/client-ses": "^3.501.0", "axios": "^1.6.5", "cassandra-driver": "^4.7.2", "esbuild": "^0.14.14", "ulidx": "^0.4.0", "unique-username-generator": "^1.3.0"}, "devDependencies": {"@types/aws-lambda": "^8.10.145", "typescript": "^5.6.2"}}