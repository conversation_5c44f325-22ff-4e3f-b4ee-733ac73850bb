// Load the AWS SDK for Node.js
// var AWS = require("aws-sdk");
const { SESClient, SendTemplatedEmailCommand } = require("@aws-sdk/client-ses"); // CommonJS import

// Set the region
// AWS.config.update({ region: "eu-central-1" });
const client = new SESClient({ region: "eu-central-1" });
const { ListTemplatesCommand } = require("@aws-sdk/client-ses");

async function listTemplates() {
    try {
        const command = new ListTemplatesCommand({
            MaxItems: 100 // Adjust this number based on how many templates you want to retrieve
        });
        
        const response = await client.send(command);
        console.log("Email Templates:", response.TemplatesMetadata);
        return response.TemplatesMetadata;
    } catch (err) {
        console.error("Error listing templates:", err);
        throw err;
    }
}

// Execute the function
listTemplates();

// // Create the promise and SES service object
// var sendPromise = new AWS.SES({ apiVersion: "2010-12-01" })
//   .sendTemplatedEmail(params)
//   .promise();

// // Handle promise's fulfilled/rejected states
// sendPromise
//   .then(function (data) {
//     console.log(data);
//   })
//   .catch(function (err) {
//     console.error(err, err.stack);
//   });
