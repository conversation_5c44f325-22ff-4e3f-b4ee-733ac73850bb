// Load the AWS SDK for Node.js
var AWS = require("aws-sdk");
// Set the region
AWS.config.update({ region: "eu-central-1" });
let message = 'asdgasdgasdg\nasdg\nsadg';
// Create sendTemplatedEmail params
var params = {
  Destination: {
    /* required */
    ToAddresses: [
      "<EMAIL>",
      /* more To email addresses */
    ],
  },
  Source: "<EMAIL>" /* required */,
  Template: "support_form_received" /* required */,
  // Template: "password_resetted" /* required */,
  // TemplateData: '{ "WWWW":"QQQ" }' /* required */,
  TemplateData: '{ "date":"'+new Date()+'","title":"sadfasdf","att":"sadfasdf","message":"'+message.replace(/\n/g,'<br>')+'" }' /* required */,
  ReplyToAddresses: ["<EMAIL>"],
};

// Create the promise and SES service object
var sendPromise = new AWS.SES({ apiVersion: "2010-12-01" })
  .sendTemplatedEmail(params)
  .promise();

// Handle promise's fulfilled/rejected states
sendPromise
  .then(function (data) {
    console.log(data);
  })
  .catch(function (err) {
    console.error(err, err.stack);
  });
