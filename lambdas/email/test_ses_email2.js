// import { SESClient, SendTemplatedEmailCommand } from "@aws-sdk/client-ses"; // ES Modules import
const { SESClient, SendTemplatedEmailCommand } = require("@aws-sdk/client-ses"); // CommonJS import
const client = new SESClient({ region: "eu-central-1" });
let message = 'asdgasdgasdg\nasdg\nsadg';
const input = { // SendTemplatedEmailRequest
  Source: "<EMAIL>", // required
  Destination: { // Destination
    ToAddresses: [ // AddressList
      "<EMAIL>",
    ],
  },
  ReplyToAddresses: [
    "<EMAIL>",
  ],
  ReturnPath: "<EMAIL>",
  // SourceArn: "STRING_VALUE",
  // ReturnPathArn: "STRING_VALUE",
  // Tags: [ // MessageTagList
  //   { // MessageTag
  //     Name: "STRING_VALUE", // required
  //     Value: "STRING_VALUE", // required
  //   },
  // ],
  // ConfigurationSetName: "STRING_VALUE",
  Template: "support_form_received", // required
  // TemplateArn: "STRING_VALUE",
  // TemplateData: '{ "WWWW":"QQQ" }' /* required */,
  TemplateData: '{ "date":"'+new Date()+'","title":"sadfasdf","att":"","message":"'+message.replace(/\n/g,'<br>')+'" }' /* required */,

  // TemplateData: "STRING_VALUE", // required
};

(async()=>{
const command = new SendTemplatedEmailCommand(input);
const response = await client.send(command);
// { // SendTemplatedEmailResponse
//   MessageId: "STRING_VALUE", // required
// };

})()