/*
*
{
    "version": "string",
    "triggerSource": "string",
    "region": AWSRegion,
    "userPoolId": "string",
    "userName": "string",
    "callerContext": 
        {
            "awsSdkVersion": "string",
            "clientId": "string"
        },
    "request": {
            "userAttributes": {
                "string": "string",
                . . .
            },
            "clientMetadata": {
            	"string": "string",
            	. . .
            }
        },
    "response": {}
}
*/
//import { APIGatewayProxyEvent, APIGatewayProxyResult } from 'aws-lambda';
import { ulid } from 'ulidx';
import { Routine } from './routine';
import {
  LambdaClient,
  ListLayersCommand,
  InvokeCommand,
} from '@aws-sdk/client-lambda';
import { AttributeType } from '@aws-sdk/client-cognito-identity-provider';

const client = new LambdaClient({
  region: process.env.AWS_REGION,
});

// const {
//     AWS_REGION,
//     API_URL,
//     AWS_ACCESS_KEY_ID,
//     AWS_SECRET_ACCESS_KEY,
//     AWS_SESSION_TOKEN,
//     IAM_SERVICE_DEFAULT_AVATAR,
//     IAM_SERVICE_ENDPOINT,
//     IAM_SERVICE_ENDPOINT_PORT,
// } = process.env;

export let ep_url = `${process.env.IAM_SERVICE_ENDPOINT}:${process.env.IAM_SERVICE_ENDPOINT_PORT}`;

// might be private alb domain http://internal-wodo-prod-alb-prv-841011465.eu-central-1.elb.amazonaws.com:8090

// AWS.config.update({ region: AWS_REGION });
let rout: Routine,
  skip_RC_per_client: string[] = [],
  skip_email_check = false,
  skip_IP_per_client: string[] = [];

if (process.env['SKIP_EMAIL_CHECK']) {
  skip_email_check = process.env['SKIP_EMAIL_CHECK'] == 'true';
}

console.log('SKIP_EMAIL_CHECK>>', skip_email_check);
if (process.env['SKIP_CAPTCHA_PER_CLIENTS']) {
  skip_RC_per_client = process.env['SKIP_CAPTCHA_PER_CLIENTS'].split(',');
}
if (process.env['SKIP_IPCHECK_PER_CLIENTS']) {
  skip_IP_per_client = process.env['SKIP_IPCHECK_PER_CLIENTS'].split(',');
}

export const lambdaHandler = async (
  event: any,
  context: any,
  callback: any,
): Promise<any> => {
  console.log(`event:${JSON.stringify(event)}`);
  console.log(`context:${JSON.stringify(context)}`);
  console.log(`SKIP_CAPTCHA_PER_CLIENTS:${JSON.stringify(skip_RC_per_client)}`);

  rout = new Routine(process.env);
  // console.log();
  let ew_id = ulid(),
    clientId = event.callerContext.clientId;

  const referralCode = rout.generateReferralCode();

  //console.log(`id:${ew_id}`);
  //console.log(`awsRegion:${AWS_REGION}`);

  if (event.triggerSource == 'PostConfirmation_ConfirmSignUp') {
    console.log('POSTTTTTTT!!');
    let pl = event.userPoolId,
      mew = event.request.userAttributes['custom:mew'],
      nickname = rout._generateNickname(event.request.userAttributes.email);

    const userAttrArray: Array<AttributeType> = [
      {
        Name: 'custom:user_id' /* required */,
        Value: ew_id,
      },
      // {
      //   Name: 'custom:mew' /* required */,
      //   Value: mew,
      // },
      {
        Name: 'picture' /* required */,
        Value: process.env.IAM_SERVICE_DEFAULT_AVATAR,
      },
      {
        Name: 'custom:nw' /* required */,
        Value: '1',
      },
      {
        Name: 'nickname' /* required */,
        Value: nickname,
      },
      {
        Name: 'custom:referral_code' /* required */,
        Value: referralCode,
      },
    ];
    const referrerCodeAttr: AttributeType | undefined =
      rout.resolveReferrerCode(event);
    if (referrerCodeAttr) {
      userAttrArray.push(referrerCodeAttr);
    }
    //since we are past pre signup no need to check again.
    await rout.updateCognitoAttributes(event, userAttrArray, ew_id);

    try {
      await rout.send_welcome_email(
        event.request.userAttributes.email,
        clientId,
      );
      //no need to check for errors because this is a new user
      const result = await rout.postUserRegistrationAWS(
        event,
        ew_id,
        referralCode,
        process.env.IAM_SERVICE_DEFAULT_AVATAR,
        nickname,
        ep_url,
      );
      //console.log('@@Adding user to group');
      //console.log("result is: ???", result);
    } catch (error) {
      console.log('send_welcome_email Error is: ???', error);
      throw error;
    }

    console.log('WRITING EMAIL');
    await rout.addUserToGroup(event.userPoolId, event.userName, 'ew_user');

    if (!skip_IP_per_client.includes(clientId)) {
      let key = await rout.checkUserKey(mew, pl);

      if (!key) {
        let err2 = new Error('Bad registration attempt-2a');
        callback(err2, event);
      }

      console.log('STEP 2');
      let ip = key['ip'];

      await Promise.all([
        rout.writeIp(ip, pl),
        rout.writeEmail(event.request.userAttributes.email, event.userPoolId),
      ]).catch((err) => {
        console.log('WRITE MANY PROBLEM2', err);
      });
    } else {
      await rout
        .writeEmail(event.request.userAttributes.email, event.userPoolId)
        .catch((err) => {
          console.log('WRITE MANY PROBLEM3', err);
        });
    }

    await rout.shutdownCassandra();

    //console.log("POST EVENT ", event);
    callback(null, event);
  } else if (event.triggerSource == 'PostConfirmation_ConfirmForgotPassword') {
    //console.log("PostConfirmation_ConfirmForgotPassword");

    await rout.send_password_change_email(
      event.request.userAttributes.email,
      clientId,
    );
    callback(null, event);
  } else if (event.triggerSource == 'PostConfirmation_ConfirmSignUp' && 
             (event.userName.includes('SignInWithApple') || event.userName.includes('apple'))) {
    // Handle PostConfirmation for Apple users if they reach this trigger
    console.log('PostConfirmation for Apple user:', event.userName);
    
    // Ensure email is verified for Apple users
    await rout.updateCognitoAttributesByUsername(
      event.userPoolId, 
      event.userName, 
      [{ Name: 'email_verified', Value: 'true' }]
    );
    
    callback(null, event);
  } else if (event.triggerSource == 'PreSignUp_AdminCreateUser') {
    //console.log("PRESIGNUP ADMIN, THIS IS A PLACEHOLDER FOR GOOGLE SIGNUP");
    // callback(null, event);
  } else if (
    event.triggerSource == 'PreSignUp_ExternalProvider' &&
    event.userName.indexOf('google') != -1
  ) {
    //some of PreSignUp_ExternalProvider credits : https://github.com/buithaibinh/aws-link-oauth-account-with-cognito-user/blob/main/cdk/functions/triggers/index.ts
    //console.log("PreSignUp_ExternalProvider");
    let pl = event.userPoolId;

    const {
      userAttributes: { email, name },
    } = event.request;

    if(!event.request.userAttributes.phone_number || !event.request.userAttributes.phone_number.startsWith('+')) {
      event.request.userAttributes.phone_number = '+**********';
    }
    

    // if the user is found then we link the Google account to the user pool
    const user = await rout.findUserByEmail(email, event.userPoolId);

    // userName example: "Facebook_12324325436" or "Google_1237823478"
    // we need to extract the provider name and provider value from the userName
    let [providerName, providerUserId] = event.userName.split('_');

    // Uppercase the first letter because the event sometimes
    // has it as google_1234 or facebook_1234. In the call to `adminLinkProviderForUser`
    // the provider name has to be Google or Facebook (first letter capitalized)
    providerName = providerName.charAt(0).toUpperCase() + providerName.slice(1);

    // if the user is found then we link the Google account to the user pool
    if (user) {
      //console.log("USER FOUND " + email);
      await rout.linkSocialAccount({
        userPoolId: event.userPoolId,
        cognitoUsername: user.Username,
        providerName: providerName,
        providerUserId: providerUserId,
      });

      // return the event to continue the signup process
      return event;
    } else {
      //console.log("USER NOT FOUND");
      // if the user is not found then we need to create the user in the user pool

      // // 1. create a native cognito account
      const newUser = await rout.createUser({
        userPoolId: event.userPoolId,
        email,
      });

      if (!newUser) {
        throw new Error('Failed to create user');
      }

      if (newUser.Username) {
        await rout.addUserToGroup(
          event.userPoolId,
          newUser.Username,
          'ew_user',
        );
      } else {
        throw new Error('Username is undefined');
      }

      //console.log("NEW USER CREATED", newUser);

      // // 2. change the password, to change status from FORCE_CHANGE_PASSWORD to CONFIRMED
      await rout.setUserPassword({
        userPoolId: event.userPoolId,
        email,
      });

      // console.log('PASSWORD SET');

      // event.request.userAttributes.newUserName = newUser.Username;

      // // 3. merge the social and the native accounts
      let rrr = await rout.linkSocialAccount({
        userPoolId: event.userPoolId,
        cognitoUsername: newUser.Username,
        providerName: providerName,
        providerUserId: providerUserId,
      });

      // console.log("SOCIAL ACCOUNT LINKED",rrr);
      event.response.autoVerifyEmail = true;
      event.response.autoConfirmUser = true;

      //We can't change the main event, it disturbs the flow.
      let event1 = JSON.parse(JSON.stringify(event)),
        nickname = rout._generateNickname(event.request.userAttributes.email);

      event1.userName = newUser.Username;

      const userAttrArray: Array<AttributeType> = [
        {
          Name: 'custom:user_id' /* required */,
          Value: ew_id,
        },
        {
          Name: 'custom:nw' /* required */,
          Value: '1',
        },
        {
          Name: 'picture' /* required */,
          Value: process.env.IAM_SERVICE_DEFAULT_AVATAR,
        },
        {
          Name: 'nickname' /* required */,
          Value: nickname,
        },
        {
          Name: 'email_verified',
          Value: 'true',
        },
        {
          Name: 'custom:referral_code' /* required */,
          Value: referralCode,
        },
      ];
      const referrerCodeAttr: AttributeType | undefined =
        rout.resolveReferrerCode(event);
      if (referrerCodeAttr) {
        userAttrArray.push(referrerCodeAttr);
      }

      await rout.updateCognitoAttributes(event1, userAttrArray, ew_id);
      // console.log("UPDATED ACCOUNT");

      // linking users with some account breaks the lambda cycle. post confirmation does not work
      try {
        await rout.send_welcome_email(
          event.request.userAttributes.email,
          clientId,
        );
        const result = await rout.postUserRegistrationAWS(
          event1,
          ew_id,
          referralCode,
          process.env.IAM_SERVICE_DEFAULT_AVATAR,
          nickname,
          ep_url,
        );
        //console.log("result is: ???", result);
      } catch (error) {
        console.log('Error is: ???', error);
        throw error;
      }

      await rout
        .writeEmail(event.request.userAttributes.email, event.userPoolId)
        .catch((err) => {
          console.log('WRITE EMAIL PROBLEM');
        });

      await rout.shutdownCassandra();
    }
    //console.log("NOW CALLBACK FROM PRE", event);

    // const input = { // InvocationRequest
    //     FunctionName: "wodo-cognito-dev-applicat-CognitoPostConfirmationF-b1TVxO6iUD0Q", // required
    //     // InvocationType: "Event",
    //     // LogType: "Tail",
    //     // ClientContext: "STRING_VALUE",
    //     Payload: JSON.stringify(event, null, 2), // pass params
    //         // Qualifier: "STRING_VALUE",
    //   };
    //   const command = new InvokeCommand(input);
    //   const response = await client.send(command);
    //   console.log(response);

    // if the user signed up with email and password then we don't need to do anything
    callback(null, event);
    return event;
  } else if (
    event.triggerSource == 'PreSignUp_ExternalProvider' &&
    (event.userName.startsWith('SignInWithApple_') || event.userName.indexOf('apple') !== -1) 
  ) {
    console.log('Apple signup detected. UserName:', event.userName);
    //console.log("PreSignUp_ExternalProvider for Apple");

    const {
      userAttributes: { email, name },
    } = event.request;

    // Generate a valid phone number format for Apple users
    // Apple name is mapped to phone_number, replace with proper phone format
    const appleName = event.request.userAttributes.phone_number;
    if (appleName && !appleName.startsWith('+')) {
      // Convert name to phone number format
      event.request.userAttributes.phone_number = '+**********';
    }

    // Set default values for required attributes
    if (!event.request.userAttributes.name && email) {
      event.request.userAttributes.name = email.split('@')[0];
    }

    // if the user is found then we link the Apple account to the user pool
    const user = await rout.findUserByEmail(email, event.userPoolId);

    // userName example: "Apple_12324325436" or "SignInWithApple_12324325436"
    let [providerName, providerUserId] = event.userName.split('_');

    // Normalize provider name to SignInWithApple for consistency
    if (providerName.toLowerCase().includes('apple')) {
      providerName = 'SignInWithApple';
    }

    if (user) {
      // link the Apple account to the existing user
      await rout.linkSocialAccount({
        userPoolId: event.userPoolId,
        cognitoUsername: user.Username,
        providerName,
        providerUserId,
      });

      // return the event to continue the signup process (same as Google flow)
      return event;
    } else {
      // if the user is not found then we create a new user
      const newUser = await rout.createUser({
        userPoolId: event.userPoolId,
        email,
      });

      if (!newUser) {
        throw new Error('Failed to create user');
      }

      if (newUser.Username) {
        await rout.addUserToGroup(
          event.userPoolId,
          newUser.Username,
          'ew_user',
        );
      } else {
        throw new Error('Username is undefined');
      }

      // change the password, to change status from FORCE_CHANGE_PASSWORD to CONFIRMED
      await rout.setUserPassword({
        userPoolId: event.userPoolId,
        email,
      });

      // link the Apple account to the new user
      await rout.linkSocialAccount({
        userPoolId: event.userPoolId,
        cognitoUsername: newUser.Username,
        providerName,
        providerUserId,
      });

      // Generate user ID and referral code for database registration
      const ew_id = ulid();
      const referralCode = rout.generateReferralCode();
      const nickname = rout._generateNickname(email);

      // Update Cognito attributes for Apple user (since PostConfirmation won't trigger)
      const userAttrArray: Array<AttributeType> = [
        {
          Name: 'custom:user_id' /* required */,
          Value: ew_id,
        },
        {
          Name: 'custom:nw' /* required */,
          Value: '1',
        },
        {
          Name: 'picture' /* required */,
          Value: process.env.IAM_SERVICE_DEFAULT_AVATAR,
        },
        {
          Name: 'nickname' /* required */,
          Value: nickname,
        },
        {
          Name: 'email_verified',
          Value: 'true',
        },
        {
          Name: 'custom:referral_code' /* required */,
          Value: referralCode,
        },
      ];
      const referrerCodeAttr: AttributeType | undefined =
        rout.resolveReferrerCode(event);
      if (referrerCodeAttr) {
        userAttrArray.push(referrerCodeAttr);
      }

      // Update all attributes except email_verified first
      const otherAttributes = userAttrArray.filter(attr => attr.Name !== 'email_verified');
      await rout.updateCognitoAttributesByUsername(event.userPoolId, newUser.Username, otherAttributes);
      
      // Set email_verified separately as the last step
      await rout.updateCognitoAttributesByUsername(event.userPoolId, newUser.Username, [
        { Name: 'email_verified', Value: 'true' }
      ]);

      // Create event copy for database registration
      let event1 = JSON.parse(JSON.stringify(event));
      event1.userName = newUser.Username;

      // Call IAM service to save user to database
      try {
        await rout.send_welcome_email(email, clientId);
        const result = await rout.postUserRegistrationAWS(
          event1,
          ew_id,
          referralCode,
          process.env.IAM_SERVICE_DEFAULT_AVATAR,
          nickname,
          process.env.IAM_SERVICE_ENDPOINT + ':' + process.env.IAM_SERVICE_ENDPOINT_PORT
        );
        console.log('Apple user registration result:', result);
      } catch (error) {
        console.error('Apple user registration failed:', error);
        // Continue anyway - user is created in Cognito
      }

      // Let Cognito handle confirmation naturally to trigger PostConfirmation
      event.response.autoConfirmUser = true;
      event.response.autoVerifyEmail = true;

      // return the event to continue the signup process  
      callback(null, event);
    }
  } else if (event.triggerSource == 'PreSignUp_SignUp') {
    //console.log("PreSignUp_SignUp");

    let pl = event.userPoolId,
      isTempEmail = skip_email_check
        ? false
        : rout.isTempEmail(event.request.userAttributes.email),
      disable_captcha =
        process.env['DISABLE_CAPTCHA_CHECK'] &&
        process.env['DISABLE_CAPTCHA_CHECK'] == 'true',
      mew = event.request.userAttributes['custom:mew'],
      isBadEmail = skip_email_check
        ? false
        : rout.isBadEmail(event.request.userAttributes.email);

    if (!skip_IP_per_client.includes(clientId)) {
      if (!event.request.userAttributes['custom:mew']) {
        let err2 = new Error('Bad registration attempt');
        callback(err2, event);
      }

      console.log('STEP 1');
      let key = await rout.checkUserKey(mew, pl);

      if (!key) {
        let err2 = new Error('Bad registration attempt-2');
        callback(err2, event);
      }

      console.log('STEP 2');
      let ip = key['ip'];
      let ipExists = await rout.checkIpExits(ip, pl);

      if (ipExists) {
        let err2 = new Error(
          'This ip has been used too often. Please try again later.',
        );
        await rout.writeLog(`Ip Rejection ${ip}`, event);
        await rout.shutdownCassandra();
        callback(err2, event);
      }
    } else {
      console.log('SKIPPING IP CHECKS PER CLIENT RULE');
    }

    //console.log("isBadEmail ", event.request.userAttributes.email, isBadEmail);
    //console.log("isTempEmail ", event.request.userAttributes.email, isTempEmail);
    console.log(
      '>>>>>>>>>',
      disable_captcha,
      skip_RC_per_client.includes(clientId),
    );
    //backwards compatibility for non
    //check for email uniqueness

    const startTime = performance.now();
    console.log('STEP 3');
    const ems = await rout.checkEmailExists(
      event.request.userAttributes.email,
      event.userPoolId,
    );

    if (ems.length > 0) {
      let err2 = new Error('This email has been used before.');
      await rout.writeLog(
        `Email Rejection ${event.request.userAttributes.email}`,
        event,
      );
      await rout.shutdownCassandra();
      // Return error to Amazon Cognito
      callback(err2, event);
    }

    const endTime = performance.now();
    const elapsedTime = endTime - startTime;
    console.log('Elapsed time:', elapsedTime, 'milliseconds');
    console.log('STEP 4');
    await rout.shutdownCassandra();

    if (!(disable_captcha || skip_RC_per_client.includes(clientId))) {
      if (
        !event.request.clientMetadata ||
        !event.request.clientMetadata['captcha']
      ) {
        console.log('Captcha is missing.');
        let err2 = new Error('Captcha is missing.');
        // Return error to Amazon Cognito
        callback(err2, event);
      } else if (event.request.clientMetadata['captcha']) {
        const captchaResponse = await rout.verifyGoogleRecaptcha(
          event.request.clientMetadata.captcha,
        );
        if (!captchaResponse) {
          //console.log("BAD CAPTCHA");
          let err2 = new Error('Bad Captcha');
          // Return error to Amazon Cognito
          callback(err2, event);
        }
        // console.log('CAPTCHA ',captchaResponse);
      }
    }

    console.log('FOUND @@@');

    if (isBadEmail) {
      console.log('+ is not allowed inside email');
      let err2 = new Error('+ is not allowed inside email');
      // Return error to Amazon Cognito
      callback(err2, event);
    } else if (isTempEmail) {
      console.log('This email domain is not allowed');
      let err2 = new Error('This email domain is not allowed');
      // Return error to Amazon Cognito
      callback(err2, event);
    }
    // callback(null, event);
  } else {
    //console.log("WRONG REDIRECT");
    callback(null, event);
  }

  // Return to Amazon Cognito
  //callback(null, event);
  //context.done(null, event);
  return event;
};
