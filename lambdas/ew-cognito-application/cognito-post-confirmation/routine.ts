const { SESClient, SendTemplatedEmailCommand } = require("@aws-sdk/client-ses"); // CommonJS import
const axios = require("axios");
import { ulid } from "ulidx";
import { TempEmails } from "./data";
import {
    CognitoIdentityProviderClient,
    AttributeType,
    ListUsersCommand,
    AdminUpdateUserAttributesCommand,
    AdminUpdateUserAttributesCommandInput,
    AdminUpdateUserAttributesCommandOutput,
    AdminLinkProviderForUserCommand,
    AdminCreateUserCommand,
    AdminAddUserToGroupCommand,
    AdminSetUserPasswordCommand,
    UserType,
} from "@aws-sdk/client-cognito-identity-provider";
const cassandra = require("cassandra-driver");
const fs = require("fs");
import { generateFromEmail, generateUsername } from "unique-username-generator";
import path from "path";

let cognitoIDPC: any;

let auth;

let sslOptions;

const Max_retry_attempts = 10;

let cassClient: any;

export class Routine {
    public region: string = "";

    public password_change_sent = false;
    public welcome_sent = false;
    public cass_inited = false;
    public env: any;
    public constructor(env: any) {
        this.env = env;

        cognitoIDPC = new CognitoIdentityProviderClient({
            region: this.env.AWS_REGION,
        });

        this.region = env.AWS_REGION;
    }

    async connectToCassandra() {
        if (this.cass_inited) return;

        const auth = new cassandra.auth.PlainTextAuthProvider(this.env.KEYSPACE_USER, this.env.KEYSPACE_PASS);
        const certFilePath = path.join(__dirname, "sf-class2-root.crt");
        const sslOptions = {
            ca: [fs.readFileSync(certFilePath, "utf-8")],
            host: this.env.KEYSPACE_HOST,
            rejectUnauthorized: true,
        };

        cassClient = new cassandra.Client({
            contactPoints: [this.env.KEYSPACE_HOST],
            localDataCenter: this.env.AWS_REGION,
            authProvider: auth,
            sslOptions: sslOptions,
            consistency: cassandra.types.consistencies.localQuorum,
            logEmitter: function log(level: any, className:
                 any, message: any, furtherInfo: any) {
                // console.log("Cassandra client log:", level, className, message, furtherInfo);
            },
            protocolOptions: { port: 9142 },
        });

        this.cass_inited = true;
    }

    isTempEmail(email: string) {
        let res = TempEmails.indexOf("," + email.toString().replace(/.*@/, "").toLowerCase() + ",");
        return res != -1;
    }

    async checkEmailExists(email: string, pool_id: string) {
        await this.connectToCassandra();
        // await new Promise(resolve => setTimeout(resolve, 2000)); // 2-second delay

        // return [];
        const modifiedEmail = email.replace(/\./g, "").toLowerCase();

        const query = "SELECT * FROM ew_reg.email WHERE email = ? and pool_id = ?";
        const result = await cassClient.execute(query, [modifiedEmail, pool_id], { prepare: true }).catch((err: any) => {
            console.log("REA ERROR : ", err);
        });
        this.cass_inited = false;
        return result.rows;
    }

    async writeEmail(email: string, pool_id: string) {
        await this.connectToCassandra();
        const modifiedEmail = email.replace(/\./g, "").toLowerCase();

        // const exists = await checkEmailExists(modifiedEmail);
        // if (exists) {
        //   console.log(`Email ${modifiedEmail} already exists in AWS Keyspaces`);
        //   return;
        // }

        const query = "INSERT INTO ew_reg.email (email, pool_id, date) VALUES (?, ?, ?)";

        await cassClient.execute(query, [modifiedEmail, pool_id, Date.now()], { prepare: true, consistency: cassandra.types.consistencies.localQuorum  })
        .catch((err: any) => {
            console.log("ERROR WRITING EMAIL : ", err);
        });
        this.cass_inited = false;

        return;

        // console.log(`Email ${modifiedEmail} written to AWS Keyspaces`);
    }

    async shutdownCassandra() {
        await cassClient.shutdown();
    }

    async checkUserKey(key: string, pool_id: string) {
        await this.connectToCassandra();
        const query = "SELECT * FROM ew_reg.ip_req_server WHERE key = ? AND pool_id=?";
        const result = await cassClient.execute(query, [key, pool_id], { prepare: true });
        return result.rows && result.rows.length > 0 ? result.rows[0] : null;
    }

    async checkIpExits(ip: string, pool_id: string) {
        await this.connectToCassandra();
        return []; //TESTING
        const query = "SELECT * FROM ew_reg.ip WHERE ip = ? AND pool_id=?";
        const result = await cassClient.execute(query, [ip, pool_id], { prepare: true });
        return result.rows && result.rows.length > 0;
    }

    async writeIp(ip:string, pool_id: string) {
        await this.connectToCassandra();
        return []; //TESTING
        const query = "INSERT INTO ew_reg.ip (ip, pool_id, date) VALUES (?, ?, ?)";
        const result = await cassClient.execute(query, [ip, pool_id, Date.now()], { prepare: true, consistency: cassandra.types.consistencies.localQuorum });
        return result;
    }

    async writeLog(log:string, event:any) {
        const id = ulid();
        const query = "INSERT INTO ew_reg.log (id, log, date) VALUES (?, ?, ?)";
        await this.connectToCassandra();
        const logg = {log: log, event: event};
        const result = await cassClient.execute(query, [ id, JSON.stringify(logg), Date.now()], { prepare: true, consistency: cassandra.types.consistencies.localQuorum });
        return result;
    }

    isBadEmail(email: string) {
        return email.toLocaleLowerCase().indexOf("+") != -1;
    }

    async addUserToGroup(poolId: string, username: string, groupName: string) {
        let params = {
            UserPoolId: poolId,
            Username: username,
            GroupName: groupName,
        };

        let cmd = new AdminAddUserToGroupCommand(params);
        console.log('SEND1aa');
        const res = await cognitoIDPC.send(cmd);

        return res;
    }

    async verifyGoogleRecaptcha(captcha: string) {
        console.log("ENV");
        console.log(this.env);
        const payload = {
            secret: this.env.RECAPTCHA_SERVER_KEY,
            response: captcha,
        };

        console.log(payload);

        const verifyResponse = await axios({
            method: "get",
            url: "https://www.google.com/recaptcha/api/siteverify?secret=" + payload.secret + "&response=" + payload.response,
            // params: payload,
        });

        if (verifyResponse.data.success) {
            return true;
        } else {
            console.log("verifyGoogleRecaptcha RESPONSE", verifyResponse);
            return false;
        }
    }

    async setUserPassword({ userPoolId, email }: { userPoolId: string; email: string }) {
        const adminChangePasswordCommand = new AdminSetUserPasswordCommand({
            UserPoolId: userPoolId,
            Username: email, // the email is the username
            Password: this._generatePassword(), // generate a random password
            Permanent: true, // this is needed to set the password as permanent
        });

        console.log('SEND2');
        await cognitoIDPC.send(adminChangePasswordCommand);
    }

    async verifyUserEmail({ userPoolId, username }: { userPoolId: string; username: string }) {
        const adminUpdateUserAttributesCommand = new AdminUpdateUserAttributesCommand({
            UserPoolId: userPoolId,
            Username: username,
            UserAttributes: [
                {
                    Name: 'email_verified',
                    Value: 'true'
                }
            ]
        });

        console.log('SEND_EMAIL_VERIFY');
        await cognitoIDPC.send(adminUpdateUserAttributesCommand);
    }

    async createUser({ userPoolId, email }: { userPoolId: string; email: string }) {
        const adminCreateUserCommand = new AdminCreateUserCommand({
            UserPoolId: userPoolId,
            MessageAction: "SUPPRESS", // don't send email to the user
            Username: email,
            UserAttributes: [
                {
                    Name: "email",
                    Value: email,
                },
                {
                    Name: "email_verified",
                    Value: "true",
                },
            ],
        });
        console.log('SEND3');

        const { User } = await cognitoIDPC.send(adminCreateUserCommand);
        return User;
    }

    async linkSocialAccount({
        userPoolId,
        cognitoUsername,
        providerName,
        providerUserId,
    }: {
        userPoolId: string;
        cognitoUsername?: string;
        providerName: string;
        providerUserId: string;
    }) {
        const linkProviderForUserCommand = new AdminLinkProviderForUserCommand({
            UserPoolId: userPoolId,
            DestinationUser: {
                ProviderName: "Cognito", // Cognito is the default provider
                ProviderAttributeValue: cognitoUsername, // this is the username of the user
            },
            SourceUser: {
                ProviderName: providerName, // Google or Facebook (first letter capitalized)
                ProviderAttributeName: "Cognito_Subject", // Cognito_Subject is the default attribute name
                ProviderAttributeValue: providerUserId, // this is the value of the provider
            },
        });

        console.log('SEND4');
        await cognitoIDPC.send(linkProviderForUserCommand);
    }

    async findUserByEmail(email: string, userPoolId: string): Promise<UserType | undefined> {
        const listUsersCommand = new ListUsersCommand({
            UserPoolId: userPoolId,
            Filter: `email = "${email}"`,
            Limit: 1,
        });

        console.log('SEND5');
        const { Users } = await cognitoIDPC.send(listUsersCommand);

        return Users?.[0];
    }

    async updateCognitoAttributes(event: any, attrs: AttributeType[], id: string) {
        const cognitoClient = new CognitoIdentityProviderClient({ region: this.region });

        var userAttributes: AttributeType[] = attrs;

        let input: AdminUpdateUserAttributesCommandInput = {
            UserPoolId: event.userPoolId,
            Username: event.userName, //"6c8fdaf9-e874-4af1-8270-acb203c5100f",
            UserAttributes: userAttributes,
        };
        let command = new AdminUpdateUserAttributesCommand(input);

        try {
            console.log('SEND6');
            let response: AdminUpdateUserAttributesCommandOutput = await cognitoClient.send(command);
            //console.log(`The user attributes successfully updated on the cognito.RequestId:${response.$metadata.requestId}`);
        } catch (error: any) {
            const { requestId, cfId, extendedRequestId } = error.$metadata;
            let errMsg: string = `Could not update cognito user attributes, RequestId:${requestId}`;
            if (error.name === "NotAuthorizedException") {
                errMsg = `NotAuthorizedException. ${errMsg}`;
            } else if (error.name === "ResourceNotFoundException") {
                // client fault.This exception is thrown when the Amazon Cognito service can't find the requested resource.
                errMsg = `Idebtity pool not found. ${errMsg}`;
            } else if (error.name === "UserNotFoundException") {
                // client fault.This exception is thrown when a user isn't found.
                errMsg = `User not found ${errMsg}`;
            } else if (error.name === "InvalidParameterException") {
                // client fault.This exception is thrown when a user isn't found.
                errMsg = `InvalidParameterException. ${errMsg}. ${error.message}`;
            }
            console.error(errMsg, error);
            throw error;
        }
    }

    async updateCognitoAttributesByUsername(userPoolId: string, username: string, attrs: AttributeType[]) {
        const cognitoClient = new CognitoIdentityProviderClient({ region: this.region });

        let input: AdminUpdateUserAttributesCommandInput = {
            UserPoolId: userPoolId,
            Username: username,
            UserAttributes: attrs,
        };
        let command = new AdminUpdateUserAttributesCommand(input);
        
        try {
            console.log('UPDATING_APPLE_ATTRIBUTES');
            let response: AdminUpdateUserAttributesCommandOutput = await cognitoClient.send(command);
            console.log('APPLE_ATTRIBUTES_UPDATED');
        } catch (error: any) {
            console.error('Failed to update Apple user attributes:', error);
            throw error;
        }
    }

    async postUserRegistrationAWS(event: any, id: String, referralCode: string, picture: string | undefined, nickname: string, url: string | undefined) {
        return new Promise(async (resolve, reject) => {
            //console.log(`userName:${event.userName}, userAttributes: ${JSON.stringify(event.request.userAttributes)}`);

            let userReg = {
                id: id,
                uuid: event.userName,
                // username: event.userName, //cognito id
                password: "password",
                picture: picture,
                nickname: nickname,
                email: event.request.userAttributes.email,
                locale: event.request.userAttributes.locale || "en-US",
                zoneinfo: event.request.userAttributes.zoneinfo || "America/New_York",
                referralCode: referralCode,
                referrerCode: this.fixRefCodeLenght(event.request.userAttributes["custom:referrer_code"]) ?? "",
            };

            const data = JSON.stringify(userReg);

            // resolves AWS_ACCESS_KEY_ID, AWS_SECRET_ACCESS_KEY, AWS_SESSION_TOKEN
            //aws4.sign(options);

            //console.log(`posting data:${data}, with options: ${JSON.stringify(userReg)}`);

            await axios
                .post(`${url}/users/register`, userReg, {
                    headers: {
                        user_groups: `service_admin`,
                    },
                })
                .then((response: any) => {
                    resolve(response);
                })
                .catch((err: any) => {
                    console.log(err);
                    reject(new Error(err));
                });
        });
    }

    async send_password_change_email(email: string, clientId: string) {
        if (this.password_change_sent) {
            //console.log("ALREADY SENT PASS CHANGE");
            return;
        }

        let template = "password_resetted";
        switch (clientId) {
            case "5mssegecpdh030eh9jr0e75tt2":
            case "1bf0gjemn7bifa02bimmftmnoc":
            case "6k4lusvul4nhtatuhndl3ut0em":
                template = "password_resetted_wallet";
                break;

            default:
                break;
        }
        //console.log("PASSWORDEMAIL " + template);

        const params = {
            // SendTemplatedEmailRequest
            Source: "<EMAIL>", // required
            Destination: {
                // Destination
                ToAddresses: [email],
            },
            ReplyToAddresses: ["<EMAIL>"],
            ReturnPath: "<EMAIL>",
            Template: template, // required
            TemplateData: '{ "WWWW":"QQQ" }' /* required */,
        };
        this.password_change_sent = true;
        await this._send_email(params);
    }

    async send_welcome_email(email: string, clientId: string) {
        if (this.welcome_sent) {
            //if aws fails to connect to our server it retries and sends multiple emails
            //console.log("ALREADY SENT PASS WELCOME");
            return;
        }
        let template = "ew_welcome_ew";
        switch (clientId) {
            case "1bf0gjemn7bifa02bimmftmnoc":
            case "6k4lusvul4nhtatuhndl3ut0em":
            case "5mssegecpdh030eh9jr0e75tt2":
                template = "ew_welcome_ew_wallet";
                break;
            default:
                break;
        }

        //console.log("WELCOMEMAIL " + template);

        const params = {
            // SendTemplatedEmailRequest
            Source: "<EMAIL>", // required
            Destination: {
                ToAddresses: [email],
            },
            ReplyToAddresses: ["<EMAIL>"],
            ReturnPath: "<EMAIL>",
            Template: template, // required
            TemplateData: '{ "WWWW":"QQQ" }' /* required */,
        };
        this.welcome_sent = true;
        await this._send_email(params);
    }

    async _send_email(params: any) {
        //console.log("SENDING EMAIL");
        //console.log(params);

        const client = new SESClient({ region: "eu-central-1" });

        const command = new SendTemplatedEmailCommand(params);
        console.log('SEND7');
        const response = await client.send(command);
        //console.log(response);
        //   var sendPromise = new AWS.SES({ apiVersion: "2010-12-01" })
        //   .sendTemplatedEmail(params)
        //   .promise();

        // // Handle promise's fulfilled/rejected states
        //   await sendPromise
        //   .then(function (data) {
        //     console.log(data);
        //   })
        //   .catch(function (err) {
        //     console.error(err, err.stack);
        //   });
    }

    _generatePassword = () => {
        return `${Math.random() // Generate random number, eg: 0.123456
            .toString(36) // Convert  to base-36 : "0.4fzyo82mvyr"
            .slice(-8)}42_-.A`; // Cut off last 8 characters : "yo82mvyr" and add a number because the cognito password policy requires a number
    };

    _generateNickname = (email: string) => {
        return generateFromEmail(email, 6).substring(0, 20);
    };

    generateReferralCode = () => {
        const randomStr = ulid(); // generates random string having length more than 10 chars
        const referralCode = randomStr.slice(randomStr.length - 10, randomStr.length);
        return referralCode;
    };

    fixLength(str: string, lenght: number) {
        if (str && str.length > lenght) {
            str = str.slice(0, lenght);
        }
        return str;
    }

    fixRefCodeLenght(str: string) {
        return this.fixLength(str, 10);
    }

    resolveReferrerCode(event: any): AttributeType | undefined {
        const referrerCode = this.fixRefCodeLenght(event.request.userAttributes["custom:referrer_code"]);
        if (referrerCode && referrerCode.length > 0) {
            return {
                Name: "custom:referrer_code" /* optional */,
                Value: referrerCode,
            } as AttributeType;
        }
        return undefined;
    }
}
