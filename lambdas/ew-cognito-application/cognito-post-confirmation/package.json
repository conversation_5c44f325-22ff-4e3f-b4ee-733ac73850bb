{"name": "cognito-dev-post-confirmation", "version": "1.0.0", "description": "hello world sample for NodeJS", "main": "app.js", "author": "SAM CLI", "license": "MIT", "dependencies": {"@aws-sdk/client-cognito-identity-provider": "^3.490.0", "@aws-sdk/client-lambda": "^3.516.0", "@aws-sdk/client-ses": "^3.501.0", "axios": "^1.6.5", "cassandra-driver": "^4.7.2", "esbuild": "^0.14.14", "ulidx": "^0.4.0", "unique-username-generator": "^1.3.0"}, "scripts": {"unit": "jest", "lint": "eslint '*.ts' --quiet --fix", "compile": "tsc", "test": "npm run compile && npm run unit"}, "devDependencies": {"@types/aws-lambda": "^8.10.119", "@types/jest": "^27.4.0", "@types/node": "^18.15.3", "@typescript-eslint/eslint-plugin": "^5.10.2", "@typescript-eslint/parser": "^5.10.2", "esbuild-jest": "^0.5.0", "eslint": "^8.8.0", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0", "jest": "^27.5.0", "prettier": "^2.5.1", "ts-node": "^10.4.0", "typescript": "^4.5.5"}}