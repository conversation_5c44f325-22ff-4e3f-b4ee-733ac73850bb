# More information about the configuration file can be found here:
# https://docs.aws.amazon.com/serverless-application-model/latest/developerguide/serverless-sam-cli-config.html
version = 0.1

[default]
[default.global.parameters]
stack_name = "ew-cognito-application-prd"

[default.build.parameters]
cached = true
parallel = true
beta_features = true

[default.validate.parameters]
lint = true

[default.deploy.parameters]
capabilities = "CAPABILITY_IAM"
confirm_changeset = true
resolve_s3 = true
s3_bucket = "ew"
s3_prefix = "ew-cognito-application-prd"
region = "eu-central-1"
image_repositories = []

[default.package.parameters]
resolve_s3 = false

[default.sync.parameters]
watch = true
beta_features = true

[default.local_start_api.parameters]
warm_containers = "EAGER"

[default.local_start_lambda.parameters]
warm_containers = "EAGER"
