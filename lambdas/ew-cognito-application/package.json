{"name": "ew-cognito-application", "version": "1.0.0", "description": "<p align=\"center\">     <a href=\"https://ezwy.dev/\" target=\"blank\"><img src=\"https://github.com/wodo-platform/wodo-branding/blob/main/src/img/branding/wodo_logo.png\" width=\"320\" alt=\"Ezywy Platform\" /></a> </p>", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "deploy": "npm i && npm run package && aws lambda update-function-code --function-name cognito-verify-challenge --zip-file fileb://function.zip", "deploy-prod": "npm i && npm run package && aws lambda update-function-code --profile wodo-prod --function-name cognito-verify-challenge --zip-file fileb://function.zip", "package": "npm run build && zip -r function.zip dist node_modules package.json", "sam": "sam build && cp ./sf-class2-root.crt .aws-sam/build/ewcognitoprdapplicationCognitoPostConfirmation/ && sam deploy --guided", "sam-check": "ls -la ./sf-class2-root.crt && ls -la .aws-sam/build/ewcognitoprdapplicationCognitoPostConfirmation/"}, "author": "", "license": "ISC"}