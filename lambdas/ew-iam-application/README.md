# lambda-authorizer-jwt-cognito

This project contains source code and supporting files for a serverless application that you can deploy with the SAM CLI. It includes the following files and folders.

- hello-world - Code for the application's Lambda function written in TypeScript.
- events - Invocation events that you can use to invoke the function.
- hello-world/tests - Unit tests for the application code. 
- template.yaml - A template that defines the application's AWS resources.

The application uses several AWS resources, including Lambda functions and an API Gateway API. These resources are defined in the `template.yaml` file in this project. You can update the template to add AWS resources through the same deployment process that updates your application code.

If you prefer to use an integrated development environment (IDE) to build and test your application, you can use the AWS Toolkit.  
The AWS Toolkit is an open source plug-in for popular IDEs that uses the SAM CLI to build and deploy serverless applications on AWS. The AWS Toolkit also adds a simplified step-through debugging experience for Lambda function code. See the following links to get started.

* [CLion](https://docs.aws.amazon.com/toolkit-for-jetbrains/latest/userguide/welcome.html)
* [GoLand](https://docs.aws.amazon.com/toolkit-for-jetbrains/latest/userguide/welcome.html)
* [IntelliJ](https://docs.aws.amazon.com/toolkit-for-jetbrains/latest/userguide/welcome.html)
* [WebStorm](https://docs.aws.amazon.com/toolkit-for-jetbrains/latest/userguide/welcome.html)
* [Rider](https://docs.aws.amazon.com/toolkit-for-jetbrains/latest/userguide/welcome.html)
* [PhpStorm](https://docs.aws.amazon.com/toolkit-for-jetbrains/latest/userguide/welcome.html)
* [PyCharm](https://docs.aws.amazon.com/toolkit-for-jetbrains/latest/userguide/welcome.html)
* [RubyMine](https://docs.aws.amazon.com/toolkit-for-jetbrains/latest/userguide/welcome.html)
* [DataGrip](https://docs.aws.amazon.com/toolkit-for-jetbrains/latest/userguide/welcome.html)
* [VS Code](https://docs.aws.amazon.com/toolkit-for-vscode/latest/userguide/welcome.html)
* [Visual Studio](https://docs.aws.amazon.com/toolkit-for-visual-studio/latest/user-guide/welcome.html)

# Samples

## Http API Flow Samples

**event in the handler**

```JSON
2023-03-23T14:30:49.639Z	c31f72dc-f37f-41c2-b947-38190d2f41d3	INFO	event: {
    "version": "2.0",
    "type": "REQUEST",
    "routeArn": "arn:aws:execute-api:eu-central-1:************:8ig41ue9pa/v1/GET/api/accounts",
    "identitySource": [
        "eyJraWQiOiJUZGpESXFCUm5xc29wRjFLT0NCMXJsbnRLaXNORVVwVkJZN3ljcnFFcFE4PSIsImFsZyI6IlJTMjU2In0.*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.tATIDsBSt4d4rUCdVC9156_xxdEz4h_W1X0Ld6nZxymuVZ7bnO3jfD16KS5fz6kapzgMcb0c5shEOqm18X3QOcQzI1vcAXddsqhqo-MJWM4BjApZJWVTCchXKej_Qyx9ZDEtDggKbs8JBSagm3as-6O8SrVi8xnQ_3iVQso_1FDRoVHadmoTmfiqc28ggaJ5qxoNJ921XqurJPRvl7uuxE24WhWVBICUwAF4FuiXB6_5ynHoqCYnDi3zsNOqL3Cuc5t7uqdT1hUjr02AlszzSlFk5iaysjInYV5Z7B1TooX2jUqJTWtKDT16hGBxh8GuRsJmqeaOm0gJ_LwjcwnNPQ"
    ],
    "routeKey": "ANY /api/{proxy+}",
    "rawPath": "/v1/api/accounts",
    "rawQueryString": "",
    "headers": {
        "accept": "*/*",
        "accept-encoding": "gzip, deflate, br",
        "authorization": "eyJraWQiOiJUZGpESXFCUm5xc29wRjFLT0NCMXJsbnRLaXNORVVwVkJZN3ljcnFFcFE4PSIsImFsZyI6IlJTMjU2In0.*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.tATIDsBSt4d4rUCdVC9156_xxdEz4h_W1X0Ld6nZxymuVZ7bnO3jfD16KS5fz6kapzgMcb0c5shEOqm18X3QOcQzI1vcAXddsqhqo-MJWM4BjApZJWVTCchXKej_Qyx9ZDEtDggKbs8JBSagm3as-6O8SrVi8xnQ_3iVQso_1FDRoVHadmoTmfiqc28ggaJ5qxoNJ921XqurJPRvl7uuxE24WhWVBICUwAF4FuiXB6_5ynHoqCYnDi3zsNOqL3Cuc5t7uqdT1hUjr02AlszzSlFk5iaysjInYV5Z7B1TooX2jUqJTWtKDT16hGBxh8GuRsJmqeaOm0gJ_LwjcwnNPQ",
        "content-length": "0",
        "host": "8ig41ue9pa.execute-api.eu-central-1.amazonaws.com",
        "postman-token": "57bff9c6-4fbe-4a83-aad3-987a2e7702cc",
        "user-agent": "PostmanRuntime/7.30.0",
        "x-amzn-trace-id": "Root=1-641c6298-7ee23e1b6ac576e44e1aa649",
        "x-forwarded-for": "***********",
        "x-forwarded-port": "443",
        "x-forwarded-proto": "https"
    },
    "requestContext": {
        "accountId": "************",
        "apiId": "8ig41ue9pa",
        "domainName": "8ig41ue9pa.execute-api.eu-central-1.amazonaws.com",
        "domainPrefix": "8ig41ue9pa",
        "http": {
            "method": "GET",
            "path": "/v1/api/accounts",
            "protocol": "HTTP/1.1",
            "sourceIp": "***********",
            "userAgent": "PostmanRuntime/7.30.0"
        },
        "requestId": "CPRX4jElFiAEJzw=",
        "routeKey": "ANY /api/{proxy+}",
        "stage": "v1",
        "time": "23/Mar/2023:14:30:48 +0000",
        "timeEpoch": *************
    },
    "pathParameters": {
        "proxy": "accounts"
    }
}

```

**Cognito getUser response:**

```typescript
   {
       "Username": "6c8fdaf9-e874-4af1-8270-aaaaaaaaaaa",
       "UserAttributes": [
           {
               "Name": "sub",
               "Value": "6c8fdaf9-e874-4af1-8270-aaaaaaaaaaa"
           },
           {
               "Name": "zoneinfo",
               "Value": "Europe\/Brussles"
           },
           {
               "Name": "website",
               "Value": "01GQ7Y2FHD0C8CXXXXXXXXXXXX"
           },
           {
               "Name": "email_verified",
               "Value": "true"
           },
           {
               "Name": "gender",
               "Value": "m"
           },
           {
               "Name": "custom:user_id",
               "Value": "01GQ7Y2FHD0C8CXXXXXXXXXXXX"
           },
           {
               "Name": "locale",
               "Value": "en-US"
           },
           {
               "Name": "middle_name",
               "Value": "01GQ7Y2FHD0C8CXXXXXXXXXXXX"
           },
           {
               "Name": "custom:tetnant_id",
               "Value": "01GQNWXR1XC83GYYYYYYYYYYYY"
           },
           {
               "Name": "nickname",
               "Value": "01GQNWXR1XC83GYYYYYYYYYYYY"
           },
           {
               "Name": "email",
               "Value": "<EMAIL>"
           }
       ],
       "@metadata": {
       }
   }
```

**Verify token response:**

```TYPESCRIPT
{
  sub: '6c8fdaf9-e874-4af1-8270-acb203c5100f',
  'cognito:groups': [ 'admin' ],
  iss: 'https://cognito-idp.eu-central-1.amazonaws.com/eu-central-1_eInVWpQWI',
  client_id: 'umlu7il6sho5cqf87dldo9eai',
  origin_jti: '96312e32-1e7b-41a2-8db2-e6cf7d14cb0a',
  event_id: '1ac17cec-978b-47cc-a015-ffc9e8d1a575',
  token_use: 'access',
  scope: 'aws.cognito.signin.user.admin',
  auth_time: 1679581367,
  exp: 1679584967,
  iat: 1679581367,
  jti: '2d066987-8d01-48b0-96ed-e4a170d0f57d',
  username: '6c8fdaf9-e874-4af1-8270-acb203c5100f'
}
```

## Http REST API Flow Samples

**event in the handler**

```
{
  type: 'REQUEST',
  methodArn: 'arn:aws:execute-api:eu-central-1:************:csxavjc8ve/test/GET/api/v1/wallets',
  resource: '/{proxy+}',
  path: '/api/v1/wallets',
  httpMethod: 'GET',
  headers: {
    accept: '*/*',
    authorization: '***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************',
    'content-type': 'application/json',
    Host: 'csxavjc8ve.execute-api.eu-central-1.amazonaws.com',
    'user-agent': 'insomnia/8.4.5',
    'X-Amzn-Trace-Id': 'Root=1-6701a6a4-57011d4d3d92e713714c450d',
    'x-api-key': 'e3S0bxGp3sao13toAGbPUaSw1jIe5K9t6RjP76Hf',
    'X-Forwarded-For': '************',
    'X-Forwarded-Port': '443',
    'X-Forwarded-Proto': 'https'
  },
  multiValueHeaders: {
    accept: [ '*/*' ],
    authorization: [
      '***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************'
    ],
    'content-type': [ 'application/json' ],
    Host: [ 'csxavjc8ve.execute-api.eu-central-1.amazonaws.com' ],
    'user-agent': [ 'insomnia/8.4.5' ],
    'X-Amzn-Trace-Id': [ 'Root=1-6701a6a4-57011d4d3d92e713714c450d' ],
    'x-api-key': [ 'e3S0bxGp3sao13toAGbPUaSw1jIe5K9t6RjP76Hf' ],
    'X-Forwarded-For': [ '************' ],
    'X-Forwarded-Port': [ '443' ],
    'X-Forwarded-Proto': [ 'https' ]
  },
  queryStringParameters: {},
  multiValueQueryStringParameters: {},
  pathParameters: { proxy: 'api/v1/wallets' },
  stageVariables: {},
  requestContext: {
    resourceId: 'su4bto',
    resourcePath: '/{proxy+}',
    httpMethod: 'GET',
    extendedRequestId: 'fMb5rGVKliAETBQ=',
    requestTime: '05/Oct/2024:20:50:44 +0000',
    path: '/test/api/v1/wallets',
    accountId: '************',
    protocol: 'HTTP/1.1',
    stage: 'test',
    domainPrefix: 'csxavjc8ve',
    requestTimeEpoch: *************,
    requestId: 'ef02f86a-17c1-4f67-aa2d-2a913c9554c9',
    identity: {
      cognitoIdentityPoolId: null,
      cognitoIdentityId: null,
      apiKey: 'e3S0bxGp3sao13toAGbPUaSw1jIe5K9t6RjP76Hf',
      principalOrgId: null,
      cognitoAuthenticationType: null,
      userArn: null,
      apiKeyId: 'qq3yp5j4g3',
      userAgent: 'insomnia/8.4.5',
      accountId: null,
      caller: null,
      sourceIp: '************',
      accessKey: null,
      cognitoAuthenticationProvider: null,
      user: null
    },
    domainName: 'csxavjc8ve.execute-api.eu-central-1.amazonaws.com',
    deploymentId: 'w3ea51',
    apiId: 'csxavjc8ve'
  }
}
```

Lambda response

```
Authentication is successfull. {
  principalId: '8b0f9a4e-e438-4665-8af4-231069a12fca',
  policyDocument: { Version: '2012-10-17', Statement: [ [Object] ] },
  context: {
    username: '8b0f9a4e-e438-4665-8af4-231069a12fca',
    user_id: '01GQ7Y2FHBSWKHTJMEFTC3ADW2',
    user_uuid: '8b0f9a4e-e438-4665-8af4-231069a12fca',
    email: '<EMAIL>',
    email_verified: undefined,
    zoneinfo: undefined,
    locale: undefined,
    user_groups: 'admin,wodo_user,t_admin'
  }
}
```

## Deploy the sample application

The Serverless Application Model Command Line Interface (SAM CLI) is an extension of the AWS CLI that adds functionality for building and testing Lambda applications. It uses Docker to run your functions in an Amazon Linux environment that matches Lambda. It can also emulate your application's build environment and API.

To use the SAM CLI, you need the following tools.

* SAM CLI - [Install the SAM CLI](https://docs.aws.amazon.com/serverless-application-model/latest/developerguide/serverless-sam-cli-install.html)
* Node.js - [Install Node.js 14](https://nodejs.org/en/), including the NPM package management tool.
* Docker - [Install Docker community edition](https://hub.docker.com/search/?type=edition&offering=community)

To build and deploy your application for the first time, run the following in your shell:

```bash
sam build
sam deploy --guided
```

The first command will build the source of your application. The second command will package and deploy your application to AWS, with a series of prompts:

* **Stack Name**: The name of the stack to deploy to CloudFormation. This should be unique to your account and region, and a good starting point would be something matching your project name.
* **AWS Region**: The AWS region you want to deploy your app to.
* **Confirm changes before deploy**: If set to yes, any change sets will be shown to you before execution for manual review. If set to no, the AWS SAM CLI will automatically deploy application changes.
* **Allow SAM CLI IAM role creation**: Many AWS SAM templates, including this example, create AWS IAM roles required for the AWS Lambda function(s) included to access AWS services. By default, these are scoped down to minimum required permissions. To deploy an AWS CloudFormation stack which creates or modifies IAM roles, the `CAPABILITY_IAM` value for `capabilities` must be provided. If permission isn't provided through this prompt, to deploy this example you must explicitly pass `--capabilities CAPABILITY_IAM` to the `sam deploy` command.
* **Save arguments to samconfig.toml**: If set to yes, your choices will be saved to a configuration file inside the project, so that in the future you can just re-run `sam deploy` without parameters to deploy changes to your application.

You can find your API Gateway Endpoint URL in the output values displayed after deployment.

## Use the SAM CLI to build and test locally

Build your application with the `sam build` command.

```bash
lambda-authorizer-jwt-cognito$ sam build
```

The SAM CLI installs dependencies defined in `hello-world/package.json`, compiles TypeScript with esbuild, creates a deployment package, and saves it in the `.aws-sam/build` folder.

Test a single function by invoking it directly with a test event. An event is a JSON document that represents the input that the function receives from the event source. Test events are included in the `events` folder in this project.

Run functions locally and invoke them with the `sam local invoke` command.

```bash
lambda-authorizer-jwt-cognito$ sam local invoke HelloWorldFunction --event events/event.json
```

The SAM CLI can also emulate your application's API. Use the `sam local start-api` to run the API locally on port 3000.

```bash
lambda-authorizer-jwt-cognito$ sam local start-api
lambda-authorizer-jwt-cognito$ curl http://localhost:3000/
```

The SAM CLI reads the application template to determine the API's routes and the functions that they invoke. The `Events` property on each function's definition includes the route and method for each path.

```yaml
      Events:
        HelloWorld:
          Type: Api
          Properties:
            Path: /hello
            Method: get
```

## Add a resource to your application
The application template uses AWS Serverless Application Model (AWS SAM) to define application resources. AWS SAM is an extension of AWS CloudFormation with a simpler syntax for configuring common serverless application resources such as functions, triggers, and APIs. For resources not included in [the SAM specification](https://github.com/awslabs/serverless-application-model/blob/master/versions/2016-10-31.md), you can use standard [AWS CloudFormation](https://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-template-resource-type-ref.html) resource types.

## Fetch, tail, and filter Lambda function logs

To simplify troubleshooting, SAM CLI has a command called `sam logs`. `sam logs` lets you fetch logs generated by your deployed Lambda function from the command line. In addition to printing the logs on the terminal, this command has several nifty features to help you quickly find the bug.

`NOTE`: This command works for all AWS Lambda functions; not just the ones you deploy using SAM.

```bash
lambda-authorizer-jwt-cognito$ sam logs -n HelloWorldFunction --stack-name lambda-authorizer-jwt-cognito --tail
```

You can find more information and examples about filtering Lambda function logs in the [SAM CLI Documentation](https://docs.aws.amazon.com/serverless-application-model/latest/developerguide/serverless-sam-cli-logging.html).

## Unit tests

Tests are defined in the `hello-world/tests` folder in this project. Use NPM to install the [Jest test framework](https://jestjs.io/) and run unit tests.

```bash
lambda-authorizer-jwt-cognito$ cd hello-world
hello-world$ npm install
hello-world$ npm run test
```

## Cleanup

To delete the sample application that you created, use the AWS CLI. Assuming you used your project name for the stack name, you can run the following:

```bash
aws cloudformation delete-stack --stack-name lambda-authorizer-jwt-cognito
```

## Resources

See the [AWS SAM developer guide](https://docs.aws.amazon.com/serverless-application-model/latest/developerguide/what-is-sam.html) for an introduction to SAM specification, the SAM CLI, and serverless application concepts.

Next, you can use AWS Serverless Application Repository to deploy ready to use Apps that go beyond hello world samples and learn how authors developed their applications: [AWS Serverless Application Repository main page](https://aws.amazon.com/serverless/serverlessrepo/)
