{"version": "2.0", "type": "REQUEST", "routeArn": "arn:aws:execute-api:eu-central-1:************:8ig41ue9pa/v1/GET/api/accounts", "identitySource": ["eyJraWQiOiJUZGpESXFCUm5xc29wRjFLT0NCMXJsbnRLaXNORVVwVkJZN3ljcnFFcFE4PSIsImFsZyI6IlJTMjU2In0.*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.tATIDsBSt4d4rUCdVC9156_xxdEz4h_W1X0Ld6nZxymuVZ7bnO3jfD16KS5fz6kapzgMcb0c5shEOqm18X3QOcQzI1vcAXddsqhqo-MJWM4BjApZJWVTCchXKej_Qyx9ZDEtDggKbs8JBSagm3as-6O8SrVi8xnQ_3iVQso_1FDRoVHadmoTmfiqc28ggaJ5qxoNJ921XqurJPRvl7uuxE24WhWVBICUwAF4FuiXB6_5ynHoqCYnDi3zsNOqL3Cuc5t7uqdT1hUjr02AlszzSlFk5iaysjInYV5Z7B1TooX2jUqJTWtKDT16hGBxh8GuRsJmqeaOm0gJ_LwjcwnNPQ"], "routeKey": "ANY /api/{proxy+}", "rawPath": "/v1/api/accounts", "rawQueryString": "", "headers": {"accept": "*/*", "accept-encoding": "gzip, deflate, br", "authorization": "eyJraWQiOiJUZGpESXFCUm5xc29wRjFLT0NCMXJsbnRLaXNORVVwVkJZN3ljcnFFcFE4PSIsImFsZyI6IlJTMjU2In0.*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.tATIDsBSt4d4rUCdVC9156_xxdEz4h_W1X0Ld6nZxymuVZ7bnO3jfD16KS5fz6kapzgMcb0c5shEOqm18X3QOcQzI1vcAXddsqhqo-MJWM4BjApZJWVTCchXKej_Qyx9ZDEtDggKbs8JBSagm3as-6O8SrVi8xnQ_3iVQso_1FDRoVHadmoTmfiqc28ggaJ5qxoNJ921XqurJPRvl7uuxE24WhWVBICUwAF4FuiXB6_5ynHoqCYnDi3zsNOqL3Cuc5t7uqdT1hUjr02AlszzSlFk5iaysjInYV5Z7B1TooX2jUqJTWtKDT16hGBxh8GuRsJmqeaOm0gJ_LwjcwnNPQ", "content-length": "0", "host": "8ig41ue9pa.execute-api.eu-central-1.amazonaws.com", "postman-token": "57bff9c6-4fbe-4a83-aad3-987a2e7702cc", "user-agent": "PostmanRuntime/7.30.0", "x-amzn-trace-id": "Root=1-641c6298-7ee23e1b6ac576e44e1aa649", "x-forwarded-for": "***********", "x-forwarded-port": "443", "x-forwarded-proto": "https"}, "requestContext": {"accountId": "************", "apiId": "8ig41ue9pa", "domainName": "8ig41ue9pa.execute-api.eu-central-1.amazonaws.com", "domainPrefix": "8ig41ue9pa", "http": {"method": "GET", "path": "/v1/api/accounts", "protocol": "HTTP/1.1", "sourceIp": "***********", "userAgent": "PostmanRuntime/7.30.0"}, "requestId": "CPRX4jElFiAEJzw=", "routeKey": "ANY /api/{proxy+}", "stage": "v1", "time": "23/Mar/2023:14:30:48 +0000", "timeEpoch": *************}, "pathParameters": {"proxy": "accounts"}}