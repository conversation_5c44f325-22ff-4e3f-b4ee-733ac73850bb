/**
 * AWS API Gateway Lambda authorizer implementation with Cognito. 
 * See more details at https://docs.aws.amazon.com/apigateway/latest/developerguide/http-api-lambda-authorizer.html
 * 
 * AWS API Gateway's defail JWT authorizer can not map JWT's custom claims, to the headers of integrated hapi endpoint
 * due to the bug https://github.com/aws/aws-cdk/issues/22010 .
 * Therefore this custom JWT authorizer is implemented as `AWS API Gateway Lambda authorizer`
 */
import {  AwsCognitoService } from './cognito/aws-cognito-service';
import { Context} from 'aws-lambda';

import { RestApiEvent, HttpApiEvent, Authorizer, AuthorizerTypeRestApi, AuthoriationResponse, }  from './authorizers/authorizer';
import { AuthorizerFactory }  from './authorizers/authorizer-factory';
import { Logger } from './logger/logger';
import { LoggerFactory } from './logger/logger-factory';

/**
 * Everything that's outside of the handler function is executed only during the cold start and is subsequently reused during warm invocations. 
 */
const awsRegion = process.env.AWS_REGION;
const cognitoPoolId = process.env.COGNITO_POOL_ID;
const cognitoClientIds = process.env.COGNITO_CLIENT_IDS;
const logger:Logger = LoggerFactory.getInstance().getLogger();
const awsCognitoService: AwsCognitoService = new AwsCognitoService(awsRegion, cognitoPoolId, cognitoClientIds);
const clazzName = "app";

export const lambdaHandler = async (event: RestApiEvent | HttpApiEvent, context: Context): Promise<AuthoriationResponse> => {

  logger.debug(clazzName,`Received event`,event);
  const authorizer:Authorizer = AuthorizerFactory.buildAuthorizer(event,awsCognitoService);
  logger.debug(clazzName,`Received a request from ${authorizer.getAuthorizerType()}.`);

  let response:AuthoriationResponse = await authorizer.authorize(event, context);
  return response;
};
