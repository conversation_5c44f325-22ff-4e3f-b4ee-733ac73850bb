{"name": "jwt-authorizer", "version": "1.0.0", "description": "hello world sample for NodeJS", "main": "app.js", "repository": "https://github.com/awslabs/aws-sam-cli/tree/develop/samcli/local/init/templates/cookiecutter-aws-sam-hello-nodejs", "author": "SAM CLI", "license": "MIT", "dependencies": {"aws-sdk": "^2.1691.0", "aws-jwt-verify": "^4.0.0", "esbuild": "^0.14.14"}, "scripts": {"unit": "jest", "lint": "eslint '*.ts' --quiet --fix", "compile": "tsc", "test": "npm run compile && npm run unit"}, "devDependencies": {"@aws-sdk/client-lambda": "^3.662.0", "@types/aws-lambda": "^8.10.92", "@types/jest": "^27.4.0", "@types/node": "^17.0.13", "@typescript-eslint/eslint-plugin": "^5.10.2", "@typescript-eslint/parser": "^5.10.2", "esbuild-jest": "^0.5.0", "eslint": "^8.8.0", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0", "jest": "^27.5.0", "prettier": "^2.5.1", "ts-node": "^10.4.0", "typescript": "^4.5.5"}}