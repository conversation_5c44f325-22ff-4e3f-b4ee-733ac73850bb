import { AwsCognitoService } from "../cognito/aws-cognito-service";
import { LoggerFactory } from "../logger/logger-factory";
import { Authorizer, HttpApiEvent, RestApiEvent } from "./authorizer";
import { HttpApiAuthorizer } from "./http-api-authorizer";
import { HttpRestApiAuthorizer } from "./http-rest-api-authorizer";

/**
 * AuthorizerFactory class is a factory class to create authorizer objects based on the lambdah authorizer event type.
 * The event type is determined by the API Gateway type (REST API or HTTP API).
 */
export class AuthorizerFactory {

    public static clazzName = "AuthorizerFactory";

    // Define type guards for each API type
    static isRestApiEvent(event: any): event is RestApiEvent {
        return event && event.methodArn;
    }

    static isHttpApiEvent(event: any): event is HttpApiEvent {
        return event && event.version === '2.0';
    }

    /**
     * Build authorizer based on the event type.
     * 
     * @param event 
     * @param awsCognitoService 
     * @returns a concrete authorizer implementation based on the event type.
     */
    static buildAuthorizer(event: RestApiEvent | HttpApiEvent, awsCognitoService: AwsCognitoService): Authorizer {
        if (AuthorizerFactory.isHttpApiEvent(event)) {
            return new HttpApiAuthorizer(awsCognitoService);
        } else if (AuthorizerFactory.isRestApiEvent(event)) {
            return new HttpRestApiAuthorizer(awsCognitoService);
        } else {
            LoggerFactory.getInstance().getLogger().error(AuthorizerFactory.clazzName,`Unsupported authorizer lambda event type.`, event);
            throw new Error(`Unsupported authorizer lambda event type:${event}.`);
        }
    }

}