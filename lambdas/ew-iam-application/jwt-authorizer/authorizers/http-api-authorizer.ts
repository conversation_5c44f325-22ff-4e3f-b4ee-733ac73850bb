import { APIGatewayAuthorizerResultContext, APIGatewaySimpleAuthorizerWithContextResult,Context } from "aws-lambda";
import { Authorizer, AuthorizerType, AuthorizerTypeHttpApi, HttpApiEvent } from "./authorizer";
import { CognitoUser } from "../cognito/cognito-user";
import { AuthorizerBase } from "./authorizer-base";
import { AwsCognitoService } from "../cognito/aws-cognito-service";
import { Logger } from "../logger/logger";
import { LoggerFactory } from "../logger/logger-factory";

export class HttpApiAuthorizer extends AuthorizerBase implements Authorizer {

    private logger:Logger;
    public clazzName = "HttpApiAuthorizer";

    constructor(awsCognitoService: AwsCognitoService) {
        super(awsCognitoService);
        this.logger = LoggerFactory.getInstance().getLogger();
    }

    getAuthorizerType(): AuthorizerType {
        return AuthorizerTypeHttpApi;
    }

    async authorize(event: HttpApiEvent, context: Context): Promise<APIGatewaySimpleAuthorizerWithContextResult<APIGatewayAuthorizerResultContext>> {
        const token: string = await this.resolveToken(event, context);
        if (!token || token == "") {
            let response = await this.getAuthResponse(event, context, false, null);
            this.logger.error(this.clazzName,`token is empty. Returning unauthorized response`, response);
            return response;
        }

        let cognitoUser: CognitoUser;;
        try {
            cognitoUser = await this.awsCognitoService.getUserByAccessToken(token);
            let user_groups: any = await this.awsCognitoService.resolveUserGroups(token);
            cognitoUser.user_groups = typeof user_groups == 'object' ? user_groups.join(',') : user_groups;

            let response = await this.getAuthResponse(event, context, true, cognitoUser);
            this.logger.debug(this.clazzName,`Authentication is successfull.`, response);
            return response;

        } catch (error) {
            let response = await this.getAuthResponse(event, context, false, undefined);
            this.logger.error(this.clazzName,`An error occurred while processing token. Authentication is NOT successfull. returning resposne ${response}`, error);
            return response;
        }
    }

    async resolveToken(event: HttpApiEvent, context: Context): Promise<string> {

        const token: string = event.identitySource ? event.identitySource[0] : '';
        this.logger.debug(this.clazzName,`  resolved token:${token}.`);
        return token;
    }

    async getAuthResponse(event: HttpApiEvent, context: Context, isAuthorized: boolean, congnitoUser: CognitoUser): Promise<APIGatewaySimpleAuthorizerWithContextResult<APIGatewayAuthorizerResultContext>> {
        return {
            "isAuthorized": isAuthorized,
            "context": congnitoUser ? { ...congnitoUser } : {}
        } as APIGatewaySimpleAuthorizerWithContextResult<APIGatewayAuthorizerResultContext>;
    }
}