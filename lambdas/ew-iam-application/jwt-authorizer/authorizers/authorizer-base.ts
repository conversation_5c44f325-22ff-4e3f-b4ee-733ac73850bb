import { AwsCognitoService } from "../cognito/aws-cognito-service";

/**
 * Base class for authorizers to extend. 
 * Authorizers are used to authorize the incoming requests to the API Gateway.
 */
export abstract class AuthorizerBase {

    protected awsCognitoService: AwsCognitoService;
    
    constructor(awsCognitoService: AwsCognitoService) {
        this.awsCognitoService = awsCognitoService;
    }
}