import { APIGatewayAuthorizerResultContext, AuthResponse, Context, PolicyDocument, StatementEffect, APIGatewayRequestAuthorizerEvent } from "aws-lambda";
import { Authorizer, AuthorizerType, RestApiEvent, AuthorizerTypeRestApi } from "./authorizer";
import { AuthorizerBase } from "./authorizer-base";
import { AwsCognitoService } from "../cognito/aws-cognito-service";
import { CognitoUser } from "../cognito/cognito-user";
import { Logger } from "../logger/logger";
import { LoggerFactory } from "../logger/logger-factory";

/**
 * HTTP REST API Authorizer implementation
 */
export class HttpRestApiAuthorizer extends AuthorizerBase implements Authorizer {

    private logger:Logger;
    public clazzName = "HttpRestApiAuthorizer";

    constructor(awsCognitoService: AwsCognitoService) {
        super(awsCognitoService);
        this.logger = LoggerFactory.getInstance().getLogger();
    }
    
    getAuthorizerType(): AuthorizerType {
        return AuthorizerTypeRestApi;
    }

    async authorize(event: RestApiEvent, context: Context): Promise<AuthResponse> {
        const token: string = await this.resolveToken(event, context);
        if (!token || token == "") {
            let response = await this.getAuthResponse(event, context, false, null);
            this.logger.error(this.clazzName,`token is empty. Returning unauthorized response`, response);
            return response;
        }

        let cognitoUser: CognitoUser;;
        try {
            cognitoUser = await this.awsCognitoService.getUserByAccessToken(token);
            let user_groups: any = await this.awsCognitoService.resolveUserGroups(token);
            cognitoUser.user_groups = typeof user_groups == 'object' ? user_groups.join(',') : user_groups;

            let response = await this.getAuthResponse(event, context, true, cognitoUser);
            this.logger.debug(this.clazzName,`Authentication is successfull.`, JSON.stringify(response));
            return response;

        } catch (error) {
            let response = await this.getAuthResponse(event, context, false, undefined);
            this.logger.error(this.clazzName,`An error occurred while processing token. Authentication is NOT successfull. returning resposne ${JSON.stringify(response)}`, error);
            return response;
        }
    }

    async resolveToken(event: RestApiEvent, context: Context): Promise<string> {
        const token = (event as APIGatewayRequestAuthorizerEvent).headers?.authorization || '';
        this.logger.debug(this.clazzName,`resolved token:${token}.`);
        return token;
    }

    async getAuthResponse(event: RestApiEvent, context: Context, isAuthorized: boolean, congnitoUser: CognitoUser): Promise<AuthResponse> {
       return this.generateRESTAPIPolicy(congnitoUser? congnitoUser.username: "unknown", isAuthorized ? "Allow" : "Deny", 
        event.methodArn, congnitoUser ? {...congnitoUser}: {});
    }

    /**
     * Generate REST API policy.
     *       
     * @param principalId 
     * @param effect 
     * @param resource 
     * @param context 
     * @returns AuthResponse
     */
    generateRESTAPIPolicy(principalId:string, effect:StatementEffect, resource:string, context?: APIGatewayAuthorizerResultContext): AuthResponse {

        const policyDocument: PolicyDocument = {
          Version: '2012-10-17',
          Statement: [
            {
              Action: 'execute-api:Invoke',
              Effect: effect,
              Resource: resource
            }
          ],
        };
      
        // Create the authorizer response
        const authResponse: AuthResponse = {
          principalId: principalId,
          policyDocument: policyDocument,
          context:context
        };
      
        return authResponse;
      }

}