import { 
    Context, AuthResponse, 
    APIGatewayAuthorizerEvent, 
    APIGatewayRequestAuthorizerEventV2, 
    APIGatewaySimpleAuthorizerWithContextResult, 
    APIGatewayAuthorizerResultContext} 
from 'aws-lambda';
import { CognitoUser } from '../cognito/cognito-user';

export type AuthoriationResponse = AuthResponse | APIGatewaySimpleAuthorizerWithContextResult<APIGatewayAuthorizerResultContext>;

// TypeScript types for REST API and HTTP API events
//type RestApiEvent = APIGatewayProxyEvent;
//type HttpApiEvent = APIGatewayProxyEventV2;

// TypeScript types for lambda authorizer REST API and HTTP API events
export type RestApiEvent = APIGatewayAuthorizerEvent;
export type HttpApiEvent = APIGatewayRequestAuthorizerEventV2;

export const AuthorizerTypeRestApi = 'REST_API';
export const AuthorizerTypeHttpApi = 'HTTP_API';

export type AuthorizerType = typeof AuthorizerTypeRestApi | typeof AuthorizerTypeHttpApi;

/**
 * Authorizer interface for REST API and HTTP API.
 */
export interface Authorizer {

    /**
     * Authorize the event and return the authorization response.
     * The method does not throw any exceptions. It returns the response with not authorized if there is an error.
     * @param event 
     * @param context
     * @returns AuthoriationResponse
     */
    authorize(event: RestApiEvent | HttpApiEvent, context: Context): Promise<AuthoriationResponse>;

    /**
     * Resolve the token from the event.
     * @param event 
     * @param context
     * @returns string 
     */
    resolveToken(event: RestApiEvent | HttpApiEvent, context: Context): Promise<string>;

    /**
     * Get the authorization response.
     * @param event 
     * @param context 
     * @param isAuthorized 
     * @param congnitoUser
     * @returns AuthoriationResponse 
     */
    getAuthResponse(event: RestApiEvent | HttpApiEvent, context: Context, isAuthorized:boolean, congnitoUser: CognitoUser): Promise<AuthoriationResponse>;

    /**
     * Get the authorizer type.
     * @returns AuthorizerType
     */
    getAuthorizerType(): AuthorizerType;

}