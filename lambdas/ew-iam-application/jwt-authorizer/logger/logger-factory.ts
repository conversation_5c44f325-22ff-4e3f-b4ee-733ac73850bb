import { Logger, LogLevel } from './logger';
import { ConsoleLogger } from './console-logger';

/**
 * LoggerFactory class to create logger instances
 */
export class LoggerFactory {

    private static instance: LoggerFactory | null = null;

    private logLevel: LogLevel = 'off';
    private loggerClass: string = "ConsoleLogger";
    private logger: Logger;

    private constructor() {
        // Default log level set from environment variable or 'info' if not provided
        const level = process.env.LOG_LEVEL || 'off';
        this.loggerClass = process.env.LOGGER_CLASS || this.loggerClass;
        this.setLogLevel(level as LogLevel);
    }

    public static getInstance(): LoggerFactory {
        if (!LoggerFactory.instance) {
          LoggerFactory.instance = new LoggerFactory();
        }
        return LoggerFactory.instance;
      }

    /**
     * Method to set log level
     * @param level 
     * @returns 
     */
    public setLogLevel(level: LogLevel): LoggerFactory {
        this.logLevel = this.parseLogLevel(level);
        return this;
    }

    /**
     * Method to create logger instance in a singleton way
     * 
     * @returns Logger instance
     */
    public getLogger(): Logger {
        if (this.loggerClass == "ConsoleLogger") {
            if (!this.logger) {
                this.logger = this.createConsoleLogger(this.logLevel);
            }
            return this.logger;
        }
        else {
            if (!this.logger) {
                this.logger = this.createConsoleLogger(this.logLevel);
            }
            return this.logger;
        }
    }

    /* 
    * Create a new ConsoleLogger instance
    */
    public createConsoleLogger(level: LogLevel): Logger {
        return new ConsoleLogger(this.logLevel);
    }

    /**
     * Utility to validate log level
     * @param level 
     * @returns 
     */
    private parseLogLevel(level: string): LogLevel {
        const levels: LogLevel[] = ['debug', 'info', 'warn', 'error', 'off'];
        return levels.includes(level.toLowerCase() as LogLevel) ? (level.toLowerCase() as LogLevel) : 'off';
    }
}
