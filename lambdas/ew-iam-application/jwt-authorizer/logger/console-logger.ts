import { Logger, LogLevel } from "./logger";

/**
 * Console logger implementation
 */
export class ConsoleLogger implements Logger {

    public clazzName = "ConsoleLogger";

    private logLevel: LogLevel;

    constructor(level: LogLevel = 'off') {
        this.logLevel = level;
    }

    private shouldLog(srouce:string, level: LogLevel): boolean {
        const levels: LogLevel[] = ['debug', 'info', 'warn', 'error', 'off'];
        return levels.indexOf(level) >= levels.indexOf(this.logLevel);
    }

    debug(srouce:string, message: string, ...optionalParams: any[]): void {
        if (this.shouldLog(srouce,'debug')) {
            console.debug(`${srouce} - : ${message}`, ...optionalParams);
        }
    }

    info(srouce:string, message: string, ...optionalParams: any[]): void {
        if (this.shouldLog(srouce,'info')) {
            console.info(`${srouce} - : ${message}`, ...optionalParams);
        }
    }

    warn(srouce:string, message: string, ...optionalParams: any[]): void {
        if (this.shouldLog(srouce,'warn')) {
            console.warn(`${srouce} - : ${message}`, ...optionalParams);
        }
    }

    error(srouce:string, message: string, ...optionalParams: any[]): void {
        if (this.shouldLog(srouce,'error')) {
            console.error(`${srouce} - : ${message}`, ...optionalParams);
        }
    }
}