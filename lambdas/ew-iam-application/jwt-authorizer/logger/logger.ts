// Define log levels
export type LogLevel = 'debug' | 'info' | 'warn' | 'error' | 'off';
/**
 * Logger interface
 */
export interface Logger {
    debug(srouce:string, message: string, ...optionalParams: any[]): void;
    info(srouce:string, message: string, ...optionalParams: any[]): void;
    warn(srouce:string, message: string, ...optionalParams: any[]): void;
    error(srouce:string, message: string, ...optionalParams: any[]): void;
  }
  