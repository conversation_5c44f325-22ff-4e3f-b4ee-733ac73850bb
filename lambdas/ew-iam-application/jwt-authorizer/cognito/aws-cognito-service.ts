import * as AWS from 'aws-sdk';
import { CognitoJwtVerifier } from "aws-jwt-verify";
import { CognitoJwtVerifierSingleUserPool } from 'aws-jwt-verify/cognito-verifier';
import { CognitoUser } from './cognito-user';
import { Logger } from '../logger/logger';
import { LoggerFactory } from '../logger/logger-factory';


/**
 * Service class for AWS Cognito operations
 */
export class AwsCognitoService {

    public clazzName = "AwsCognitoService";

    private cognitoClient: AWS.CognitoIdentityServiceProvider;
    private cognitoJwtVerifier;
    private logger:Logger;

    constructor(awsRegion: string, cognitoPoolId: string, cognitoClientIds: string) {
        this.logger = LoggerFactory.getInstance().getLogger();
        this.logger.debug(this.clazzName,`instantaiting AwsCognitoService`);
        const cognitoClientIdArray = this.processENVCognitoCleintIds(cognitoPoolId, cognitoClientIds);
        this.cognitoClient = new AWS.CognitoIdentityServiceProvider({ region: awsRegion });
        this.cognitoJwtVerifier = CognitoJwtVerifier.create({
            userPoolId: cognitoPoolId, // mandatory, can't be overridden upon calling verify
            tokenUse: null, // needs to be specified here or upon calling verify
            clientId: cognitoClientIdArray, // needs to be specified here or upon calling verify
        });
    }

    processENVCognitoCleintIds(cognitoPoolId: string, cognitoClientIds: string) {
        this.validateENVVariables(cognitoPoolId, cognitoClientIds);
        return cognitoClientIds.split(',');
    }

    validateENVVariables(cognitoPoolId: string, cognitoClientIds: string) {
        if (!cognitoPoolId) {
            throw new Error(`COGNITO_POOL_ID environment variable is not defined`);
        }
        if (!cognitoClientIds) {
            throw new Error(`COGNITO_CLIENT_IDS environment variable is not defined`);
        }
    }

    /**
     * 
     * @param token 
     * @returns 
     */
    async verifyToken(token: string) {
        let payload;
        try {
            payload = await this.cognitoJwtVerifier.verify(
                token // the JWT as string
            );
            return payload;
        } catch (e) {
            throw e;
        }
    }

    /**
     * Decode the given token and extract the groups if there is any
     * @param token 
     */
    async resolveUserGroups(token: string) {

        let groups = "";
        let tokenPayload = await this.verifyToken(token);
        this.logger.debug(this.clazzName,`tokenPayload`,JSON.stringify(tokenPayload));
        if (tokenPayload) {
            groups = tokenPayload['cognito:groups'];
        }
        return groups;
    }

    /**
     * Invokes geetUer() method of `CognitoIdentityServiceProvider` with tthe given token.
     * Cognito validates and authorizes the token and returns user details if the token is valid one(not expired).
     *  
     */
    async getUserByAccessToken(token:string):Promise<CognitoUser> {
        let request = {
            AccessToken: token
        }
        let user = await this.cognitoClient.getUser(request).promise();

        this.logger.debug(this.clazzName,`retrieved cognito user`,JSON.stringify(user));

        let username = user.Username;
        let userUuid;
        let userId;
        let email;
        let emailVerified;
        let locale;
        let zoneInfo;

        for (let i = 0; i < user.UserAttributes.length; i++) {
            const userAttr = user.UserAttributes[i];
            if (userAttr.Name == "sub") {
                userUuid = userAttr.Value;
            }
            else if (userAttr.Name == "custom:user_id") {
                userId = userAttr.Value;
            }
            else if (userAttr.Name == "email") {
                email = userAttr.Value;
            }
            else if (userAttr.Name == "email_verified") {
                emailVerified = userAttr.Value;
            }
            else if (userAttr.Name == "zoneinfo") {
                zoneInfo = userAttr.Value;
            }
            else if (userAttr.Name == "locale") {
                locale = userAttr.Value;
            }
        }

        let cognitoUser = new CognitoUser(username, userId, userUuid, email, emailVerified, zoneInfo, locale, "");
        return cognitoUser;
    }

}