/**
 * CognitoUser class represents the user details fetched from Cognito.
 */
export class CognitoUser {

    username: string;
    user_id: string;
    user_uuid: string;
    email: string;
    email_verified: string;
    zoneinfo: string;
    locale: string;
    user_groups: string;

    constructor(
        username: string,
        user_id: string,
        user_uuid: string,
        email: string,
        email_verified: string,
        zoneinfo: string,
        locale: string,
        user_groups: string
    ) {
        this.username = username;
        this.user_id = user_id;
        this.user_uuid = user_uuid;
        this.email = email;
        this.email_verified = email_verified;
        this.zoneinfo = zoneinfo;
        this.locale = locale;
        this.user_groups = user_groups;
    }
}