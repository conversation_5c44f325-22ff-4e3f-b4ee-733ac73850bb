import { Test, TestingModule } from '@nestjs/testing';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { RedisService } from '../src/redis/services/redis.service';
import { RedisModule } from '../src/redis/redis.module';
import { RedisMemoryServer } from 'redis-memory-server';

describe('RedisModule (e2e)', () => {
    let redisServer: RedisMemoryServer;
    let redisService: RedisService;
    let module: TestingModule;
  
    beforeAll(async () => {

        // Start an in-memory Redis server
        // the same port defined in test/.env-redis
        redisServer = new RedisMemoryServer({
            instance: {
                port: 6399,
            },
        });

        await redisServer.start();
        
       module = await Test.createTestingModule({
            imports: [
                ConfigModule.forRoot({
                    isGlobal: true,
                    envFilePath: 'test/.env-redis', // Optionally load a specific .env file for tests
                }),
                RedisModule,
            ],
            providers: [
            ],
            exports: [],
        }).compile();
        module.enableShutdownHooks();
        await module.init();

        redisService = module.get<RedisService>(RedisService);
    });

    afterAll(async () => {
        module?.close();
        let stopped:boolean = false;
        while (!stopped) {
            stopped = await redisServer?.stop();
        }
    });

    afterEach(() => {
        jest.clearAllMocks();
    });

    it('should be defined', () => {
        expect(redisService).toBeDefined();
    });

    it('should call redis.set and then redis.get and return the expected value', async () => {
        const testKey = 'test-key';
        const testValue = 'test-value';
        await redisService.set(testKey, testValue);
        const result = await redisService.get(testKey);
        expect(result).toBe(testValue);
    });

    it('should return null if there is no key', async () => {
        const testKey = 'test-key1';
        const result = await redisService.get(testKey);
        expect(result).toBeNull();
    });
});
