### Redis
REDIS_HOST=127.0.0.1
REDIS_PORT=6399
REDIS_REDIS_USERNAME=
REDIS_REDIS_PASSWORD=
REDIS_CLUSTER_ENABLED=false
REDIS_CONNECT_TIME_OUT=10000
REDIS_COMMAND_TIME_OUT=10000
# only dev purpose when the redis cluster is deployed in docker
REDIS_NAT_MAP=**********:6379:127.0.0.1:6379,**********:6379:127.0.0.1:6380,**********:6379:127.0.0.1:6381,**********:6379:127.0.0.1:6382,**********:6379:127.0.0.1:6383,**********:6379:127.0.0.1:6384
IDEMPOTENCY_CACHE_KEY_PREFIX=ipk