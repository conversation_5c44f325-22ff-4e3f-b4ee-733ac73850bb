import { SwaggerConfigLoader } from "../../src/utils/swagger/swagget-config-loader";

export class TestEnvUtils {

    /**
     * Initialize global environment variables instantiated in SwaggerConfigLoader.initConfigs(); function.
     * These configs need to be read before the nestjs app is initialized.
     * 
     * AWS_APIGW_AUTH_LAMBDA_NAME=ew-iam-app-lambda-auth-jwt-cognito
     * AWS_APIGW_AUTH_LAMBDA_AUTHORIZER_URI=arn:aws:apigateway:eu-central-1:lambda:path/2015-03-31/functions/arn:aws:lambda:eu-central-1:158907794445:function:ew-iam-application-JwtAuthorizerFunction-djwriLMpSAra/invocations
     * AWS_APIGW_AUTH_LAMBDA_AUTHORIZER_TTL_SECONDS=300
     */
    public static initGlobalEnvVariables() {
        process.env.AWS_APIGW_AUTH_LAMBDA_NAME = 'ew-iam-app-lambda-auth-jwt-cognito';
        process.env.AWS_APIGW_AUTH_LAMBDA_AUTHORIZER_TTL_SECONDS = '300';
        process.env.AWS_APIGW_AUTH_LAMBDA_AUTHORIZER_URI = 'arn:aws:apigateway:eu-central-1:lambda:path/2015-03-31/functions/arn:aws:lambda:eu-central-1:158907794445:function:ew-iam-application-JwtAuthorizerFunction-djwriLMpSAra/invocations';
    }

    /**
     * Initialize swagger configs.
     */
    public static initSwaggerConfigs() {
        this.initGlobalEnvVariables();
        SwaggerConfigLoader.initConfigs();
    }
}