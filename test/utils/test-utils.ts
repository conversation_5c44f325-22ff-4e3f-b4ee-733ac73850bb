import { mock } from "ts-mockito";
import * as scylla from "cassandra-driver";
import { MongoMemoryServer } from "mongodb-memory-server";
import RedisMemoryServer from "redis-memory-server";

export class TestUtils {

    public static mockScyllaClient() {
        return {
            onModuleInit: async () => {
            },
            onModuleDestroy: async () => {
            },
            getClient: () => {
                return mock(scylla.Client);
            }
        };
    };

    public static async startInMemoryMongoDB(): Promise<MongoMemoryServer> {
        // Start in-memory MongoDB instance
        const mongoServer = await MongoMemoryServer.create();
        return mongoServer;
    }

    public static async stopInMemoryMongoDB(mongoServer:MongoMemoryServer): Promise<void> {
        try {
            if (mongoServer) {
                let stopped:boolean = await mongoServer.stop();
                while (!stopped) {
                    stopped = await mongoServer.stop();
                }
            }
        } catch (error) {
            console.error(`Error stoping mongodb server: ${error}`);
        }
       
    }

    public static async startInMemoryRedis(port:number = 6399): Promise<RedisMemoryServer> {
        // Start in-memory MongoDB instance
        const redisServer = new RedisMemoryServer({
            instance: {
                port: port,
            },
        });

        await redisServer.start();
        return redisServer;
    }

    public static async stopInMemoryRedis(redisServer:RedisMemoryServer): Promise<void> {
        try {
            if (redisServer) {
                let stopped:boolean = await redisServer.stop();
                while (!stopped) {
                    stopped = await redisServer.stop();
                }
            }
        } catch (error) {
            console.error(`Error stoping redis server: ${error}`);
        }
       
    }
}