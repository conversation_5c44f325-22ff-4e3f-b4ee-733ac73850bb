{
    "version": "0.2.0",
    "configurations": [
        {
            "name": "Debug Eazy Wallet",
            "request": "launch",
            "runtimeArgs": ["run-script", "start:debug"],
            "runtimeExecutable": "npm",
            "skipFiles": ["<node_internals>/**"],
            "type": "node",
            "nodeVersionHint": 20,
            "console": "integratedTerminal",
            "envFile": "${workspaceFolder}/.env"
        },
        {
            "name": "Debug Debug Eazy Wallet E2E Tests",
            "request": "launch",
            "runtimeArgs": ["run-script", "test:e2e"],
            "runtimeExecutable": "npm",
            "skipFiles": ["<node_internals>/**"],
            "type": "node",
            "nodeVersionHint": 20,
            "console": "integratedTerminal"
        },
    ]
}
