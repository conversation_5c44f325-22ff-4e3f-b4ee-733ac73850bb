# syntax=docker/dockerfile:1.7

# Base image
FROM node:20.14-bullseye-slim AS base
WORKDIR /app

# ---------- Builder (dev deps + build) ----------
FROM base AS builder
RUN apt-get update \
 && apt-get install -y --no-install-recommends build-essential python3 \
 && rm -rf /var/lib/apt/lists/*

COPY package*.json ./

# Install with token as a BuildKit secret + cache
RUN --mount=type=secret,id=npm_token \
    --mount=type=cache,target=/root/.npm \
    sh -lc 'printf "@eazy-wallet:registry=https://npm.pkg.github.com/\n//npm.pkg.github.com/:_authToken=%s\n" "$(cat /run/secrets/npm_token)" > .npmrc' \
 && npm ci \
 && rm -f .npmrc

# Copy sources and build
COPY . .
RUN npm run build

# ---------- Prod deps only ----------
FROM base AS prod-deps
COPY package*.json ./
RUN --mount=type=secret,id=npm_token \
    --mount=type=cache,target=/root/.npm \
    sh -lc 'printf "@eazy-wallet:registry=https://npm.pkg.github.com/\n//npm.pkg.github.com/:_authToken=%s\n" "$(cat /run/secrets/npm_token)" > .npmrc' \
 && npm ci --omit=dev \
 && rm -f .npmrc \
 && npm cache clean --force

# ---------- Runtime ----------
FROM node:20.14-bullseye-slim AS production
ENV NODE_ENV=production
WORKDIR /app

RUN apt-get update \
 && apt-get install -y --no-install-recommends ca-certificates \
 && rm -rf /var/lib/apt/lists/*

# bring in prod deps and built app
COPY --from=prod-deps /app/node_modules ./node_modules
COPY --from=builder   /app/package*.json ./
COPY --from=builder   /app/dist ./dist

# non-root runtime
RUN mkdir -p logs && chown -R node:node /app
USER node


# Port
ARG PORT=3002
ENV PORT=${PORT}

EXPOSE ${PORT}

# Start the application
CMD ["npm", "run", "start:prod"]





