name: Build and Publish

on:
  release:
    types: 
      - published
env:
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}
  NPM_TOKEN: ${{ secrets.GITHUB_TOKEN }}
  TAG_NAME: ${{ github.ref_name }} // this includes the refs/tags/ prefix

jobs:

  prebuild:
    runs-on: ubuntu-latest
    if: github.event_name == 'release'
    outputs:
      prefix: ${{ steps.extract_prefix.outputs.prefix }}
    steps:
      - name: Extract prefix from tag
        id: extract_prefix
        run: |
          TAG_NAME=${{ github.ref }}
          TAG_BASENAME=$(basename "$TAG_NAME")
          PREFIX=$(echo $TAG_BASENAME | cut -d'_' -f 1)
          # echo "::set-output name=prefix::$PREFIX"
          echo "prefix=$PREFIX" >> $GITHUB_OUTPUT
      - name: Use the prefix
        run: |
          echo "The prefix is ${{ steps.extract_prefix.outputs.prefix }}"
      - name: Conditional step
        if: steps.extract_prefix.outputs.prefix == 'dev'
        run: |
          echo "Running this because the prefix is dev"
      - name: Conditional step
        if: steps.extract_prefix.outputs.prefix == 'stag'
        run: |
          echo "Running this because the prefix is stag"




  build:  
    needs: prebuild 
    runs-on: ubuntu-latest
    if: github.event_name == 'release'
    strategy:
      matrix:
        node-version: [20.x]
        # See supported Node.js release schedule at https://nodejs.org/en/about/releases/
    outputs:
      prefix: ${{ needs.prebuild.outputs.prefix }}
    steps:
      - uses: actions/checkout@v3

      - name: Use Node.js ${{ matrix.node-version }}
        uses: actions/setup-node@v3
        with:
          node-version: ${{ matrix.node-version }}

      - name: Install dependencies
        run: |
          echo "@eazy-wallet:registry=https://npm.pkg.github.com/" > .npmrc
          echo "registry=https://registry.npmjs.org/" >> .npmrc
          echo "//npm.pkg.github.com/:_authToken=${NPM_TOKEN}" >> .npmrc
          npm install --legacy-peer-deps
          rm -f .npmrc
        env:
          NPM_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      
      - run: npm run build 
      - run: export NODE_OPTIONS=--max-old-space-size=3072
      - run: npm test 
      - run: npm run test:e2e
       
      # Login against a Docker registry except
      - name: Log into registry ${{ env.REGISTRY }}
        #if: github.event_name != 'pull_request'
        uses: docker/login-action@v1
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}
      
      # Extract metadata (tags, labels) for Docker
      # https://github.com/docker/metadata-action
      - name: Extract Docker metadata
        id: meta
        uses: docker/metadata-action@v3
        with:
          images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}

      # Build and push Docker image with Buildx (don't push on PR)
      # https://github.com/docker/build-push-action
      - name: Build and push Docker image
        uses: docker/build-push-action@v2
        with:
          context: .
          push: ${{ github.event_name != 'pull_request' }}
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
          build-args: |
            NPM_TOKEN=${{ secrets.GITHUB_TOKEN }}

      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
            aws-region: eu-central-1
            aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
            aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
      - name: get caller identity
        run: |
            aws sts get-caller-identity
      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v2
      - name: Tag, and push docker image to Amazon ECR
        env:
          AWS_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
          AWS_REPOSITORY: $IMAGE_NAME
          AWS_IMAGE_TAG: ${{ github.sha }}
          GITHUB_REF_NAME: ${{ github.ref_name }}
        run: |
          echo $AWS_REGISTRY
          echo $AWS_REPOSITORY
          echo $AWS_IMAGE_TAG
          echo $IMAGE_NAME
          echo $REGISTRY
          echo $GITHUB_REF_NAME
          docker tag $REGISTRY/$IMAGE_NAME:latest $AWS_REGISTRY/$IMAGE_NAME:$GITHUB_REF_NAME
          docker push $AWS_REGISTRY/$IMAGE_NAME:$GITHUB_REF_NAME


  newrelic:
    needs: build
    runs-on: ubuntu-latest
    name: New Relic
    if: github.event_name == 'release' && needs.build.outputs.prefix == 'stag'
    steps:
      # This step builds a var with the release tag value to use later
      - name: Set Release Version from Tag
        run: echo "RELEASE_VERSION=${{ github.ref_name }}" >> $GITHUB_ENV
      # This step creates a new Change Tracking Marker
      - name: New Relic Application Deployment Marker
        uses: newrelic/deployment-marker-action@v2.3.0
        with:
          apiKey: ${{ secrets.STAGING_NEW_RELIC_API_KEY }}
          guid: ${{ secrets.STAGING_NEW_RELIC_DEPLOYMENT_ENTITY_GUID }}
          version: "${{ env.RELEASE_VERSION }}"
          user: "${{ github.actor }}"
          region: EU

  deploy2staging:
    needs: build
    name: deploy
    runs-on: ubuntu-latest
    if: github.event_name == 'release' && needs.build.outputs.prefix == 'stag'
    outputs:
      prefix: ${{ needs.build.outputs.prefix }}
    steps:       
      - name: Info about the release
        env:
          TAG_NAME: ${{ github.ref }} 
        run: |
          TAG_BASENAME=$(basename "$TAG_NAME")
          echo "Deploying to production with tag $TAG_BASENAME"
          # Your deployment script or command here

      - name: executing remote ssh commands using ssh key
        uses: appleboy/ssh-action@v1.0.0
        env:
          # ! DEVELOPERS: Please add your new secrets here and in the repo settings
          ENV_CONTENT: ${{ vars.ENV_CONTENT }}
          REPONAME: ${{ github.event.repository.name}}
          STAGING_MYSQLDB_ROOT_PASSWORD: ${{ secrets.STAGING_MYSQLDB_ROOT_PASSWORD }}
          STAGING_MYSQLDB_EW_PASSWORD: ${{ secrets.STAGING_MYSQLDB_EW_PASSWORD }}
          STAGING_MYSQLDB_EW_USER: ${{ secrets.STAGING_MYSQLDB_EW_USER }}
          GITHUB_REF_NAME: ${{ github.ref_name }}
          STAGING_NEW_RELIC_LICENSE_KEY: ${{secrets.STAGING_NEW_RELIC_LICENSE_KEY}}
          AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
          AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}


        with:
          host: ${{ vars.STAGING_HOST }}
          username: ${{ secrets.STAGING_SSH_USERNAME }}
          key: ${{ secrets.STAGING_SSH_KEY }}
          proxy_host: ${{ vars.BASTION_HOST }}
          proxy_username: ${{ secrets.BASTION_SSH_USERNAME }}
          proxy_key: ${{ secrets.STAGING_SSH_KEY }}
          # ! DEVELOPERS: Please add your new secret name you added here as well
          envs: REPONAME, ENV_CONTENT, STAGING_MYSQLDB_ROOT_PASSWORD, 
            STAGING_MYSQLDB_EW_PASSWORD, STAGING_MYSQLDB_EW_USER, 
            GITHUB_REF_NAME, IMAGE_NAME,REGISTRY, STAGING_NEW_RELIC_LICENSE_KEY,
            AWS_SECRET_ACCESS_KEY, AWS_ACCESS_KEY_ID
          script: |
          
            ls -all /data
            echo "--------INFO---------"
            echo "Reponame: $REPONAME"
            echo "Repo URL: $REGISTRY/$IMAGE_NAME:$GITHUB_REF_NAME"
            echo "$ENV_CONTENT" > /data/eazy-wallet-services-docker-compose/$REPONAME/.env
            echo "---------------------"


            # ! DEVELOPERS: Please add your new secrets here as you will consume them in the .env file
            echo "MYSQLDB_ROOT_PASSWORD=$STAGING_MYSQLDB_ROOT_PASSWORD" >> /data/eazy-wallet-services-docker-compose/$REPONAME/.env
            echo "MYSQLDB_EW_PASSWORD=$STAGING_MYSQLDB_EW_PASSWORD" >> /data/eazy-wallet-services-docker-compose/$REPONAME/.env
            echo "MYSQLDB_EW_USER=$STAGING_MYSQLDB_EW_USER" >> /data/eazy-wallet-services-docker-compose/$REPONAME/.env
            echo "AWS_ACCESS_KEY_ID=$AWS_ACCESS_KEY_ID" >> /data/eazy-wallet-services-docker-compose/$REPONAME/.env
            echo "AWS_SECRET_ACCESS_KEY=$AWS_SECRET_ACCESS_KEY" >> /data/eazy-wallet-services-docker-compose/$REPONAME/.env
            echo "NEW_RELIC_LICENSE_KEY=$STAGING_NEW_RELIC_LICENSE_KEY" >> /data/eazy-wallet-services-docker-compose/$REPONAME/.env
            echo "RELEASE_VERSION=$GITHUB_REF_NAME" >> /data/eazy-wallet-services-docker-compose/$REPONAME/.env

            # Our compose file is already in this folder
            COMPOSE_FILE=/data/eazy-wallet-services-docker-compose/$REPONAME/docker-compose.yaml


            #Update the image tag for the service in compose file
            sed -i "/^  $REPONAME:/,/^$/s|image: ghcr.io/eazy-wallet-/$REPONAME:.*|image: ghcr.io/eazy-wallet-/$REPONAME:$GITHUB_REF_NAME|" $COMPOSE_FILE


            # Timestamping the new config name as it is required by the docker stack deploy command
            TIMESTAMP=$(date +%s)            
            sed -i "/name: $REPONAME-config-/s/-config-[0-9]\+/-config-$TIMESTAMP/" $COMPOSE_FILE


           
            docker pull $REGISTRY/$IMAGE_NAME:$GITHUB_REF_NAME
            docker stack deploy eazy-wallet-services --compose-file $COMPOSE_FILE --with-registry-auth


  deploy2prod:
    needs: build
    name: Deploy to Production
    runs-on: ubuntu-latest
    if: github.event_name == 'release' && needs.build.outputs.prefix == 'prod'
    steps:
      - name: Info about the release
        env:
          TAG_NAME: ${{ github.ref }}
        run: |
          TAG_BASENAME=$(basename "$TAG_NAME")
          echo "Deploying to production with tag $TAG_BASENAME"

      - name: Get Variable ID from Terraform Cloud
        id: get-variable-id
        run: |
          VAR_NAME="${{ github.event.repository.name }}_image_version"
          WORKSPACE_ID="${{ vars.TF_WORKSPACE_ID }}"
          
          # Get the list of variables in the workspace
          VAR_ID=$(curl \
            --header "Authorization: Bearer ${{ secrets.TF_API_TOKEN }}" \
            --header "Content-Type: application/vnd.api+json" \
            --request GET \
            https://app.terraform.io/api/v2/workspaces/$WORKSPACE_ID/vars | \
            jq -r --arg VAR_NAME "$VAR_NAME" '.data[] | select(.attributes.key == $VAR_NAME) | .id')
          
          echo "Variable ID: $VAR_ID"
          echo "::set-output name=variable_id::$VAR_ID"

      - name: Update Variable in Terraform Cloud
        env:
          TAG_BASENAME: ${{ github.ref_name }}
          VAR_ID: ${{ steps.get-variable-id.outputs.variable_id }}
          WORKSPACE_ID: ${{ vars.TF_WORKSPACE_ID }}
        run: |
          curl \
            --header "Authorization: Bearer ${{ secrets.TF_API_TOKEN }}" \
            --header "Content-Type: application/vnd.api+json" \
            --request PATCH \
            --data '{
              "data": {
                "id":"${{ steps.get-variable-id.outputs.variable_id }}",
                "type": "vars",
                "attributes": {
                  "key": "'${{ github.event.repository.name }}_image_version'",
                  "value": "'${TAG_BASENAME}'",
                  "category": "terraform",
                  "hcl": false,
                  "sensitive": false
                }
              }
            }' \
            https://app.terraform.io/api/v2/workspaces/$WORKSPACE_ID/vars/${{ steps.get-variable-id.outputs.variable_id }}

      - name: Trigger Terraform Run
        run: |
          curl \
            --header "Authorization: Bearer ${{ secrets.TF_API_TOKEN }}" \
            --header "Content-Type: application/vnd.api+json" \
            --request POST \
            --data '{
              "data": {
                "attributes": {
                  "is-destroy": false,
                  "message": "GitHub Prod Deployment: ${{ github.event.repository.name }}:${{ github.ref_name }}"
                },
                "type": "runs",
                "relationships": {
                  "workspace": {
                    "data": {
                      "type": "workspaces",
                      "id": "${{ vars.TF_WORKSPACE_ID }}"
                    }
                  }
                }
              }
            }' \
            https://app.terraform.io/api/v2/runs
        
          
          
