name: PR Build

on:
  pull_request:
    types:
      - opened
env:
  # Use docker.io for Docker Hub if empty
  REGISTRY: ghcr.io
  # github.repository as <account>/<repo>
  NPM_TOKEN: ${{ secrets.GITHUB_TOKEN }}

jobs:
  build:

    runs-on: ubuntu-latest

    strategy:
      matrix:
        node-version: [20.x]
        # See supported Node.js release schedule at https://nodejs.org/en/about/releases/

    steps:
      - uses: actions/checkout@v3
      - name: Use Node.js ${{ matrix.node-version }}
        uses: actions/setup-node@v3
        with:
          node-version: ${{ matrix.node-version }}

      #- Till we add some build time test, the step below is disabled
      - name: Install dependencies
        run: |
          echo "@eazy-wallet:registry=https://npm.pkg.github.com/" > .npmrc
          echo "registry=https://registry.npmjs.org/" >> .npmrc
          echo "//npm.pkg.github.com/:_authToken=${NPM_TOKEN}" >> .npmrc
          npm install --legacy-peer-deps
          rm -f .npmrc
        env:
          NPM_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      
      - run: npm run build
      - run: export NODE_OPTIONS=--max-old-space-size=3072
      - run: npm run test
      - run: npm run test:e2e