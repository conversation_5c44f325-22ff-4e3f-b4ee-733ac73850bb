name: Build and Publish

on:
  push:
    branches: [ main, dev ]

permissions:
  contents: read
  packages: write

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}

jobs:
  build_and_test:
    runs-on: ubuntu-latest
    strategy:
      matrix: { node-version: [20.x] }
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
        with:
          node-version: ${{ matrix.node-version }}
      - name: Install dependencies
        run: |
          echo "@eazy-wallet:registry=https://npm.pkg.github.com/" > .npmrc
          echo "registry=https://registry.npmjs.org/" >> .npmrc
          echo "//npm.pkg.github.com/:_authToken=${NPM_TOKEN}" >> .npmrc
          npm install --legacy-peer-deps
          rm -f .npmrc
        env:
          NPM_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      - run: npm run build
      # - run: npm test
      # - run: npm run test:e2e

  docker_build_and_publish:
    needs: [build_and_test]
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4

      - name: Log into registry ${{ env.REGISTRY }}
        uses: docker/login-action@v3
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}  # or secrets.CR_PAT

      - name: Extract Docker metadata
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}

      - name: Build and push Docker image
        uses: docker/build-push-action@v6
        with:
          context: .
          push: ${{ github.event_name != 'pull_request' }}
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
          # Prefer BuildKit secrets over ARG for tokens (see notes below)
          secrets: |
            npm_token=${{ secrets.GITHUB_TOKEN }}  # or a PAT with write:packages if org policy requires
